/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.common.constant;

/**
 * UI 相关常量
 *
 * <AUTHOR>
 * @since 2023/9/17 14:12
 */
public class UiConstants {

    /**
     * 主色（极致蓝）
     */
    public static final String COLOR_PRIMARY = "arcoblue";

    /**
     * 成功色（仙野绿）
     */
    public static final String COLOR_SUCCESS = "green";

    /**
     * 警告色（活力橙）
     */
    public static final String COLOR_WARNING = "orangered";

    /**
     * 错误色（浪漫红）
     */
    public static final String COLOR_ERROR = "red";

    /**
     * 默认色（中性灰）
     */
    public static final String COLOR_DEFAULT = "gray";

    private UiConstants() {
    }
}