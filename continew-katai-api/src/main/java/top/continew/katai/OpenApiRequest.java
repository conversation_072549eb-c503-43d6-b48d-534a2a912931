/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

public class OpenApiRequest {

    public Map<String, String> headers;
    public Map<String, String> query;
    public Object body;
    public InputStream stream;

    public OpenApiRequest() {
    }

    public OpenApiRequest setHeaders(Map<String, String> headers) {
        this.headers = headers;
        return this;
    }

    public Map<String, String> getHeaders() {
        return this.headers;
    }

    public OpenApiRequest setQuery(Map<String, String> query) {
        this.query = query;
        return this;
    }

    public Map<String, String> getQuery() {
        return this.query;
    }

    public OpenApiRequest setBody(Object body) {
        this.body = body;
        return this;
    }

    public Object getBody() {
        return this.body;
    }

    public OpenApiRequest setStream(InputStream stream) {
        this.stream = stream;
        return this;
    }

    public InputStream getStream() {
        return this.stream;
    }

    public OpenApiRequest addHeader(String key, String value) {
        if (this.headers == null) {
            this.headers = new HashMap<>();
        }
        this.headers.put(key, value);
        return this;
    }

    public OpenApiRequest addQuery(String key, String value) {
        if (this.query == null) {
            this.query = new HashMap<>();
        }
        this.query.put(key, value);
        return this;
    }
}
