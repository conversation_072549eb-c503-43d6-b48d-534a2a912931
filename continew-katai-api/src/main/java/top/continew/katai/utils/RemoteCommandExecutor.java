package top.continew.katai.utils;

import com.jcraft.jsch.*;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 远程命令执行工具类
 */
@Slf4j
public class RemoteCommandExecutor {

    @Data
    @Builder
    public static class SSHConfig {
        private String host;
        private int port = 22;
        private String username;
        private String password;
        private String privateKeyPath;
        private int timeout;

        public static SSHConfig defaultConfig() {
            return SSHConfig.builder()
                    .port(22)
                    .timeout(30000)
                    .build();
        }
    }

    public static String executeCommand(SSHConfig config, String command) {
        Session session = null;
        ChannelExec channel = null;
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(config.getUsername(), config.getHost(), config.getPort());

            if (config.getPassword() != null) {
                session.setPassword(config.getPassword());
            }

            Properties properties = new Properties();
            properties.put("StrictHostKeyChecking", "no");
            properties.put("PreferredAuthentications", "password,publickey");
            properties.put("UseDNS", "no");
            properties.put("TCPKeepAlive", "yes");
            properties.put("ServerAliveInterval", "60");
            properties.put("ServerAliveCountMax", "3");

            session.setConfig(properties);
            session.setTimeout(config.getTimeout());

            log.info("正在连接到远程服务器: {}:{}", config.getHost(), config.getPort());
            session.connect();
            log.info("服务器连接成功");

            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);

            ByteArrayOutputStream responseStream = new ByteArrayOutputStream();
            InputStream in = channel.getInputStream();

            channel.connect();

            byte[] tmp = new byte[1024];
            StringBuilder response = new StringBuilder();

            while (true) {
                while (in.available() > 0) {
                    int i = in.read(tmp, 0, 1024);
                    if (i < 0) break;
                    String output = new String(tmp, 0, i);
                    response.append(output);
                    log.info("命令执行输出: {}", output);
                }

                if (channel.isClosed()) {
                    int exitStatus = channel.getExitStatus();
                    log.info("命令执行完成，退出状态: {}", exitStatus);
                    if (exitStatus != 0) {
                        throw new RuntimeException("远程命令执行失败，退出状态: " + exitStatus);
                    }
                    break;
                }

                try {
                    Thread.sleep(100);
                } catch (Exception e) {
                    // 忽略中断异常
                }
            }

            return response.toString();

        } catch (Exception e) {
            log.error("远程命令执行异常", e);
            throw new RuntimeException("远程命令执行异常: " + e.getMessage(), e);
        } finally {
            if (channel != null) {
                channel.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }
}