/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.utils;

import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.AESDecrypter;
import com.nimbusds.jose.crypto.RSADecrypter;
import com.nimbusds.jose.crypto.RSAEncrypter;

import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.text.ParseException;
import java.util.Base64;
import java.util.Date;

public class JWEUtilsUsingRSAPKI {

    /**
     * Create JWE using RSA Public Key
     *
     * @param data      - Plain Text
     * @param publicKey - RSA Public Key
     * @param kid       - Key UserId
     * @return JWE String in compact serialization format * @throws Exception
     */
    public static String createJwe(String data, String publicKey, String kid) throws Exception {
        RSAPublicKey rsaPubKey = loadPublicKeyFromString(publicKey);
        long currentTime = (new Date()).getTime() / 1000L;
        JWEHeader updatedHeader = (new JWEHeader.Builder(JWEAlgorithm.RSA_OAEP_256, EncryptionMethod.A128GCM))
            .keyID(kid)
            .type(JOSEObjectType.JOSE)
            .customParam("iat", currentTime)
            .build();
        JWEObject jweObject = new JWEObject(updatedHeader, new Payload(data));
        RSAEncrypter encrypter = new RSAEncrypter(rsaPubKey);
        jweObject.encrypt(encrypter);
        return jweObject.serialize();
    }

    /**
     * Decrypt JWE Using RSA PKI
     *
     * @param jwe        - JWE String in compact serialization format
     * @param privateKey - RSA Private Key in PEM Format * @return Plain Text
     * @throws GeneralSecurityException
     * @throws ParseException
     */
    public static final String decryptJwe(String jwe,
                                          String privateKey) throws GeneralSecurityException, ParseException {
        byte[] encodedPrivateKey = Base64.getDecoder().decode(privateKey);
        String plainText;
        JWEObject jweObject = JWEObject.parse(jwe);
        JWEHeader header = jweObject.getHeader();
        JWEAlgorithm jweAlgorithm = header.getAlgorithm();
        try {
            if (JWEAlgorithm.RSA1_5.equals(jweAlgorithm) || JWEAlgorithm.RSA_OAEP_256.equals(jweAlgorithm)) {
                RSAPrivateKey rsaPrivateKey = (RSAPrivateKey)KeyFactory.getInstance("RSA")
                    .generatePrivate(new PKCS8EncodedKeySpec(encodedPrivateKey));
                RSADecrypter decrypter = new RSADecrypter(rsaPrivateKey);
                jweObject.decrypt(decrypter);
                plainText = jweObject.getPayload().toString();
            } else {
                JWEDecrypter decrypterForAES = new AESDecrypter(encodedPrivateKey);
                jweObject.decrypt(decrypterForAES);
                plainText = jweObject.getPayload().toString();
            }
        } catch (JOSEException e) {
            throw new GeneralSecurityException("JOSEException has encountered.", e);
        }
        return plainText;
    }

    /**
     * Load RSA Public Key From File (PEM Format) *
     *
     * @param publicKey - Public Key File
     * @return {@link RSAPublicKey}
     * @throws Exception
     */
    public static RSAPublicKey loadPublicKeyFromString(String publicKey) throws Exception {
        byte[] encoded = Base64.getDecoder().decode(publicKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encoded);
        return (RSAPublicKey)keyFactory.generatePublic(keySpec);
    }
}
