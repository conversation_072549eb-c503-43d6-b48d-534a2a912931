/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.utils;

import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.ObjectUtils;
import top.continew.katai.DaziRequest;
import top.continew.katai.DaziResponse;
import top.continew.katai.RuntimeOptions;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.okhttp.ClientHelper;
import top.continew.katai.okhttp.OkRequestBuilder;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class Third {

    public Third() {
    }

    private static String composeUrl(DaziRequest request) {
        Map<String, String> queries = request.query;
        String host = request.headers.get("host");
        String protocol = null == request.protocol ? "http" : request.protocol;
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(protocol);
        urlBuilder.append("://").append(host);
        if (null != request.pathname) {
            urlBuilder.append(request.pathname);
        }

        if (queries != null && !queries.isEmpty()) {
            if (urlBuilder.indexOf("?") >= 1) {
                urlBuilder.append("&");
            } else {
                urlBuilder.append("?");
            }

            try {

                for (Map.Entry<String, String> stringStringEntry : queries.entrySet()) {
                    String key = stringStringEntry.getKey();
                    String val = stringStringEntry.getValue();
                    if (val != null && !"null".equals(val)) {
                        urlBuilder.append(URLEncoder.encode(key, StandardCharsets.UTF_8));
                        urlBuilder.append("=");
                        urlBuilder.append(URLEncoder.encode(val, StandardCharsets.UTF_8));
                        urlBuilder.append("&");
                    }
                }
            } catch (Exception e) {
                throw new ThirdException(e.getMessage(), e);
            }

            int strIndex = urlBuilder.length();
            urlBuilder.deleteCharAt(strIndex - 1);
        }

        return urlBuilder.toString();
    }

    public static DaziResponse doAction(DaziRequest request) {
        return doAction(request, new RuntimeOptions());
    }

    public static DaziResponse doAction(DaziRequest request, RuntimeOptions runtimeOptions) {
        try {
            String urlString = composeUrl(request);
            URL url = new URL(urlString);
            OkHttpClient okHttpClient = ClientHelper.getOkHttpClient(url.getHost(), url.getPort(), runtimeOptions);
            Request.Builder requestBuilder = new Request.Builder();
            OkRequestBuilder okRequestBuilder = (new OkRequestBuilder(requestBuilder)).url(url).header(request.headers);
            Response response = okHttpClient.newCall(okRequestBuilder.buildRequest(request)).execute();
            return new DaziResponse(response);
        } catch (Exception e) {
            throw new ThirdException(e.getMessage(), e);
        }
    }

    private static Map<String, String> setProxyAuthorization(Map<String, String> header, Object httpsProxy) {
        try {
            if (!ObjectUtils.isEmpty(httpsProxy)) {
                URL proxyUrl = new URL(String.valueOf(httpsProxy));
                String userInfo = proxyUrl.getUserInfo();
                if (null != userInfo) {
                    String[] userMessage = userInfo.split(":");
                    String credential = Credentials.basic(userMessage[0], userMessage[1]);
                    header.put("Proxy-Authorization", credential);
                }
            }

            return header;
        } catch (Exception e) {
            throw new ThirdException(e.getMessage(), e);
        }
    }

    public static boolean allowRetry(Map<String, ?> map, int retryTimes, long now) {
        if (0 == retryTimes) {
            return true;
        } else if (map == null) {
            return false;
        } else {
            Object shouldRetry = map.get("retryable");
            if (shouldRetry instanceof Boolean && (Boolean)shouldRetry) {
                int retry = map.get("maxAttempts") == null
                    ? 0
                    : Integer.parseInt(String.valueOf(map.get("maxAttempts")));
                return retry >= retryTimes;
            } else {
                return false;
            }
        }
    }

    public static int getBackoffTime(Object o, int retryTimes) {
        int backOffTime = 0;
        Map<String, Object> map = (Map)o;
        if (!ObjectUtils.isEmpty(map.get("policy")) && !"no".equals(map.get("policy"))) {
            return !ObjectUtils.isEmpty(map.get("period")) && (backOffTime = Integer.parseInt(String.valueOf(map
                .get("period")))) <= 0 ? retryTimes : backOffTime;
        } else {
            return backOffTime;
        }
    }

    public static void sleep(int time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            throw new ThirdException(e.getMessage(), e);
        }
    }

    public static InputStream toReadable(String string) {
        return toReadable(string.getBytes(StandardCharsets.UTF_8));
    }

    public static InputStream toReadable(byte[] byteArray) {
        return new ByteArrayInputStream(byteArray);
    }

    public static OutputStream toWriteable(int size) {
        try {
            return new ByteArrayOutputStream(size);
        } catch (IllegalArgumentException e) {
            throw new ThirdException(e.getMessage(), e);
        }
    }

    public static OutputStream toWriteable() {
        return new ByteArrayOutputStream();
    }
}
