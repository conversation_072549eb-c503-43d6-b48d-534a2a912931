/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import top.continew.katai.exception.ThirdUtilException;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

public class Common {

    public Common() {
    }

    public static <T> T toModel(Map<String, ?> map, Class<T> s) {
        return JSONObject.parseObject(JSON.toJSONString(map), s);
    }

    public static <T> List<T> toModelList(Map<String,?> map, String mapKey, Class<T> s) {
        Object value = map.get(mapKey);
        if (value == null) {
            return new ArrayList<>();
        }

        // 如果 value 是字符串类型，直接解析
        if (value instanceof String) {
            return JSONArray.parseArray((String) value, s);
        }

        // 如果 value 是集合或对象类型，先序列化为 JSON 字符串，再解析
        String jsonString = JSON.toJSONString(value);
        return JSONArray.parseArray(jsonString, s);
    }

    public static <T> T toModel(Map<String, ?> map, String mapKey, Class<T> s) {
        Object value = map.get(mapKey);
        if (value == null) {
            return null;
        }

        // 如果 value 是字符串类型，直接解析
        if (value instanceof String) {
            return JSONObject.parseObject((String) value, s);
        }

        return JSONObject.parseObject(JSON.toJSONString(value), s);
    }

    public static Map<String, String> toMap(Object object) {
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(object));
        Map<String, String> result = new HashMap<>();
        for (String s : jsonObject.keySet()) {
            result.put(s, jsonObject.getString(s));
        }
        return result;
    }

    public static byte[] toBytes(String str) {
        return str.getBytes(StandardCharsets.UTF_8);
    }

    public static String toString(byte[] bytes) {
        return new String(bytes, StandardCharsets.UTF_8);
    }

    public static Map<String, Object> assertAsMap(Object object) {
        if (null != object && Map.class.isAssignableFrom(object.getClass())) {
            return (Map)object;
        } else {
            throw new ThirdUtilException("The value is not a object");
        }
    }

    public static List<Object> assertAsArray(Object object) {
        if (null != object && List.class.isAssignableFrom(object.getClass())) {
            return (List)object;
        } else {
            throw new ThirdUtilException("The value is not a array");
        }
    }

    public static InputStream assertAsReadable(Object value) {
        if (null != value && InputStream.class.isAssignableFrom(value.getClass())) {
            return (InputStream)value;
        } else {
            throw new ThirdUtilException("The value is not a readable");
        }
    }

    public static byte[] assertAsBytes(Object object) {
        if (object instanceof byte[]) {
            return (byte[])((byte[])object);
        } else {
            throw new ThirdUtilException("The value is not a byteArray");
        }
    }

    public static Number assertAsNumber(Object object) {
        if (object instanceof Number) {
            return (Number)object;
        } else {
            throw new ThirdUtilException("The value is not a Number");
        }
    }

    public static String assertAsString(Object object) {
        if (object instanceof String) {
            return (String)object;
        } else {
            throw new ThirdUtilException("The value is not a String");
        }
    }

    public static Boolean assertAsBoolean(Object object) {
        try {
            return (Boolean)object;
        } catch (Exception var2) {
            throw new ThirdUtilException("The value is not a Boolean");
        }
    }

    public static byte[] readAsBytes(InputStream stream) {
        byte[] var16;
        try {
            if (null != stream) {
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                byte[] buff = new byte[1024];

                while (true) {
                    int read = stream.read(buff);
                    if (read == -1) {
                        byte[] var4 = os.toByteArray();
                        return var4;
                    }

                    os.write(buff, 0, read);
                }
            }

            var16 = new byte[0];
        } catch (Exception var14) {
            Exception e = var14;
            throw new ThirdUtilException(e.getMessage(), e);
        } finally {
            if (null != stream) {
                try {
                    stream.close();
                } catch (IOException var13) {
                    IOException e = var13;
                    throw new ThirdUtilException(e.getMessage(), e);
                }
            }

        }

        return var16;
    }

    public static String readAsString(InputStream stream) {
        return new String(readAsBytes(stream), StandardCharsets.UTF_8);
    }

    public static Object readAsJSON(InputStream stream) {
        return readAsJSON(readAsString(stream));
    }

    public static Object readAsJSON(String string) {
        try {
            return JSON.parse(string);
        } catch (Exception var3) {
            throw new ThirdUtilException("Error: convert to JSON, response is:\n" + string);
        }
    }

    public static String getNonce() {
        StringBuilder uniqueNonce = new StringBuilder();
        UUID uuid = UUID.randomUUID();
        uniqueNonce.append(uuid.toString());
        uniqueNonce.append(System.currentTimeMillis());
        uniqueNonce.append(Thread.currentThread().getId());
        return uniqueNonce.toString();
    }

    public static String getDateUTCString() {
        SimpleDateFormat df = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        df.setTimeZone(new SimpleTimeZone(0, "GMT"));
        return df.format(new Date());
    }

    public static String defaultString(String str, String defaultStr) {
        return !StringUtils.isEmpty(str) ? str : defaultStr;
    }

    public static Number defaultNumber(Number number, Number defaultNumber) {
        return number != null && number.doubleValue() >= 0.0 ? number : defaultNumber;
    }

    public static String toFormString(Map<String, ?> map) {
        if (null == map) {
            return "";
        } else {
            StringBuilder result = new StringBuilder();
            boolean first = true;

            try {

                for (Map.Entry<String, ?> stringEntry : map.entrySet()) {
                    if (!ObjectUtils.isEmpty(stringEntry.getValue())) {
                        if (first) {
                            first = false;
                        } else {
                            result.append("&");
                        }

                        result.append(URLEncoder.encode(stringEntry.getKey(), StandardCharsets.UTF_8));
                        result.append("=");
                        result.append(URLEncoder.encode(String.valueOf(stringEntry
                            .getValue()), StandardCharsets.UTF_8));
                    }
                }
            } catch (Exception var5) {
                Exception e = var5;
                throw new ThirdUtilException(e.getMessage(), e);
            }

            return result.toString();
        }
    }

    public static String toJSONString(Object object) {
        return object instanceof String ? (String)object : JSON.toJSONString(object);
    }

    public static boolean empty(String str) {
        return StringUtils.isEmpty(str);
    }

    public static boolean equalString(String str, String val) {
        return str != null && str.equals(val);
    }

    public static boolean equalNumber(Number num, Number val) {
        if (num != null && val != null) {
            return num.doubleValue() == val.doubleValue();
        } else {
            return false;
        }
    }

    public static boolean isUnset(Object object) {
        return null == object;
    }

    public static Map<String, String> stringifyMapValue(Map<String, ?> map) {
        Map<String, String> result = new HashMap<>();
        if (null == map) {
            return null;
        } else {

            for (Map.Entry<String, ?> stringEntry : map.entrySet()) {
                if (null != stringEntry.getValue()) {
                    result.put(stringEntry.getKey(), String.valueOf(stringEntry.getValue()));
                }
            }

            return result;
        }
    }

    public static Map<String, Object> anyifyMapValue(Map<String, ?> map) {
        Map<String, Object> result = new HashMap<>();
        if (null == map) {
            return null;
        } else {

            for (Map.Entry<String, ?> stringEntry : map.entrySet()) {
                result.put(stringEntry.getKey(), stringEntry.getValue());
            }

            return result;
        }
    }

    public static boolean is2xx(Number code) {
        if (null == code) {
            return false;
        } else {
            return code.intValue() >= 200 && code.intValue() < 300;
        }
    }

    public static boolean is3xx(Number code) {
        if (null == code) {
            return false;
        } else {
            return code.intValue() >= 300 && code.intValue() < 400;
        }
    }

    public static boolean is4xx(Number code) {
        if (null == code) {
            return false;
        } else {
            return code.intValue() >= 400 && code.intValue() < 500;
        }
    }

    public static boolean is5xx(Number code) {
        if (null == code) {
            return false;
        } else {
            return code.intValue() >= 500 && code.intValue() < 600;
        }
    }

    public static void sleep(int millisecond) {
        try {
            Thread.sleep(millisecond);
        } catch (InterruptedException e) {
            throw new ThirdUtilException(e.getMessage(), e);
        }
    }
}
