/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.huitong.HuiTongConfig;
import top.continew.katai.huitong.model.HTPaginationRes;
import top.continew.katai.huitong.model.card.*;
import top.continew.katai.huitong.model.customer.HTBalanceRecord;
import top.continew.katai.huitong.model.customer.HTCustomerInfo;
import top.continew.katai.huitong.model.customer.HTGetBalanceRecordRequest;
import top.continew.katai.huitong.model.task.HTGetTasksRequest;
import top.continew.katai.huitong.model.task.HTTask;
import top.continew.katai.huitong.model.transaction.HTGetTransactionsRequest;
import top.continew.katai.huitong.model.transaction.HTTransaction;
import top.continew.katai.utils.Common;
import top.continew.katai.utils.JWEUtilsUsingRSAPKI;

import java.security.GeneralSecurityException;
import java.text.ParseException;
import java.util.Map;

/**
 * 汇通API 对接文档
 *
 * 链接: <a href="https://ht-api.apifox.cn">...</a> 访问密码: GmWJb0kS
 */
public class HuiTongClient extends Client {

    private final String customerToken;

    private final String secret;

    private final String customerPrivateKey;

    private final String serverPublicKey;

    public HuiTongClient(HuiTongConfig config) {
        super(config);
        this.customerToken = config.getCustomerToken();
        this.secret = config.getSecret();
        this.customerPrivateKey = config.getCustomerPrivateKey();
        this.serverPublicKey = config.getServerPublicKey();
    }

    /**
     * 获取客户信息
     *
     * @return
     */
    public CommonResponse<HTCustomerInfo> getCustomerInfo() {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest();
        Params params = new Params().setProtocol(config.protocol).setMethod("GET").setPathname("/vcc-api/api/vcc/v1/");
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 获取余额明细列表
     *
     * @param request
     * @return
     */
    public CommonResponse<HTPaginationRes<HTBalanceRecord>> getBalanceRecords(HTGetBalanceRecordRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest().setQuery(Common.toMap(request));
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("GET")
            .setPathname("/vcc-api/api/vcc/v1/balance/record");
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 获取卡片列表
     *
     * @param request
     * @return
     */
    public CommonResponse<HTPaginationRes<HTCard>> getCards(HTGetCardPageRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest().setQuery(Common.toMap(request));
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("GET")
            .setPathname("/vcc-api/api/vcc/v1/card");
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 开卡
     *
     * @param request
     * @return
     */
    public CommonResponse<HTTaskIdResponseBody> openCard(HTOpenCardRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest().setBody(Common.toMap(request));
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("POST")
            .setPathname("/vcc-api/api/vcc/v1/card");
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 获取卡片详情
     *
     * @param id
     * @return
     */
    public CommonResponse<HTCard> getCardDetail(String id) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest();
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("GET")
            .setPathname("/vcc-api/api/vcc/v1/card/%s".formatted(id));
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 解锁卡片
     *
     * @param id
     * @return
     */
    public CommonResponse<HTTaskIdResponseBody> unlockCard(String id) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest();
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("POST")
            .setPathname("/vcc-api/api/vcc/v1/card/unlock/%s".formatted(id));
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 锁定卡片
     *
     * @param id
     * @return
     */
    public CommonResponse<HTTaskIdResponseBody> lockCard(String id) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest();
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("POST")
            .setPathname("/vcc-api/api/vcc/v1/card/lock/%s".formatted(id));
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 注销卡片
     *
     * @param id
     * @return
     */
    public CommonResponse<HTTaskIdResponseBody> deleteCard(String id) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest();
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("DELETE")
            .setPathname("/vcc-api/api/vcc/v1/card/%s".formatted(id));
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 充值卡片
     *
     * @param id
     * @param request
     * @return
     */
    public CommonResponse<HTTaskIdResponseBody> rechargeCard(String id, HTRechargeCardRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest().setBody(request);
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("POST")
            .setPathname("/vcc-api/api/vcc/v1/card/recharge/%s".formatted(id));
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 提现卡片
     *
     * @param id
     * @param request
     * @return
     */
    public CommonResponse<HTTaskIdResponseBody> withdrawCard(String id, HTWithdrawCardRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest().setBody(request);
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("POST")
            .setPathname("/vcc-api/api/vcc/v1/card/withdraw/%s".formatted(id));
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 获取卡片交易流水
     *
     * @param request
     * @return
     */
    public CommonResponse<HTPaginationRes<HTTransaction>> getTransactions(HTGetTransactionsRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest().setQuery(Common.toMap(request));
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("GET")
            .setPathname("/vcc-api/api/vcc/v1/authorization");
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 获取任务列表
     *
     * @param request
     * @return
     */
    public CommonResponse<HTPaginationRes<HTTask>> getTasks(HTGetTasksRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest().setQuery(Common.toMap(request));
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("GET")
            .setPathname("/vcc-api/api/vcc/v1/job");
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    /**
     * 获取任务详情
     *
     * @param taskId
     * @return
     */
    public CommonResponse<HTTask> getTaskDetail(String taskId) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        OpenApiRequest req = new OpenApiRequest();
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("GET")
            .setPathname("/vcc-api/api/vcc/v1/job/%s".formatted(taskId));
        return this.callApi(params, req, runtimeOptions).to(new TypeReference<>() {});
    }

    private JSONObject callApi(Params params, OpenApiRequest req, RuntimeOptions runtime) {
        req.addHeader("customerToken", this.customerToken);
        if (params.getMethod().equalsIgnoreCase("POST") && req.getBody() != null) {
            String encryptData = JSONObject.toJSONString(req.getBody());
            try {
                String payload = JWEUtilsUsingRSAPKI.createJwe(encryptData, serverPublicKey, secret);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("payload", payload);
                req.setBody(jsonObject);
            } catch (Exception e) {
                throw new ThirdException(e.getMessage(), e);
            }
        }
        Map<String, ?> response = null;
        boolean isError = false;
        String payload;
        try {
            response = this.doRequest(params, req, runtime);
            JSONObject result = JSONObject.from(response.get("body"));
            payload = result.getString("payload");
        } catch (ThirdException e) {
            isError = true;
            Map<String, Object> data = e.getData();
            payload = (String)data.get("payload");
        }
        try {
            String decryptData = JWEUtilsUsingRSAPKI.decryptJwe(payload, customerPrivateKey);
            JSONObject data = JSONObject.parseObject(decryptData);
            if (isError) {
                throw new ThirdException(Map.of("code", data.get("code"), "message", data.get("message")));
            }
            JSONObject res = new JSONObject();
            res.put("headers", response.get("headers"));
            res.put("body", data);
            return res;
        } catch (GeneralSecurityException | ParseException e) {
            throw new ThirdException(e.getMessage(), e);
        }
    }
}
