/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import top.continew.katai.cardvp.CardVpCrawlerConfig;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class CardVpCrawlerClient extends Client {

    private final String username;

    private final String password;

    @Setter
    @Getter
    private String cookie;

    public CardVpCrawlerClient(CardVpCrawlerConfig config) {
        super(config);
        this.username = config.getUsername();
        this.password = config.getPassword();
    }

    public CommonResponse<String> login() {
        Map<String, Object> form = new HashMap<>();
        form.put("user.suUser", this.username);
        form.put("user.suPasswd", this.password);
        OpenApiRequest req = new OpenApiRequest().setBody(form);
        Params params = new Params().setProtocol(config.protocol).setMethod("POST").setPathname("/login_commit.html");
        return this.callApi(params, req, new RuntimeOptions()).to(new TypeReference<>() {});
    }

    public CommonResponse<String> getCardList(int page, int pageSize, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> form = new HashMap<>();
        form.put("page", page);
        form.put("pagesize", pageSize);
        OpenApiRequest req = new OpenApiRequest().setBody(form);
        if (startTime != null && endTime != null) {
            form.put("rangtime", String.format("%s To %s", LocalDateTimeUtil.format(startTime, "yyyy-MM-dd"), LocalDateTimeUtil.format(endTime, "yyyy-MM-dd")));
        }
        Params params = new Params().setProtocol(config.protocol).setMethod("POST").setPathname("/card_query.html");
        return this.callApi(params, req, new RuntimeOptions()).to(new TypeReference<>() {});
    }

    public CommonResponse<String> getBillingList(int page,
                                                 int pageSize,
                                                 LocalDateTime startTime,
                                                 LocalDateTime endTime) {
        Map<String, Object> form = new HashMap<>();
        form.put("page", page);
        form.put("pagesize", pageSize);
        OpenApiRequest req = new OpenApiRequest().setBody(form);
        if (startTime != null && endTime != null) {
            form.put("rangtime", String.format("%s To %s", LocalDateTimeUtil.format(startTime, "yyyy-MM-dd"), LocalDateTimeUtil.format(endTime, "yyyy-MM-dd")));
        }
        Params params = new Params().setProtocol(config.protocol).setMethod("POST").setPathname("/billing_query.html");
        return this.callApi(params, req, new RuntimeOptions()).to(new TypeReference<>() {});
    }

    public CommonResponse<String> getAvailableAuthorizeList(int page,
                                                 int pageSize) {
        Map<String, Object> form = new HashMap<>();
        form.put("page", page);
        form.put("pagesize", pageSize);
        OpenApiRequest req = new OpenApiRequest().setBody(form);
        Params params = new Params().setProtocol(config.protocol).setMethod("POST").setPathname("/available_authorize.html");
        return this.callApi(params, req, new RuntimeOptions()).to(new TypeReference<>() {});
    }

    public CommonResponse<String> updateCard(String cardNumber, String label, String cardName) {
        Map<String, Object> form = new HashMap<>();
        form.put("cardnumber", cardNumber);
        form.put("label", label);
        form.put("cardname", cardName);
        OpenApiRequest req = new OpenApiRequest().setBody(form);
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("POST")
            .setPathname("/cardinfo_editcommit.html");
        return this.callApi(params, req, new RuntimeOptions()).to(new TypeReference<>() {});
    }

    public CommonResponse<String> getCardBulkAddPage() {
        OpenApiRequest req = new OpenApiRequest();
        Params params = new Params().setProtocol(config.protocol)
            .setMethod("GET")
            .setPathname("/cardbulk_add.html");
        return this.callApi(params, req, new RuntimeOptions()).to(new TypeReference<>() {});
    }

    private JSONObject callApi(Params params, OpenApiRequest req, RuntimeOptions runtime) {
        if (!params.getPathname().contains("login") && StringUtils.isNotBlank(this.cookie)) {
            req.addHeader("cookie", this.cookie);
        }
        params.setBodyType("string").setReqBodyType("form");
        Map<String, ?> response = this.doRequest(params, req, runtime);
        JSONObject result = new JSONObject();
        result.put("headers", response.get("headers"));
        result.put("body", response.get("body"));
        return result;
    }
}
