/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.exception;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class ThirdException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    public String code;
    public String message;
    public Map<String, Object> data;

    public ThirdException() {
    }

    public ThirdException(String message, Throwable cause) {
        super(message, cause);
    }

    public ThirdException(Map<String, ?> map) {
        this.setCode(String.valueOf(map.get("code")));
        this.setMessage(String.valueOf(map.get("message")));
        Object obj = map.get("data");
        if (obj != null) {
            if (obj instanceof Map) {
                this.data = (Map)obj;
            } else {
                Map<String, Object> hashMap = new HashMap<>();
                Field[] declaredFields = obj.getClass().getDeclaredFields();
                for (Field field : declaredFields) {
                    field.setAccessible(true);

                    try {
                        hashMap.put(field.getName(), field.get(obj));
                    } catch (Exception ignored) {
                    }
                }

                this.data = hashMap;
            }
        }
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Map<String, Object> getData() {
        return this.data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }
}
