/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

public class Params {

    public String protocol = "https";

    public String pathname;

    public String method;

    public String bodyType = "json";

    public String reqBodyType = "json";

    public Params() {
    }

    public Params setProtocol(String protocol) {
        this.protocol = protocol;
        return this;
    }

    public String getProtocol() {
        return this.protocol;
    }

    public Params setPathname(String pathname) {
        this.pathname = pathname;
        return this;
    }

    public String getPathname() {
        return this.pathname;
    }

    public Params setMethod(String method) {
        this.method = method;
        return this;
    }

    public String getMethod() {
        return this.method;
    }

    public Params setBodyType(String bodyType) {
        this.bodyType = bodyType;
        return this;
    }

    public String getBodyType() {
        return this.bodyType;
    }

    public Params setReqBodyType(String reqBodyType) {
        this.reqBodyType = reqBodyType;
        return this;
    }

    public String getReqBodyType() {
        return this.reqBodyType;
    }
}
