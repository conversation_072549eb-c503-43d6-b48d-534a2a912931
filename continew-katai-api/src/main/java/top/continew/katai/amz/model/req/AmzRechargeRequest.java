package top.continew.katai.amz.model.req;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
public class AmzRechargeRequest {
    /**
     * 卡段-6位数字
     */
    @JSONField(name = "card_type")
    private Integer cardType;

    /**
     * 请求ID
     */
    @JSONField(name = "request_id")
    private Long requestId;

    /**
     * 充值金额
     */
    @JSONField(name = "amount")
    private BigDecimal amount;

    /**
     * 账户类型：USD或CNY
     */
    @JSONField(name = "account_type")
    private String accountType;
}
