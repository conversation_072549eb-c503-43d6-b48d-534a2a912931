package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/16 15:51
 */
@Data
public class AmzCardTypeResponse {
    /**
     * 卡段
     */
    @JSONField(name = "card_type")
    private Integer cardType;

    /**
     * 开卡费用，每张
     */
    @JSONField(name = "new_card_fee")
    private BigDecimal newCardFee;

    /**
     * 服务费，百分比
     */
    @JSONField(name = "service_fee")
    private BigDecimal serviceFee;

    /**
     * 最低开卡金额，USD
     */
    @JSONField(name = "min_opencard_amount")
    private BigDecimal minOpenCardAmount;

    /**
     * 最低充值金额，USD
     */
    @JSONField(name = "min_recharge_amount")
    private BigDecimal minRechargeAmount;
}
