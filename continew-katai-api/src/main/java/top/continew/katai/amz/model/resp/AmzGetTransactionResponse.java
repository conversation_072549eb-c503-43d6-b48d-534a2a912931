package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class AmzGetTransactionResponse {
    @JSONField(name = "item")
    private List<AmzTransactionItem> item;

    @JSONField(name = "page_no")
    private Integer pageNo;

    @JSONField(name = "page_size")
    private Integer pageSize;

    @JSONField(name = "total_size")
    private Integer totalSize;
}
