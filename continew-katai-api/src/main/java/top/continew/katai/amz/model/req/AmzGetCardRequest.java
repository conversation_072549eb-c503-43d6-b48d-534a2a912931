package top.continew.katai.amz.model.req;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class AmzGetCardRequest {
    /**
     * 16位AES密钥加密之后的值，16位AES密钥由商户随机生成
     */
    @JSONField(name = "ram")
    private String ram;


    /**
     * 第几页
     */
    @JSONField(name = "page_no")
    private Integer pageNo;

    /**
     * 页数
     */
    @JSONField(name = "page_size")
    private Integer pageSize;
}
