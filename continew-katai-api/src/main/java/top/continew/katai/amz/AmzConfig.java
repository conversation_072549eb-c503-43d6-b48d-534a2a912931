/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.amz;

import top.continew.katai.ThirdConfig;

/**
 * AMZ配置类
 *
 * <AUTHOR>
 * @since 2025/1/1
 */
public class AmzConfig extends ThirdConfig {

    public String appId;

    public String appKey;

    public String privateKey;

    public String publicKey;

    /**
     * 获取私钥
     *
     * @return 私钥
     */
    public String getPrivateKey() {
        return privateKey;
    }

    /**
     * 设置私钥
     *
     * @param privateKey 私钥
     */
    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    /**
     * 获取公钥
     *
     * @return 公钥
     */
    public String getPublicKey() {
        return publicKey;
    }

    /**
     * 设置公钥
     *
     * @param publicKey 公钥
     */
    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    /**
     * 获取应用ID
     *
     * @return 应用ID
     */
    public String getAppId() {
        return appId;
    }

    /**
     * 设置应用ID
     *
     * @param appId 应用ID
     */
    public void setAppId(String appId) {
        this.appId = appId;
    }

    /**
     * 获取应用密钥
     *
     * @return 应用密钥
     */
    public String getAppKey() {
        return appKey;
    }

    /**
     * 设置应用密钥
     *
     * @param appKey 应用密钥
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    /**
     * 获取协议
     *
     * @return 协议
     */
    public String getProtocol() {
        return protocol;
    }

    /**
     * 设置协议
     *
     * @param protocol 协议
     * @return AmzConfig
     */
    public AmzConfig setProtocol(String protocol) {
        this.protocol = protocol;
        return this;
    }

    /**
     * 获取请求方法
     *
     * @return 请求方法
     */
    public String getMethod() {
        return method;
    }

    /**
     * 设置请求方法
     *
     * @param method 请求方法
     * @return AmzConfig
     */
    public AmzConfig setMethod(String method) {
        this.method = method;
        return this;
    }

    /**
     * 获取读取超时时间
     *
     * @return 读取超时时间
     */
    public Integer getReadTimeout() {
        return readTimeout;
    }

    /**
     * 设置读取超时时间
     *
     * @param readTimeout 读取超时时间
     * @return AmzConfig
     */
    public AmzConfig setReadTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
        return this;
    }

    /**
     * 获取连接超时时间
     *
     * @return 连接超时时间
     */
    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    /**
     * 设置连接超时时间
     *
     * @param connectTimeout 连接超时时间
     * @return AmzConfig
     */
    public AmzConfig setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }

    /**
     * 获取HTTP代理
     *
     * @return HTTP代理
     */
    public String getHttpProxy() {
        return httpProxy;
    }

    /**
     * 设置HTTP代理
     *
     * @param httpProxy HTTP代理
     * @return AmzConfig
     */
    public AmzConfig setHttpProxy(String httpProxy) {
        this.httpProxy = httpProxy;
        return this;
    }

    /**
     * 获取HTTPS代理
     *
     * @return HTTPS代理
     */
    public String getHttpsProxy() {
        return httpsProxy;
    }

    /**
     * 设置HTTPS代理
     *
     * @param httpsProxy HTTPS代理
     * @return AmzConfig
     */
    public AmzConfig setHttpsProxy(String httpsProxy) {
        this.httpsProxy = httpsProxy;
        return this;
    }

    /**
     * 获取端点
     *
     * @return 端点
     */
    public String getEndpoint() {
        return endpoint;
    }

    /**
     * 设置端点
     *
     * @param endpoint 端点
     * @return AmzConfig
     */
    public AmzConfig setEndpoint(String endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    /**
     * 获取无代理
     *
     * @return 无代理
     */
    public String getNoProxy() {
        return noProxy;
    }

    /**
     * 设置无代理
     *
     * @param noProxy 无代理
     * @return AmzConfig
     */
    public AmzConfig setNoProxy(String noProxy) {
        this.noProxy = noProxy;
        return this;
    }

    /**
     * 获取最大空闲连接数
     *
     * @return 最大空闲连接数
     */
    public Integer getMaxIdleConns() {
        return maxIdleConns;
    }

    /**
     * 设置最大空闲连接数
     *
     * @param maxIdleConns 最大空闲连接数
     * @return AmzConfig
     */
    public AmzConfig setMaxIdleConns(Integer maxIdleConns) {
        this.maxIdleConns = maxIdleConns;
        return this;
    }

    /**
     * 获取SOCKS5代理
     *
     * @return SOCKS5代理
     */
    public String getSocks5Proxy() {
        return socks5Proxy;
    }

    /**
     * 设置SOCKS5代理
     *
     * @param socks5Proxy SOCKS5代理
     * @return AmzConfig
     */
    public AmzConfig setSocks5Proxy(String socks5Proxy) {
        this.socks5Proxy = socks5Proxy;
        return this;
    }

    /**
     * 获取日志启用状态
     *
     * @return 日志启用状态
     */
    public Boolean getLogEnabled() {
        return logEnabled;
    }

    /**
     * 设置日志启用状态
     *
     * @param logEnabled 日志启用状态
     * @return AmzConfig
     */
    public AmzConfig setLogEnabled(Boolean logEnabled) {
        this.logEnabled = logEnabled;
        return this;
    }
}