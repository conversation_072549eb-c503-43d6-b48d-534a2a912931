package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/16 15:51
 */
@Data
public class AmzCardBalanceResponse {
    /**
     * 卡状态：1是正常，2是作废，3是作废中
     */
    @JSONField(name = "card_status")
    private Integer cardStatus;


    /**
     * 余额
     */
    @JSONField(name = "card_balance")
    private BigDecimal cardBalance;
}
