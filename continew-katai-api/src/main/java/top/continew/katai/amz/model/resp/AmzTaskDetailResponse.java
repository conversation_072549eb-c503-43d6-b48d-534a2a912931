/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * AMZ任务详情响应
 *
 * <AUTHOR>
 * @since 2025/1/1
 */
@Getter
@Setter
public class AmzTaskDetailResponse {
    /**
     * 任务ID
     */
    @JSONField(name = "task_id")
    private Long taskId;

    /**
     * 任务类型：1开卡，2充值，3提现
     */
    @JSONField(name = "task_type")
    private Integer taskType;

    /**
     * 任务状态：1处理中，2成功，3失败
     */
    @JSONField(name = "task_status")
    private Integer taskStatus;

    /**
     * 失败原因
     */
    @JSONField(name = "fail_reason")
    private String failReason;

    /**
     * 卡片列表（开卡任务时返回）
     */
    @JSONField(name = "cards")
    private List<AmzCardResponse> cards;

    /**
     * 创建时间
     */
    @JSONField(name = "create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @JSONField(name = "update_time")
    private String updateTime;
}