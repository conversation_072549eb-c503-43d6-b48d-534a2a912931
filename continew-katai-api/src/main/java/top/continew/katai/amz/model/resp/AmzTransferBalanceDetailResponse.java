package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/16 15:35
 */
@Data
public class AmzTransferBalanceDetailResponse {
    /**
     * 状态：0 待处理，1 处理中，2 成功，3 失败，4 提
     * 现处理中
     */
    @JSONField(name = "status")
    private Integer status;

    /**
     *  实际转出金额 status:2 返回
     */
    @JSONField(name = "had_transfer_amount")
    private BigDecimal hadTransferAmount;

    /**
     * 剩余余额 status : 2 返回
     */
    @JSONField(name = "balance_amount")
    private BigDecimal balanceAmount;

    /**
     * 处理时间 ， 状态：0 不返回
     */
    @JSONField(name = "update_time")
    private String updateTime;

    /**
     * 描述: status ：3 返回，
     */
    @JSONField(name = "msg")
    private String msg;
}
