package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/16 15:35
 */
@Data
public class AmzTaskResponse<T> {
    /**
     * 任务订单ID，通过任务详情接口查询任务开卡的数据，建议5分钟后查询
     */
    @JSONField(name = "task_id")
    private Long taskId;

    /**
     * 任务订单号
     */
    @JSONField(name = "order_no")
    private String orderNo;

    /**
     * 任务状态：0未处理，1已完成，2处理中，3处理异常，4部分成功，5全部失败
     */
    @JSONField(name = "task_status")
    private Integer taskStatus;

    @JSONField(name = "item")
    private String item;

    private T data;
}
