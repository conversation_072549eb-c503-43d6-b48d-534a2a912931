package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class AmzGetCardResponse {
    private List<AmzCardResponse> cards;

    @JSONField(name = "item")
    private String item;

    @JSONField(name = "page_no")
    private Integer pageNo;

    @JSONField(name = "page_size")
    private Integer pageSize;

    @JSONField(name = "total_size")
    private Integer totalSize;
}
