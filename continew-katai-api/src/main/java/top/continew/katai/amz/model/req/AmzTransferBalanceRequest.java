package top.continew.katai.amz.model.req;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
public class AmzTransferBalanceRequest {
    /**
     * 卡唯一ID
     */
    @JSONField(name="request_id")
    private Long requestId;

    /**
     * 提现金额
     */
    @JSONField(name="transfer_amount")
    private BigDecimal transferAmount;

}
