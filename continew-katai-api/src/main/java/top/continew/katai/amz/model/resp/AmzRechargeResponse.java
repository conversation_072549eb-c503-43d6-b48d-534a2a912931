package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/16 15:51
 */
@Data
public class AmzRechargeResponse {
    @JSONField(name = "request_id")
    private Integer requestId;

    /**
     * 0 未处理，1 已处理，2 处理异常 ，3 已退款到账户余额 4 处理中
     */
    @JSONField(name = "task_status")
    private Integer taskStatus;

    /**
     * 处理时间
     */
    @JSONField(name = "task_time")
    private String taskTime;

    /**
     * 充值金额
     */
    @JSONField(name = "amount")
    private BigDecimal amount;

    /**
     * 卡余额
     */
    @JSONField(name = "card_balance")
    private BigDecimal cardBalance;
}
