package top.continew.katai.amz.model.req;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
public class AmzCreateCardRequest {
    /**
     * 账户类型：USD或CNY
     */
    @JSONField(name = "account_type")
    private String accountType;

    /**
     * 开卡金额
     */
    @JSONField(name = "amount")
    private BigDecimal amount;

    /**
     * 卡段-6位数字
     */
    @JSONField(name = "card_type")
    private Integer cardType;

    /**
     * 开卡数量
     */
    @JSONField(name = "number")
    private Integer number;

}
