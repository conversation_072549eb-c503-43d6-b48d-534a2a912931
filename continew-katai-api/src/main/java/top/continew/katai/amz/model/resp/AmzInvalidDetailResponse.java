package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/16 15:35
 */
@Data
public class AmzInvalidDetailResponse {

    /**
     * 作废卡处理状态：0 未处理，1 处理中 2 成功，3 失败，4 销卡中
     */
    @JSONField(name = "status")
    private Integer status;

    /**
     * 作废成功后返回的可用余额
     */
    @JSONField(name = "available_balance")
    private BigDecimal availableBalance;

    /**
     * 作废时间
     */
    @JSONField(name = "update_time")
    private String updateTime;
}
