package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AmzTransactionItem {
    @JSONField(name = "request_id")
    private String requestId;


    /**
     * 发卡行响应，描述； 如：卡余额不足
     */
    @JSONField(name="response")
    private String response;

    /**
     * 授权码
     */
    @JSONField(name="approval_code")
    private String approvalCode;

    /**
     * 开单金额
     */
    @JSONField(name = "opening_amount")
    private BigDecimal openingAmount;

    /**
     * 开单币种
     */
    @JSONField(name = "billing_currency_code")
    private String billingCurrencyCode;

    /**
     * 交易金额
     */
    @JSONField(name = "trader_amount")
    private BigDecimal traderAmount;

    /**
     * 交易币种
     */
    @JSONField(name = "trader_billing_currency_code")
    private String traderBillingCurrencyCode;

    /**
     * 交易时间
     */
    @JSONField(name = "transaction_date")
    private String transactionDate;

    /**
     * 交易类型 ： 授权，结算
     */
    @JSONField(name = "transaction_type")
    private String transactionType;

    /**
     * 已掌握响应 ：成功 、 失败
     */
    @JSONField(name = "controlled_response")
    private String controlledResponse;

    /**
     * 商家名称
     */
    @JSONField(name = "merchant_name")
    private String merchantName;

    /**
     * 交易ID
     */
    @JSONField(name = "transaction_id")
    private String transactionId;

    /**
     * 卡段
     */
    @JSONField(name = "card_type")
    private Integer cardType;
}
