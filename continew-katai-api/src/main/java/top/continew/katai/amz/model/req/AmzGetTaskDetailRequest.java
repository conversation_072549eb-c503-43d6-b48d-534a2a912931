package top.continew.katai.amz.model.req;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class AmzGetTaskDetailRequest {
    /**
     * 任务类型，1是开卡，2是充值
     */
    @JSONField(name = "task_type")
    private Integer taskType;

    /**
     * 任务订单ID
     */
    @JSONField(name = "task_id")
    private Long taskId;

    /**
     * 16位AES 密钥加密之后的值
     */
    @JSONField(name = "ram")
    private String ram;
}
