package top.continew.katai.amz.model.req;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class AmzGetTransactionRequest {
    /**
     * 卡ID
     */
    @JSONField(name = "request_id")
    private String requestId;

    /**
     * 交易开始日期，只能查最近一月的交易记录
     */
    @JSONField(name = "start_date")
    private String startDate;

    /**
     * 交易结束日期，只能查最近一月的交易记录
     */
    @JSONField(name = "end_date")
    private String endDate;

    /**
     * 第几页
     */
    @JSONField(name = "page_no")
    private Integer pageNo;

    /**
     * 页数
     */
    @JSONField(name = "page_size")
    private Integer pageSize;
}
