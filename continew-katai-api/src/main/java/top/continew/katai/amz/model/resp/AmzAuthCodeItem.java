package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/16 15:51
 */
@Data
public class AmzAuthCodeItem {
    @JSONField(name = "id")
    private String id;
    /**
     * 卡后4位
     */
    @JSONField(name = "card_no_last4")
    private String cardNoLast4;
    /**
     * 验证码
     */
    @JSONField(name = "auth_code")
    private String authCode;
    /**
     * 商家名称
     */
    @JSONField(name = "merchant_name")
    private String merchantName;
    /**
     * 交易金额
     */
    @JSONField(name = "trader_amount")
    private String traderAmount;

    /**
     * 货币
     */
    @JSONField(name = "trader_billing_currency_code")
    private String traderBillingCurrencyCode;

    /**
     * 内容描述
     */
    @JSONField(name = "content")
    private String content;
    /**
     * 导入时间
     */
    @JSONField(name = "create_time")
    private String createTime;
}
