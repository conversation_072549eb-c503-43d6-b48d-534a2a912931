package top.continew.katai.amz.model.resp;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/16 15:51
 */
@Data
public class AmzCardResponse {
    /**
     * 卡段类型
     */
    @JSONField(name = "card_type")
    private Integer cardType;

    /**
     * 卡状态：1是正常，2是作废，3是作废中
     */
    private Integer cardStatus;


    /**
     * 请求ID，唯一性
     */
    @JSONField(name = "request_id")
    private Long requestId;

    /**
     * 卡号
     */
    @JSONField(name = "card_no")
    private String cardNo;

    /**
     * 安全码
     */
    @JSONField(name = "cvv")
    private Integer cvv;

    /**
     * 卡有效期:年-月
     */
    @JSONField(name = "valid_date")
    private String validDate;

    /**
     * 开卡金额
     */
    @JSONField(name = "open_card_amount")
    private BigDecimal openCardAmount;

    /**
     * 创建时间
     */
    @JSONField(name = "create_time")
    private String createTime;

    /**
     * 余额
     */
    @JSONField(name = "total_price")
    private BigDecimal totalPrice;
}
