/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.utils.Common;
import top.continew.katai.utils.Third;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class Client {

    public ThirdConfig config;

    public Client(ThirdConfig config) {
        this.config = config;
    }

    public Map<String, ?> doRequest(Params params, OpenApiRequest request, RuntimeOptions runtimeOptions) {
        Map<String, ?> res;
        DaziRequest request_ = new DaziRequest();
        if (runtimeOptions.getReadTimeout() == null) {
            runtimeOptions.setReadTimeout(config.readTimeout);
        }
        if (runtimeOptions.getConnectTimeout() == null) {
            runtimeOptions.setConnectTimeout(config.connectTimeout);
        }
        if (Common.empty(runtimeOptions.getHttpProxy())) {
            runtimeOptions.setHttpProxy(config.httpProxy);
        }
        if (Common.empty(runtimeOptions.getHttpsProxy())) {
            runtimeOptions.setHttpsProxy(config.httpsProxy);
        }
        if (Common.empty(runtimeOptions.getSocks5Proxy())) {
            runtimeOptions.setSocks5Proxy(config.socks5Proxy);
        }
        if (runtimeOptions.getMaxIdleConns() == null) {
            runtimeOptions.setMaxIdleConns(config.maxIdleConns);
        }
        request_.protocol = StringUtils.defaultIfBlank(this.config.protocol, params.protocol);
        request_.method = params.method;
        request_.pathname = params.pathname;
        request_.query = request.query;
        Map<String, String> headers = request.headers == null ? new HashMap<>() : request.headers;
        headers.put("host", this.config.endpoint);
        request_.headers = headers;
        String str;
        if (!Common.isUnset(request.stream)) {
            byte[] tmp = Common.readAsBytes(request.stream);
            request_.body = Third.toReadable(tmp);
            request_.headers.put("content-type", "application/octet-stream");
        } else if (!Common.isUnset(request.body)) {
            if (Common.equalString(params.reqBodyType, "json")) {
                String bodyStr = Common.toJSONString(request.body);
                request_.body = Third.toReadable(bodyStr);
                request_.headers.put("content-type", "application/json; charset=utf-8");
            } else if (Common.equalString(params.reqBodyType, "form")) {
                Map<String, Object> m = Common.assertAsMap(request.body);
                str = Common.toFormString(m);
                request_.body = Third.toReadable(str);
                request_.headers.put("content-type", "application/x-www-form-urlencoded");
            } else {
                String bodyStr = Common.toJSONString(request.body);
                request_.body = Third.toReadable(bodyStr);
            }
        }
        DaziResponse response_ = null;
        int retryTime = 0;
        while (retryTime < 3) {
            try {
                response_ = Third.doAction(request_, runtimeOptions);
                break;
            } catch (ThirdException e) {
                if (e.getCause() instanceof IOException) {
                    retryTime++;
                    Common.sleep(1000);
                } else {
                    throw e;
                }
            }
        }
        Object arr;
        if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
            if (config.endpoint.equals("api.huitongcard.com")) {
                arr = Common.readAsJSON(response_.body);
                res = Common.assertAsMap(arr);
                throw new ThirdException(Map
                    .of("code", response_.statusCode, "message", response_.statusMessage, "data", res));
            } else {
                String body = Common.readAsString(response_.body);
                if (JSON.isValid(body)) {
                    arr = JSONObject.parseObject(body);
                    res = Common.assertAsMap(arr);
                    String msg = (String)res.get("message");
                    if(null == msg) {
                        msg = (String)res.get("msg");;
                    }

                    if(null == msg) {
                        msg = "error";
                    }

                    throw new ThirdException(Map.of("code", res.get("code"), "message", msg , "data", res));
                } else {
                    throw new ThirdException(Map.of("code", response_.statusCode, "message", response_.statusMessage));
                }
            }
        }
        Map<String, ?> resp;
        if (!Common.equalString(params.bodyType, "binary")) {
            if (Common.equalString(params.bodyType, "byte")) {
                byte[] byt = Common.readAsBytes(response_.body);
                res = Map.of("body", byt, "headers", response_.headers);
                return res;
            }

            if (Common.equalString(params.bodyType, "string")) {
                str = Common.readAsString(response_.body);
                res = Map.of("body", str, "headers", response_.headers);
                return res;
            }

            if (Common.equalString(params.bodyType, "json")) {
                arr = Common.readAsJSON(response_.body);
                res = Common.assertAsMap(arr);
                return Map.of("body", res, "headers", response_.headers);
            }

            if (Common.equalString(params.bodyType, "array")) {
                arr = Common.readAsJSON(response_.body);
                res = Map.of("body", arr, "headers", response_.headers);
                return res;
            }

            resp = Map.of("headers", response_.headers);
            return resp;
        }

        resp = Map.of("body", response_.body, "headers", response_.headers);
        res = resp;
        return res;
    }
}
