/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.atlassian.guava.common.util.concurrent.RateLimiter;
import org.apache.commons.lang3.StringUtils;
import top.continew.katai.cardvp.CardVpConfig;
import top.continew.katai.cardvp.model.CVPaginationRes;
import top.continew.katai.cardvp.model.card.CVCard;
import top.continew.katai.cardvp.model.card.CVCreateCardRequest;
import top.continew.katai.cardvp.model.card.CVUpdateCardAmountRequest;
import top.continew.katai.cardvp.model.card.CVUpdateCardInformationRequest;
import top.continew.katai.cardvp.model.transaction.CVGetTransactionsRequest;
import top.continew.katai.cardvp.model.transaction.CVTransaction;
import top.continew.katai.exception.ThirdException;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 接口文档
 * <a href="https://apifox.com/apidoc/shared-48826a20-90bd-431f-a463-8525460e156d">...</a>
 */
public class CardVpClient extends Client {

    private final String appId;

    private final String appKey;

    private final RateLimiter rateLimiter = RateLimiter.create(3);

    public CardVpClient(CardVpConfig config) {
        super(config);
        this.appId = config.getAppId();
        this.appKey = config.getAppKey();
    }

    /**
     * 创建新卡
     *
     * @param request
     * @param runtime
     * @return
     */
    public CommonResponse<CVCard> createCard(CVCreateCardRequest request, RuntimeOptions runtime) {
        OpenApiRequest req = new OpenApiRequest().setBody(request);
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/openCard");
        return this.callApi(params, req, runtime, "card", false).to(new TypeReference<>() {});
    }

    public CommonResponse<CVCard> createCard(CVCreateCardRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        return this.createCard(request, runtimeOptions);
    }

    /**
     * 更新卡片信息/修改状态
     *
     * @param request
     * @param runtime
     * @return
     */
    public CommonResponse<CVCard> updateCardInformation(CVUpdateCardInformationRequest request,
                                                        RuntimeOptions runtime) {
        OpenApiRequest req = new OpenApiRequest().setBody(request);
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/updateCardInfo");
        return this.callApi(params, req, runtime, "card", false).to(new TypeReference<>() {});
    }

    public CommonResponse<CVCard> updateCardInformation(CVUpdateCardInformationRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        return this.updateCardInformation(request, runtimeOptions);
    }

    /**
     * 更新卡片信息/修改状态
     *
     * @param cardNumber
     * @param runtime
     * @return
     */
    public CommonResponse<CVCard> getCardInformation(String cardNumber, RuntimeOptions runtime) {
        JSONObject body = new JSONObject();
        body.put("cardnumber", cardNumber);
        OpenApiRequest req = new OpenApiRequest().setBody(body);
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/getCardDetail");
        return this.callApi(params, req, runtime, "card", false).to(new TypeReference<>() {});
    }

    public CommonResponse<CVCard> getCardInformation(String cardNumber) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        return this.getCardInformation(cardNumber, runtimeOptions);
    }

    /**
     * 更新卡片限额
     *
     * @param request
     * @param runtime
     * @return
     */
    public CommonResponse<CVCard> updateCardAmount(CVUpdateCardAmountRequest request, RuntimeOptions runtime) {
        OpenApiRequest req = new OpenApiRequest().setBody(request);
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/updateCard");
        return this.callApi(params, req, runtime, "card", false).to(new TypeReference<>() {});
    }

    public CommonResponse<CVCard> updateCardAmount(CVUpdateCardAmountRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        return this.updateCardAmount(request, runtimeOptions);
    }

    /**
     * 查询交易流水
     *
     * @param request
     * @param runtime
     * @return
     */
    public CommonResponse<CVPaginationRes<CVTransaction>> getTransactions(CVGetTransactionsRequest request,
                                                                          RuntimeOptions runtime) {
        OpenApiRequest req = new OpenApiRequest().setBody(request);
        Params params = new Params().setProtocol("HTTPS")
            .setMethod("POST")
            .setPathname("/api/v1/searchCardTransactions");
        return this.callApi(params, req, runtime, "transactions", true).to(new TypeReference<>() {});
    }

    public CommonResponse<CVPaginationRes<CVTransaction>> getTransactions(CVGetTransactionsRequest request) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        return this.getTransactions(request, runtimeOptions);
    }

    /**
     * 获取卡片交易流水
     *
     * @param cardNumber
     * @param runtime
     * @return
     */
    public CommonResponse<List<CVTransaction>> getCardTransactions(String cardNumber,
                                                                              RuntimeOptions runtime) {
        JSONObject body = new JSONObject();
        body.put("cardnumber", cardNumber);
        OpenApiRequest req = new OpenApiRequest().setBody(body);
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/getCardTransactions");
        return this.callApi(params, req, runtime, "transactions", false).to(new TypeReference<>() {});
    }

    public CommonResponse<List<CVTransaction>> getCardTransactions(String cardNumber) {
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        return this.getCardTransactions(cardNumber, runtimeOptions);
    }

    private JSONObject callApi(Params params,
                               OpenApiRequest req,
                               RuntimeOptions runtime,
                               String respKey,
                               boolean isPage) {
        req.addHeader("APP-ID", this.appId);
        req.addHeader("APP-KEY", this.appKey);
        if (rateLimiter.tryAcquire(20, TimeUnit.SECONDS)) {
            Map<String, ?> response = this.doRequest(params, req, runtime);
            JSONObject data = JSONObject.from(response.get("body"));
            int code = data.getIntValue("code");
            if (code != 200) {
                throw new ThirdException(Map.of("code", code, "message", StringUtils.defaultIfBlank(data.getString("message"), "请求错误")));
            }
            JSONObject result = new JSONObject();
            result.put("headers", response.get("headers"));
            if (isPage) {
                JSONObject body = new JSONObject();
                body.put("total", data.getIntValue("total"));
                body.put("numbers", data.getIntValue("numbers"));
                body.put("items", data.getJSONArray(respKey));
                result.put("body", body);
            } else {
                result.put("body", data.get(respKey));
            }
            return result;
        } else {
            throw new ThirdException(Map.of("code", "429", "message", "接口限流，请稍后再试"));
        }
    }
}
