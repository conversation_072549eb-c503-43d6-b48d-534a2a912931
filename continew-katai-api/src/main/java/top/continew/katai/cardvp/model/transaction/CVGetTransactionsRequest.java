/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.cardvp.model.transaction;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class CVGetTransactionsRequest {
    /**
     * Created end, createdEnd - createdStart <= 7 days
     */
    private String createdEnd;
    /**
     *
     * {"createdStart":"2024-08-01T08:43:28","createdEnd":"2024-08-30T08:43:28","pageSize":"2","pageIndex":"1","status":"DECLINED"}
     * Created start
     */
    private String createdStart;
    /**
     * Page number
     */
    private Integer pageIndex;
    /**
     * Number of data per page
     */
    private Integer pageSize;
    /**
     * Status of the transaction
     */
    private String status;

    private String paydStart;

    private String paydEnd;
}
