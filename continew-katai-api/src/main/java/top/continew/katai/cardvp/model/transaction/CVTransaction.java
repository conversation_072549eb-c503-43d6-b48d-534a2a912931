/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.cardvp.model.transaction;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class CVTransaction {

    private BigDecimal amount;
    /**
     * @Schema(description = "Approval code for the transaction, if any", example = "")
     *                     Authorization code for the transaction
     */
    private String authCode;
    /**
     * Name on the card
     */
    private String cardname;
    /**
     * Card number used in the transaction
     */
    private String cardnumber;
    /**
     * Country of the card
     */
    private String country;
    /**
     * Card Verification Value (CVV)
     */
    private String cvv;
    /**
     * Description of the transaction
     */
    private String desciption;
    /**
     * Card's expiration date in format MM/YY
     */
    private String expdate;
    /**
     * Last few digits of card number, if applicable
     */
    private String lastnumber;
    /**
     * Merchant Category Code (MCC)
     */
    private String merchantMcc;
    /**
     * Name of the merchant
     */
    private String merchantName;
    /**
     * Payment date and time
     */
    private String paydate;
    /**
     * Status of the transaction
     */
    private String status;
    /**
     * Unique transaction number
     */
    private String tradeNo;
    /**
     * Transaction type, Available: 'authorization', 'authorization.clearing',
     * 'authorization.reversal', 'authorization.reversal.issuerexpiration',
     * 'refund.authorization.clearing'
     */
    private String transactionType;

}
