/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.cardvp.model.card;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class CVUpdateCardInformationRequest {

    /**
     * Card number sequence
     */
    private String cardnumber;
    /**
     * Type of purchase made, Available: 'ADS', 'ECOMMERCE-COMMON', 'ECOMMERCE-ELSE'
     * <p>
     * For example:
     * Temu platform should use ECOMMERCE-COMMON
     * Amazon is ECOMMERCE-COMMON
     * Paypal is ECOMMERCE-COMMON
     * Facebook ADS is ADS
     * Etsy is ECOMMERCE-ELSE
     * Strikingly is ECOMMERCE-COMMON
     * STK*Shuttterstock is ECOMMERCE-ELSE
     */
    private String purchaseType;
    /**
     * R activates card. P suspends card
     */
    private String status;
}
