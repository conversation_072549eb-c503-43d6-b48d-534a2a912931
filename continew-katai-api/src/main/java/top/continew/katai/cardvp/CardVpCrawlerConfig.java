/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.cardvp;

import lombok.Getter;
import top.continew.katai.ThirdConfig;

@Getter
public class CardVpCrawlerConfig extends ThirdConfig {

    public String username;

    public String password;

    public CardVpCrawlerConfig setUsername(String username) {
        this.username = username;
        return this;
    }

    public CardVpCrawlerConfig setPassword(String password) {
        this.password = password;
        return this;
    }

    public String getProtocol() {
        return protocol;
    }

    public CardVpCrawlerConfig setProtocol(String protocol) {
        this.protocol = protocol;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public CardVpCrawlerConfig setMethod(String method) {
        this.method = method;
        return this;
    }

    public Integer getReadTimeout() {
        return readTimeout;
    }

    public CardVpCrawlerConfig setReadTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
        return this;
    }

    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    public CardVpCrawlerConfig setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }

    public String getHttpProxy() {
        return httpProxy;
    }

    public CardVpCrawlerConfig setHttpProxy(String httpProxy) {
        this.httpProxy = httpProxy;
        return this;
    }

    public String getHttpsProxy() {
        return httpsProxy;
    }

    public CardVpCrawlerConfig setHttpsProxy(String httpsProxy) {
        this.httpsProxy = httpsProxy;
        return this;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public CardVpCrawlerConfig setEndpoint(String endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    public String getNoProxy() {
        return noProxy;
    }

    public CardVpCrawlerConfig setNoProxy(String noProxy) {
        this.noProxy = noProxy;
        return this;
    }

    public Integer getMaxIdleConns() {
        return maxIdleConns;
    }

    public CardVpCrawlerConfig setMaxIdleConns(Integer maxIdleConns) {
        this.maxIdleConns = maxIdleConns;
        return this;
    }

    public String getSocks5Proxy() {
        return socks5Proxy;
    }

    public CardVpCrawlerConfig setSocks5Proxy(String socks5Proxy) {
        this.socks5Proxy = socks5Proxy;
        return this;
    }

    public Boolean getLogEnabled() {
        return logEnabled;
    }

    public CardVpCrawlerConfig setLogEnabled(Boolean logEnabled) {
        this.logEnabled = logEnabled;
        return this;
    }
}
