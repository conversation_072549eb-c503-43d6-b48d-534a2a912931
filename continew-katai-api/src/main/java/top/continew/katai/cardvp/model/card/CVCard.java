/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.cardvp.model.card;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class CVCard {

    /**
     * Primary address of the cardholder
     */
    private String address1;
    /**
     * Secondary address part (if any)
     */
    private String address2;
    /**
     * Amount associated with the card
     */
    private BigDecimal amount;
    /**
     * Current balance on the card
     */
    private BigDecimal balance;
    /**
     * Name on the card
     */
    private String cardname;
    /**
     * Card number sequence
     */
    private String cardnumber;
    /**
     * City of residence
     */
    private String city;
    /**
     * Card account closure date and time in format YYYY-MM-DD HH:mm:ss
     */
    private String closedate;
    /**
     * Country code in ISO format
     */
    private String country;
    /**
     * Card Verification Value (CVV)
     */
    private String cvv;
    /**
     * Card's expiration date in format MM/YY
     */
    private String expdate;
    /**
     * First name of the cardholder
     */
    private String firstname;
    /**
     * Last name of the cardholder
     */
    private String lastname;
    /**
     * Cardholder's contact number
     */
    private String phonenumber;
    /**
     * Platform or channel, Available: 'Facebook','tiktok': 'Tiktok','google': 'Google','apple':
     * 'Appple store','chatgpt': 'Chatgpt','amazon': 'Amazon'
     */
    private String platform;
    /**
     * Type of purchase made, Available: 'ADS', 'ECOMMERCE-COMMON', 'ECOMMERCE-ELSE'}
     */
    private String purchaseType;
    /**
     * Reference number for the transaction
     */
    private String referencenumber;
    /**
     * Any remarks or notes
     */
    private String remark;
    /**
     * Amount spent
     */
    private BigDecimal spent;
    /**
     * State of residence
     */
    private String state;
    /**
     * Card type
     */
    private String type;
    /**
     * ZIP code of cardholder's address
     */
    private String zip;
}
