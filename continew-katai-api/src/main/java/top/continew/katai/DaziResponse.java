/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

import okhttp3.Headers;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import top.continew.katai.exception.ThirdException;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DaziResponse {

    public Response response;
    public int statusCode;
    public String statusMessage;
    public HashMap<String, String> headers = new HashMap<>();
    public InputStream body;

    public DaziResponse() {
    }

    public DaziResponse(Response response) {
        this.response = response;
        this.statusCode = response.code();
        this.statusMessage = response.message();
        this.body = response.body().byteStream();
        Headers headers = response.headers();
        Map<String, List<String>> resultHeaders = headers.toMultimap();

        for (Map.Entry<String, List<String>> stringListEntry : resultHeaders.entrySet()) {
            this.headers.put(stringListEntry.getKey(), StringUtils.join(";", stringListEntry.getValue()));
        }

    }

    public InputStream getResponse() {
        return this.body;
    }

    public String getResponseBody() {
        if (null == this.body) {
            return String.format("{\"message\":\"%s\"}", this.statusMessage);
        } else {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            byte[] buff = new byte[4096];

            try {
                while (true) {
                    int read = this.body.read(buff);
                    if (read == -1) {
                        return os.toString();
                    }

                    os.write(buff, 0, read);
                }
            } catch (Exception e) {
                throw new ThirdException(e.getMessage(), e);
            }
        }
    }
}
