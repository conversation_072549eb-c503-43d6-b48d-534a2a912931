/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.okhttp;

import okhttp3.Authenticator;
import okhttp3.ConnectionPool;
import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.utils.Common;
import top.continew.katai.utils.TrueHostnameVerifier;
import top.continew.katai.utils.X509TrustManagerImp;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URL;
import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

public class OkHttpClientBuilder {

    private final OkHttpClient.Builder builder = (new OkHttpClient()).newBuilder();

    public OkHttpClientBuilder() {
    }

    public OkHttpClientBuilder connectTimeout(Integer connectTimeout) {
        if (connectTimeout != null) {
            this.builder.connectTimeout(connectTimeout, TimeUnit.MILLISECONDS);
        }
        return this;
    }

    public OkHttpClientBuilder readTimeout(Integer readTimeout) {
        if (readTimeout != null) {
            this.builder.readTimeout(readTimeout, TimeUnit.MILLISECONDS);
        }
        return this;
    }

    public OkHttpClientBuilder connectionPool(Integer maxIdleConnections) {
        if (maxIdleConnections == null) {
            maxIdleConnections = 5;
        }
        ConnectionPool connectionPool = new ConnectionPool(maxIdleConnections, 10000L, TimeUnit.MILLISECONDS);
        this.builder.connectionPool(connectionPool);
        return this;
    }

    public OkHttpClientBuilder certificate(boolean ignoreSSL) {
        try {
            if (ignoreSSL) {
                X509TrustManager compositeX509TrustManager = new X509TrustManagerImp();
                SSLContext sslContext = SSLContext.getInstance("TLS");
                sslContext.init(null, new TrustManager[] {compositeX509TrustManager}, new SecureRandom());
                this.builder.sslSocketFactory(sslContext.getSocketFactory(), compositeX509TrustManager)
                    .hostnameVerifier(new TrueHostnameVerifier());
            }

            return this;
        } catch (Exception e) {
            throw new ThirdException(e.getMessage(), e);
        }
    }

    public OkHttpClientBuilder proxy(String httpProxy, String httpsProxy) {
        try {
            if (!Common.empty(httpProxy) || !Common.empty(httpsProxy)) {
                Object urlString = Common.empty(httpProxy) ? httpsProxy : httpProxy;
                URL url = new URL(String.valueOf(urlString));
                this.builder.proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(url.getHost(), url.getPort())));
            }

            return this;
        } catch (Exception e) {
            throw new ThirdException(e.getMessage(), e);
        }
    }

    public OkHttpClientBuilder proxyAuthenticator(String httpsProxy) {
        try {
            if (!Common.empty(httpsProxy)) {
                URL proxyUrl = new URL(httpsProxy);
                String userInfo = proxyUrl.getUserInfo();
                if (null != userInfo) {
                    String[] userMessage = userInfo.split(":");
                    final String credential = Credentials.basic(userMessage[0], userMessage[1]);
                    Authenticator authenticator = (route, response) -> response.request()
                        .newBuilder()
                        .header("Proxy-Authorization", credential)
                        .build();
                    this.builder.proxyAuthenticator(authenticator);
                }
            }

            return this;
        } catch (Exception e) {
            throw new ThirdException(e.getMessage(), e);
        }
    }

    public OkHttpClient buildOkHttpClient() {
        return this.builder.followRedirects(false).build();
    }
}
