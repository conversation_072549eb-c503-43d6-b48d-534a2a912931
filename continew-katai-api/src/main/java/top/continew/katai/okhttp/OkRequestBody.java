/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.okhttp;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okio.BufferedSink;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import top.continew.katai.DaziRequest;

import java.io.IOException;
import java.io.InputStream;

public class OkRequestBody extends RequestBody {

    private final InputStream inputStream;

    private final String contentType;

    public OkRequestBody(DaziRequest okRequest) {
        this.inputStream = okRequest.body;
        this.contentType = okRequest.headers.get("content-type");
    }

    @Override
    public MediaType contentType() {
        if (StringUtils.isEmpty(this.contentType)) {
            if (null == this.inputStream) {
                return null;
            } else {
                return MediaType.parse("application/json; charset=UTF-8;");
            }
        } else {
            return MediaType.parse(this.contentType);
        }
    }

    @Override
    public long contentLength() throws IOException {
        return null != this.inputStream && this.inputStream.available() > 0
            ? (long)this.inputStream.available()
            : super.contentLength();
    }

    @Override
    public void writeTo(@NotNull BufferedSink bufferedSink) throws IOException {
        if (null != this.inputStream) {
            byte[] buffer = new byte[4096];

            int bytesRead;
            while ((bytesRead = this.inputStream.read(buffer)) != -1) {
                bufferedSink.write(buffer, 0, bytesRead);
            }

        }
    }
}
