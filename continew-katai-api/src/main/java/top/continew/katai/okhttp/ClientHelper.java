/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.okhttp;

import okhttp3.OkHttpClient;
import top.continew.katai.RuntimeOptions;
import top.continew.katai.utils.Common;

import java.net.URL;
import java.util.concurrent.ConcurrentHashMap;

public class ClientHelper {

    public static final ConcurrentHashMap<String, OkHttpClient> clients = new ConcurrentHashMap<>();

    public ClientHelper() {
    }

    public static OkHttpClient getOkHttpClient(String host, int port, RuntimeOptions options) throws Exception {
        String key;
        if (Common.empty(options.getHttpProxy()) && Common.empty(options.getHttpsProxy())) {
            key = getClientKey(host, port);
        } else {
            Object urlString = Common.empty(options.getHttpProxy()) ? options.getHttpsProxy() : options.getHttpProxy();
            URL url = new URL(String.valueOf(urlString));
            key = getClientKey(url.getHost(), url.getPort());
        }

        OkHttpClient client = clients.get(key);
        if (null == client) {
            client = creatClient(options);
            clients.put(key, client);
        }

        return client;
    }

    public static OkHttpClient creatClient(RuntimeOptions options) {
        OkHttpClientBuilder builder = new OkHttpClientBuilder();
        builder = builder.connectTimeout(options.getConnectTimeout())
            .readTimeout(options.getReadTimeout())
            .connectionPool(options.getMaxIdleConns())
            .certificate(options.getIgnoreSSL())
            .proxy(options.getHttpProxy(), options.getHttpsProxy())
            .proxyAuthenticator(options.getHttpsProxy());
        return builder.buildOkHttpClient();
    }

    public static String getClientKey(String host, int port) {
        return String.format("%s:%d", host, port);
    }
}
