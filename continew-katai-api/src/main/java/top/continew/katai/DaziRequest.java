/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class DaziRequest {

    public static final String URL_ENCODING = "UTF-8";
    public String protocol = "http";
    public Integer port;
    public String method = "GET";
    public String pathname;
    public Map<String, String> query = new HashMap<>();
    public Map<String, String> headers = new HashMap<>();
    public InputStream body;

    public DaziRequest() {
    }

    public static DaziRequest create() {
        return new DaziRequest();
    }

    @Override
    public String toString() {
        String output = "Protocol: " + this.protocol + "\nPort: " + this.port + "\n" + this.method + " " + this.pathname + "\n";
        output = output + "Query:\n";

        Iterator<Map.Entry<String, String>> var2;
        Map.Entry<String, String> e;
        for (var2 = this.query.entrySet().iterator(); var2.hasNext(); output = output + "    " + e.getKey() + ": " + e
            .getValue() + "\n") {
            e = var2.next();
        }

        output = output + "Headers:\n";

        for (var2 = this.headers.entrySet().iterator(); var2.hasNext(); output = output + "    " + e.getKey() + ": " + e
            .getValue() + "\n") {
            e = var2.next();
        }

        return output;
    }
}
