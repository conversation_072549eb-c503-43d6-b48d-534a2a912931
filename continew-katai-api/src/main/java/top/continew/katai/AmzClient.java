/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.atlassian.guava.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import top.continew.katai.amz.AmzConfig;
import top.continew.katai.amz.model.req.*;
import top.continew.katai.amz.model.resp.*;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.utils.AESUtil;
import top.continew.katai.utils.Common;
import top.continew.katai.utils.RemoteCommandExecutor;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * AMZ客户端
 *
 * <AUTHOR>
 * @since 2025/1/1
 */
@Slf4j
public class AmzClient extends Client {
    //限制API调用频率：3次/秒
    private final RateLimiter rateLimiter = RateLimiter.create(3, 1, TimeUnit.SECONDS);

    private final String appKey;
    private final String appId;
    private final String privateKey;
    private final String publicKey;

    public AmzClient(AmzConfig config) {
        super(config);
        this.appKey = config.getAppKey();
        this.appId = config.getAppId();
        this.publicKey = config.getPublicKey();
        this.privateKey = config.getPrivateKey();
    }

    /**
     * 获取卡片类型
     *
     * @return 卡片类型列表
     */


    public List<AmzCardTypeResponse> getCardTypes() {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/getCardTypes");
        Map<String, ?> result = this.callApi(params, null);
        log.info("result:{}", JSON.toJSONString(result));
        return Common.toModelList(result, "body", AmzCardTypeResponse.class);
    }

    /**
     * 获取交易记录
     *
     * @param request
     * @return
     */
    public AmzGetTransactionResponse getTransactions(AmzGetTransactionRequest request) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/transactions/get");
        return Common.toModel(this.callApi(params, request), "body", AmzGetTransactionResponse.class);
    }

    /**
     * 获取卡余额
     *
     * @param requestId
     * @return
     */
    public AmzCardBalanceResponse getCardBalance(String requestId) {
        JSONObject paramData = new JSONObject();
        paramData.put("request_id", requestId);
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/balance");
        return Common.toModel(this.callApi(params, paramData), "body", AmzCardBalanceResponse.class);
    }

    public AmzGetCardResponse getCards(AmzGetCardRequest request) {
        //加密
        request.setRam(AESUtil.generate16String());

        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/getCardList");
        AmzGetCardResponse resp = Common.toModel(this.callApi(params, request), "body", AmzGetCardResponse.class);

        if(null == resp) {
            throw new ThirdException(Map.of("code", "500", "message", "AMZ-获取卡片数据为空"));
        }

        if(StrUtil.isBlank(resp.getItem())) {
            return resp;
        }

        //解密
        String item = decodeTaskItem(resp.getItem(), request.getRam());
        // 将解密后的JSON转换为指定的类型
        if (StrUtil.isNotBlank(item)) {
            try {
                List<AmzCardResponse> dataList = JSON.parseArray(item, AmzCardResponse.class);
                resp.setCards(dataList);
            } catch (com.alibaba.fastjson2.JSONException e) {
                throw new ThirdException("JSON解析失败", e);
            }
        }

        return resp;
    }


    /**
     * 创建新卡
     *
     * @param request
     * @return
     */
    public AmzTaskResponse createCard(AmzCreateCardRequest request) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/create");
        return Common.toModel(this.callApi(params, request), "body", AmzTaskResponse.class);
    }


    /**
     * 充值
     * @param request
     * @return
     */
    public AmzTaskResponse recharge(AmzRechargeRequest request) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/recharge");
        return Common.toModel(this.callApi(params, request), "body", AmzTaskResponse.class);
    }




    /**
     * 获取任务详情
     * @param request 请求参数
     * @param clazz 解密后的数据类型
     * @return 任务响应
     */
    public <T> AmzTaskResponse<T> getTaskDetail(AmzGetTaskDetailRequest request, Class<T> clazz) {
        // Generate a 16-character random string for ram
        request.setRam(AESUtil.generate16String());
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/taskDetail");
        AmzTaskResponse<T> amzTaskResponse = Common.toModel(this.callApi(params, request), "body", AmzTaskResponse.class);

        if(StrUtil.isBlank(amzTaskResponse.getItem())) {
            return amzTaskResponse;
        }

        if(request.getTaskType().equals(1)) {
            amzTaskResponse.setItem(decodeTaskItem(amzTaskResponse.getItem(), request.getRam()));
        }

        // 将解密后的JSON转换为指定的类型
        if (StrUtil.isNotBlank(amzTaskResponse.getItem())) {
            try {
                // 如果解析单个对象失败，尝试解析为数组
                if (amzTaskResponse.getItem().startsWith("[")) {
                    List<T> dataList = JSON.parseArray(amzTaskResponse.getItem(), clazz);
                    // 如果数组不为空，取第一个元素
                    if (dataList != null && !dataList.isEmpty()) {
                        amzTaskResponse.setData(dataList.get(0));
                    }

                } else {
                    // 首先尝试解析为单个对象
                    T data = JSON.parseObject(amzTaskResponse.getItem(), clazz);
                    amzTaskResponse.setData(data);
                }

            } catch (com.alibaba.fastjson2.JSONException e) {
                throw new ThirdException("JSON解析失败", e);
            }
        }

        return amzTaskResponse;
    }



    /**
     * 作废卡
     * @param request
     * @return
     */
    public AmzInvalidResponse invalid(AmzInvalidRequest request) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/invalid");
        return Common.toModel(this.callApi(params, request), "body", AmzInvalidResponse.class);
    }

    /**
     * 获取作废卡处理接口
     * @param request
     * @return
     */
    public AmzInvalidDetailResponse getInvalidDetail(AmzInvalidRequest request) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/getInvalidDetail");
        return Common.toModel(this.callApi(params, request), "body", AmzInvalidDetailResponse.class);
    }



    /**
     * 获取 3DS 验证码公池接口
     * @return 3DS 验证码公池仅展示最近一小时的数据详情，有效期一般为：10 分钟；请自行针对需要验证码的卡号进行自主识别和辨别
     */
    public AmzAuthCodeResponse getAuthCode() {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/authorization/authCode");
        return Common.toModel(this.callApi(params, null), "body", AmzAuthCodeResponse.class);
    }


    /**
     * 卡余额提现
     */
    public AmzTaskResponse transferBalance(AmzTransferBalanceRequest request) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/transferBalance");
        return Common.toModel(this.callApi(params, request), "body", AmzTaskResponse.class);
    }


    /**
     * 卡余额提现处理结果
     */
    public AmzTransferBalanceDetailResponse transferBalanceDetail(AmzTransferBalanceDetailRequest request) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/api/v1/card/transferBalanceDetail");
        return Common.toModel(this.callApi(params, request) , "body", AmzTransferBalanceDetailResponse.class);
    }


    private Map<String, ?> callApi(Params params, Object paramData) {
        if (rateLimiter.tryAcquire(30, TimeUnit.SECONDS)) {
            JSONObject requestBody = new JSONObject();
            requestBody.put("app_id", this.appId);
            requestBody.put("app_key", this.appKey);
            // yyyyMMddHHmmss
            Date date = new Date();
            // 20位的请求流水号
            requestBody.put("request_no", DateUtil.format(date, "yyyyMMddHHmmss") + String.format("%06d", RandomUtil.randomInt(1000000)));
            //请求报文时间戳，格式"yyyy-MM-dd HH:mm:ss"
            requestBody.put("timestamp", DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"));
            //签名类型：填写 RSA2
            requestBody.put("sign_type", "RSA2");
            requestBody.put("requestBody", JSON.toJSONString(paramData));
            //签名值 ，通过商户 RSA2 私钥进行对参数加密后的值
            requestBody.put("sign", generateSign(requestBody));

            OpenApiRequest apiRequest = new OpenApiRequest();
            apiRequest.setHeaders(new HashMap<>());
            apiRequest.setBody(requestBody);
            apiRequest.addHeader("Content-Type", "application/json");
            RuntimeOptions runtime = new RuntimeOptions();
            return doCallApi(params, apiRequest, runtime);
        }else {
            throw new ThirdException(Map.of("code", "429", "message", "接口限流，请稍后再试"));
        }
    }

    private Map<String, ?> doCallApi(Params params, OpenApiRequest apiRequest, RuntimeOptions runtime) {
        log.info("AMZ-请求URL:{},{}", params.getMethod(), params.getPathname());
        log.info("AMZ-请求body:{}", JSON.toJSONString(apiRequest.getBody()));
        log.info("AMZ-请求query:{}", JSON.toJSONString(apiRequest.getQuery()));
        log.info("AMZ-请求headers:{}", JSON.toJSONString(apiRequest.getHeaders()));



        String responseBody;

        if(isLocalEnvironment()) {
            String curlCommand = String.format(
                    "curl -X POST 'https://%s%s' \\\n" +
                            "  -H 'Content-Type: application/json' \\\n" +
                            "  %s \\\n" +
                            "  -d '%s'",
                    config.endpoint,
                    params.getPathname(),
                    null == apiRequest.getHeaders() ? "" : apiRequest.getHeaders().entrySet().stream()
                            .map(e -> String.format("-H '%s: %s'", e.getKey(), e.getValue()))
                            .collect(Collectors.joining(" \\\n  ")),
                    JSON.toJSONString(apiRequest.getBody()).replace("'", "'\\''")
            );
            log.info("AMZ-curl命令:\n{}", curlCommand);

            // 使用远程执行器执行curl命令
            RemoteCommandExecutor.SSHConfig sshConfig = RemoteCommandExecutor.SSHConfig.builder()
                    .host("*************")
                    .username("")
                    .password("")
                    .timeout(60000)
                    .port(22)
                    .build();

            responseBody = RemoteCommandExecutor.executeCommand(sshConfig, curlCommand);

        }else {
            Map<String, ?> response = this.doRequest(params, apiRequest, runtime);
            log.info("response:{}", JSON.toJSONString(response));
            responseBody = JSON.toJSONString(response.get("body"));
        }

        JSONObject data = JSON.parseObject(responseBody);
        int code = data.getIntValue("code");
        if (code != 10000) {
            throw new ThirdException(Map.of("code", data.get("code"), "message", data.get("msg")));
        }

        Map<String, Object> result = new HashMap<>();
        result.put("body", data.getString("responseBody"));
        return result;
    }


    private String generateSign(JSONObject params) {
        try {
            // Remove sign field and get sorted keys
            List<String> keys = new ArrayList<>(params.keySet());
            keys.remove("sign");
            Collections.sort(keys);

            // Build key=value string
            StringBuilder content = new StringBuilder();
            for (String key : keys) {
                Object value = params.get(key);
                if (value != null && !value.toString().isEmpty()) {
                    if (content.length() > 0) {
                        content.append("&");
                    }
                    content.append(key).append("=").append(value);
                }
            }

            // Sign using RSA2 private key
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(getPrivateKey(this.privateKey));
            signature.update(content.toString().getBytes(java.nio.charset.StandardCharsets.UTF_8));
            byte[] signed = signature.sign();

            // Convert to Base64
            return Base64.getEncoder().encodeToString(signed);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate signature", e);
        }
    }

    private String decodeTaskItem(String item, String ram) {
        //item : 需要商户通过 base64_decode 后，使用 AES 通过 ram 私钥进行解密，vi 向量的值（平台提供），解密出来的是 json 数据
        //vi 向量：SADEUT78WE23HGKW
        //AES 使用 AES-128-CFB、无填充 模式 （AES/CFB/NoPadding）

        try {
            // 固定的IV向量值
            final String IV = "SADEUT78WE23HGKW";
            // 使用AES-128-CFB模式解密
            return AESUtil.decryptCFB(item, ram, IV);
        } catch (Exception e) {
            log.error("Failed to decode task item", e);
            throw new ThirdException("任务信息解密失败", e);
        }
    }


    /**
     * 判断是否为本地环境
     *
     * @return 是否为本地环境
     */
    private boolean isLocalEnvironment() {
        return false;
    }


    /**
     * 获取私钥对象
     */
    public static PrivateKey getPrivateKey(String privateKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 获取公钥对象
     */
    public static PublicKey getPublicKey(String publicKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }
}
