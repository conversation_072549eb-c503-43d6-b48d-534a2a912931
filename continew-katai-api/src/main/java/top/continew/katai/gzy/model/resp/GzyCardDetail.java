package top.continew.katai.gzy.model.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/12 17:51
 */
@Data
public class GzyCardDetail {
    /**
     * 卡ID，每张卡的唯一编号
     */
    private String cardId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 掩码卡号
     */
    private String maskCardNo;

    /**
     * 卡本币
     */
    private String cardCurrency;

    /**
     * 卡昵称
     */
    private String nickname;

    /**
     * 卡组织（枚举值：MasterCard、Discover）
     */
    private String cardScheme;

    /**
     * 卡状态（具体中文描述可查看发卡状态）
     * Enum: "normal" "pending_recharge" "freezing" "frozen" "risk_frozen" "system_frozen" "unfreezing" "expired" "canceling" "cancelled" "unactivated"
     */
    private String cardStatus;

    /**
     * 卡类型：
     * - share：共享卡
     * - recharge：常规卡
     */
    private String cardType;

    /**
     * CVV（卡片安全码）
     */
    private String cvv;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 卡有效期，格式为 MM/YY
     */
    private String expirationDate;

    /**
     * 名字
     */
    private String firstName;

    /**
     * 姓氏
     */
    private String lastName;

    /**
     * 会员ID（如涉及matrix产品，返回对应的连接会员信息）
     */
    private String memberId;

    /**
     * Matrix账户号
     */
    private String matrixAccount;

    /**
     * 日交易限额
     */
    private Integer maxOnDaily;

    /**
     * 月交易限额
     */
    private Integer maxOnMonthly;

    /**
     * 单笔交易最大金额
     */
    private Integer maxOnPercent;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 手机号前缀
     */
    private String mobilePrefix;

    /**
     * 国籍（国家码二字码）
     */
    private String nationality;

    /**
     * 交易限额类型：
     * - limited：有限制
     * - unlimited：无限制
     */
    private String transactionLimitType;

    /**
     * 可交易额度
     */
    private BigDecimal availableTransactionLimit;

    /**
     * 总交易限额。此卡至今为止设置的所有交易限额汇总。此值不可作为卡内可用交易限额来参考。
     */
    private BigDecimal totalTransactionLimit;


    /**
     * 常规卡金额
     */
    private BigDecimal cardBalance;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 用卡人ID
     */
    private String cardholderId;
}
