package top.continew.katai.gzy;


import top.continew.katai.ThirdConfig;

public class GzyConfig extends ThirdConfig {

    public String appId;

    public String appSecret;

    public String privateKey;

    public String gzyPublicKey;

    public String getGzyPublicKey() {
        return gzyPublicKey;
    }

    public void setGzyPublicKey(String gzyPublicKey) {
        this.gzyPublicKey = gzyPublicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }



    public String getProtocol() {
        return protocol;
    }

    public GzyConfig setProtocol(String protocol) {
        this.protocol = protocol;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public GzyConfig setMethod(String method) {
        this.method = method;
        return this;
    }

    public Integer getReadTimeout() {
        return readTimeout;
    }

    public GzyConfig setReadTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
        return this;
    }

    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    public GzyConfig setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }

    public String getHttpProxy() {
        return httpProxy;
    }

    public GzyConfig setHttpProxy(String httpProxy) {
        this.httpProxy = httpProxy;
        return this;
    }

    public String getHttpsProxy() {
        return httpsProxy;
    }

    public GzyConfig setHttpsProxy(String httpsProxy) {
        this.httpsProxy = httpsProxy;
        return this;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public GzyConfig setEndpoint(String endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    public String getNoProxy() {
        return noProxy;
    }

    public GzyConfig setNoProxy(String noProxy) {
        this.noProxy = noProxy;
        return this;
    }

    public Integer getMaxIdleConns() {
        return maxIdleConns;
    }

    public GzyConfig setMaxIdleConns(Integer maxIdleConns) {
        this.maxIdleConns = maxIdleConns;
        return this;
    }

    public String getSocks5Proxy() {
        return socks5Proxy;
    }

    public GzyConfig setSocks5Proxy(String socks5Proxy) {
        this.socks5Proxy = socks5Proxy;
        return this;
    }

    public Boolean getLogEnabled() {
        return logEnabled;
    }

    public GzyConfig setLogEnabled(Boolean logEnabled) {
        this.logEnabled = logEnabled;
        return this;
    }
    
    
}
