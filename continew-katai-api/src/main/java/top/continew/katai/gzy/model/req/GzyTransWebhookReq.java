package top.continew.katai.gzy.model.req;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 18:31
 */
@Data
public class GzyTransWebhookReq {
    private String memberId;                     // 会员号
    private String matrixAccount;                // matrix账户号
    private String createdAt;                    // 创建时间
    private String updatedAt;                    // 更新时间
    private String cardId;                       // 卡ID
    private String cardType;                     // 卡类型
    private String transactionId;                // 交易ID
    private String originTransactionId;          // 原始交易ID
    private String requestId;                    // 商户请求ID
    private String transactionType;              // 交易类型
    private String status;                       // 状态
    private String code;                         // 状态码
    private String msg;                          // 状态描述
    private String mcc;                          // 商户类别码
    private String authCode;                     // 授权码
    private BigDecimal transactionAmount;        // 交易金额
    private String transactionCurrency;          // 交易币种
    private String txnPrincipalChangeAccount;    // 交易本金变动账户
    private BigDecimal txnPrincipalChangeAmount; // 交易本金预授权金额
    private String txnPrincipalChangeCurrency;   // 交易本金变动币种
    private String feeDeductionAccount;          // 手续费扣费账户
    private BigDecimal feeDeductionAmount;       // 手续费扣费金额
    private String feeDeductionCurrency;         // 手续费扣费币种
    private JSONObject feeDetailJson;            // 手续费扣费金额明细
    private String feeReturnAccount;             // 手续费返还账户
    private BigDecimal feeReturnAmount;          // 手续费返还金额
    private String feeReturnCurrency;            // 手续费返还币种
    private JSONObject feeReturnDetailJson;      // 手续费返还金额明细
    private String arrivalAccount;               // 到账账户
    private BigDecimal arrivalAmount;            // 到账金额
    private String merchantNameLocation;         // 商户名称
    private BigDecimal cardBalance;              // 常规卡金额
    private BigDecimal availableTransactionLimit; // 共享卡可交易额度

    /**
     {
  "arrivalAccount": "card",
  "arrivalAmount": "28.000000",
  "cardId": "*********************",
  "code": "0000",
  "createdAt": "2021-06-01T09:55:04",
  "updatedAt": "2021-06-01T09:55:04",
  "feeDeductionAccount": "card",
  "feeDeductionAmount": "-2.000000",
  "feeDeductionCurrency": "USD",
  "feeDetailJson": {
    "transactionFeeAmount": -2
  },
  "feeReturnAccount": "",
  "feeReturnAmount": "0",
  "feeReturnCurrency": "",
  "feeReturnDetailJson": {},
  "matrixAccount": "",
  "memberId": "****************",
  "merchantNameLocation": "",
  "msg": "succeed",
  "mcc": 1234,
  "authCode": 123456,
  "originTransactionId": "",
  "requestId": "**************",
  "status": "succeed",
  "transactionAmount": "30",
  "transactionCurrency": "USD",
  "transactionId": "*********************",
  "transactionType": "recharge",
  "txnPrincipalChangeAccount": "member",
  "txnPrincipalChangeAmount": "-30.000000",
  "txnPrincipalChangeCurrency": "USD"
}
     */
}
