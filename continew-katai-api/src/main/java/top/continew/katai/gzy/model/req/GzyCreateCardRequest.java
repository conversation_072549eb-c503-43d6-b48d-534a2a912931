package top.continew.katai.gzy.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/12 17:39
 */
@Getter
@Setter
@Builder
public class GzyCreateCardRequest {
    /** 请求所指向的会员号，如不传则默认为token下的会员；若涉及matrix产品，可传入指定的连接会员号 */
    private String memberId;

    /** 如果你想在matrix下申请卡，请填写matrix账号。如果不填，就在会员下默认创建 */
    private String matrixAccount;

    /** 您需要填入你想开卡的卡bin信息，目前支持的卡bin信息可在卡bin接口中查询 */
    private String cardBin;

    /** 卡本币，支持的枚举值: "USD", "EUR", "GBP" */
    private String cardCurrency;

    /** 卡有效期，以月为计数单位。最小12个月，最大35个月，默认由系统自动分配 */
    private Integer cardExpirationDate;

    /** 卡组织，支持的枚举值: "MasterCard", "Discover" */
    private String cardScheme;

    /** 卡类型，支持的枚举值: "share"（共享卡）、"recharge"（常规卡） */
    private String cardType;

    /** 填入用卡人id后，虚拟卡将属于此用卡人。如不填则默认使用默认持卡人信息进行开卡 */
    private String cardholderId;

    /** 日交易限额。为空时不做限制，非空时大于1。默认最高限额为20000USD */
    private Integer maxOnDaily;

    /** 月交易限额。为空时不做限制，非空时大于1。默认最高限额为20000USD */
    private Integer maxOnMonthly;

    /** 单笔交易最大金额。为空时不做限制，非空时大于1。默认最高限额为20000USD */
    private Integer maxOnPercent;

    /** 转入金额，您想从币种光子易账户金额往卡里转入的资金 */
    private BigDecimal rechargeAmount;

    /** 商户请求流水号，每笔交易的唯一请求号，不可重复 */
    private String requestId;

    /** 可交易额度。transactionLimitType值为limited时，不能为空 */
    private BigDecimal transactionLimit;

    /** 是否限制可交易额度，默认unlimited。支持的枚举值: "limited", "unlimited" */
    private String transactionLimitType;

    /** 您需要用于转入的币种光子易账户ID号 */
    private String accountId;

    /** 到账金额，您希望转入到账金额是多少 */
    private BigDecimal arrivalAmount;

}
