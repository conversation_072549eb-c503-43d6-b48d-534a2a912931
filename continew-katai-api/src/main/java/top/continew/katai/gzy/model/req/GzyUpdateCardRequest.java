package top.continew.katai.gzy.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/12 17:39
 */
@Getter
@Setter
@Builder
public class GzyUpdateCardRequest {
    /** 卡ID */
    private String cardId;

    /** 商户请求流水号，每笔交易的唯一请求号，不可重复 */
    private String requestId;


    /** 日交易限额。为空时不做限制，非空时大于1。默认最高限额为20000USD */
    private Integer maxOnDaily;

    /** 月交易限额。为空时不做限制，非空时大于1。默认最高限额为20000USD */
    private Integer maxOnMonthly;

    /** 单笔交易最大金额。为空时不做限制，非空时大于1。默认最高限额为20000USD */
    private Integer maxOnPercent;


    /** 昵称，为空时不更新。 */
    private String nickname;

    /** 可交易额度。transactionLimitType值为limited时，不能为空 */
    private BigDecimal transactionLimit;

    /** 是否限制可交易额度，默认unlimited。支持的枚举值: "limited", "unlimited" */
    private String transactionLimitType;

    /** 可交易额度变动类型，销卡后不可再操作调增，可以调减。支持的枚举值："increase" "decrease"*/
    private String transactionLimitChangeType;



}
