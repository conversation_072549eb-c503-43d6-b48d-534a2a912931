package top.continew.katai.gzy.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class GzyGetCardRequest {
    /**
     * 当前页/默认第一页
     */
    private Integer pageIndex;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 查询所指向的会员号，如不传则默认为token下的会员；
     * 如涉及matrix产品，如不传则指向会员和连接会员所有信息查询，
     * 或传入指定会员查询。
     */
    private String memberId;

    /**
     * 卡bin。您可输入卡bin进行筛选。如需筛选多个卡bin，每个卡bin之间逗号分隔即可。
     */
    private String cardBin;

    /**
     * Matrix账户ID
     */
    private String matrixAccount;

    /**
     * 起始时间
     * Example: createdAtStart=2022-03-18T08:43:28
     */
    private String createdAtStart;

    /**
     * 结束时间
     * Example: createdAtEnd=2022-03-18T08:43:28
     */
    private String createdAtEnd;



    /**
     * 卡类型。值: share 或 recharge 可为空。如为空，则查询结果将显示所有类型的卡。
     */
    private String cardType;


    /**
     * string
     * Enum: "normal" "pending_recharge" "freezing" "frozen" "risk_frozen" "system_frozen" "unfreezing" "expired" "canceling" "cancelled" "unactivated"
     * 卡状态
     */
    private String cardStatus;
}
