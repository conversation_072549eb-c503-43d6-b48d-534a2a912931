package top.continew.katai.gzy.model.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 09:38
 */
@Data
public class GzyShareCardTxnLimitDetail {
    // 创建时间
    private String createdAt; // 例如: "2022-03-18T08:43:28"

    // 卡ID
    private String cardId;

    // 交易ID
    private String transactionId;

    // 交易类型
    // Enum: "auth", "verification", "auth_failed_return", "void", "refund",
    // "refund_reversal", "corrective_auth", "corrective_refund",
    // "corrective_refund_void", "limit_adjustment", "service_fee",
    // "fund_in", "settlement_spread"
    private String transactionType;

    // 金额
    private BigDecimal amount;

    // 可用交易额度
    private BigDecimal availableTransactionLimit;

    // 额度流向。
    // Enum: "transfer_in" (增加), "transfer_out" (减少), "unlimited" (不限)
    private String capitalFlows;

    // 卡本币
    private String cardCurrency;

    // 总变动金额
    private BigDecimal changeAmount;

    // 手续费金额
    private BigDecimal feeAmount;

    // 掩码卡号
    private String maskCardNo;

    // 交易额度模式。
    // Enum: "txn" (仅交易金额), "txn_fee" (含交易金额和手续费)
    private String txnLimitMethod;

    // 会员ID；如涉及matrix产品，返回对应的连接会员信息。
    private String memberId;

    // Matrix账户ID
    private String matrixAccount;
}
