package top.continew.katai.gzy.model.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 09:13
 */
@Data
public class GzyTransactionItem {
    /** 会员ID；如涉及matrix产品，返回对应的连接会员信息 */
    private String memberId;

    /** Matrix账户ID */
    private String matrixAccount;

    /** 创建时间 */
    private String createdAt;

    /** 卡ID */
    private String cardId;

    /** 卡类型。share：共享卡； recharge：常规卡 */
    private String cardType;

    /** 卡本币 */
    private String cardCurrency;

    /** 交易ID */
    private String transactionId;

    /** 原始交易ID */
    private String originTransactionId;

    /** 商户请求ID (所有交易以及请求的唯一标识，建议您保存。可用于对应的操作) */
    private String requestId;

    /** 交易类型，具体交易类型中文说明可以查看 发卡交易类型枚举值 */
    private String transactionType;

    /** 状态 */
    private String status;

    /** 状态码 */
    private String code;

    /** 状态描述 */
    private String msg;

    /** 商户类别码 */
    private String mcc;

    /** 授权码 */
    private String authCode;

    /** 结算状态 */
    private String settleStatus;

    /** 交易金额 */
    private BigDecimal transactionAmount;

    /** 交易币种 */
    private String transactionCurrency;

    /**
     * 余额
     */
    private BigDecimal availableTransactionLimit;

    /** 交易本金变动账户。member：会员光子易账户；matrix：Matrix账户；card：常规卡账户 */
    private String txnPrincipalChangeAccount;

    /** 交易本金预授权金额 */
    private BigDecimal txnPrincipalChangeAmount;

    /** 交易本金变动币种 */
    private String txnPrincipalChangeCurrency;

    /** 交易本金已结算金额 */
    private BigDecimal txnPrincipalChangeSettledAmount;

    /** 结算价差变动账户。member：会员光子易账户；matrix：Matrix账户；card：常规卡账户 */
    private String settleSpreadChangeAccount;

    /** 结算价差变动币种 */
    private String settleSpreadChangeCurrency;

    /** 手续费扣费账户。member：会员光子易账户；matrix：Matrix账户；card：常规卡账户 */
    private String feeDeductionAccount;

    /** 手续费扣费金额 */
    private BigDecimal feeDeductionAmount;

    /** 手续费扣费币种 */
    private String feeDeductionCurrency;

    /** 手续费扣费金额明细 */
    private String feeDetailJson;

    /** 手续费返还账户。member：会员光子易账户；matrix：Matrix账户；card：常规卡账户 */
    private String feeReturnAccount;

    /** 手续费返还金额 */
    private BigDecimal feeReturnAmount;

    /** 手续费返还币种 */
    private String feeReturnCurrency;

    /** 手续费返还金额明细 */
    private String feeReturnDetailJson;

    /** 到账账户。member：会员光子易账户；matrix：Matrix账户；card：常规卡账户 */
    private String arrivalAccount;

    /** 到账金额 */
    private BigDecimal arrivalAmount;

    /** 前六后四格式的掩码卡号 */
    private String maskCardNo;

    /** 商户名称 */
    private String merchantNameLocation;
}
