package top.continew.katai.gzy.model.resp;

import lombok.Data;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/11 15:55
 */
@Data
public class GzyCardBinResponse {
    /**
     * 卡bin。
     */
    private String cardBin;
    /**
     * 卡bin支持的币种。多个币种用逗号分隔。
     */
    private String cardCurrency;
    /**
     * 卡组织
     * Enum: "MasterCard" "Discover"
     */
    private String cardScheme;
    /**
     * 当值为'Y'此卡支持更新账单地址；值为'N'则此卡不支持变更账单地址。
     */
    private String billingAddressUpdatable;
    /**
     * 卡类型。share： 共享卡； recharge： 常规卡； share,recharge: 既支持常规卡也支持共享卡。
     */
    private String cardType;
    /**
     * 有效期自定义
     */
    private String expiryDateCustomization;
    /**
     * 此卡BIN当前剩余可开卡的数量，Unlimited 为不限制
     */
    private String remainingAvailableCard;
    /**
     * 此卡BIN 已开卡并可用的卡数量（不包含销卡及过期）
     */
    private String availableCard;
}
