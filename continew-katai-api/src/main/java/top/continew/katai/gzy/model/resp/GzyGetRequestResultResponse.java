package top.continew.katai.gzy.model.resp;

import lombok.Data;

@Data
public class GzyGetRequestResultResponse {
    private GzyCardDetail cardDetail;

    /**
     *
     * String
     * 商户请求流水号，每笔交易的唯一请求号，不可重复。(所有交易以及请求的唯一标识，建议您保存。可用于对应的操作。)
     */
    private String requestId;

    /**
     *
     * string
     * Enum: "pending" "pending_recharge" "succeed" "failed"
     * 请求状态
     */
    private String status;
}
