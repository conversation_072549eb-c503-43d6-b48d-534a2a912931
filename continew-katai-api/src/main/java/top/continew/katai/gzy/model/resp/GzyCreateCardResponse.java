package top.continew.katai.gzy.model.resp;

import lombok.Data;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/12 17:51
 */
@Data
public class GzyCreateCardResponse {
    /**
     * 卡对象
     */
    private GzyCardDetail cardDetail;

    /**
     * 商户请求流水号，每笔交易的唯一请求号，不可重复
     */
    private String requestId;

    /**
     * 请求状态：
     * - pending：待处理
     * - pending_recharge：待充值
     * - succeed：成功
     * - failed：失败
     */
    private String status;

}
