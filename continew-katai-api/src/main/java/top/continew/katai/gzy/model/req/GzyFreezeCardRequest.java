package top.continew.katai.gzy.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/12 17:39
 */
@Getter
@Setter
@Builder
public class GzyFreezeCardRequest {
    /** 卡ID */
    private String cardId;

    /** 商户请求流水号，每笔交易的唯一请求号，不可重复 */
    private String requestId;


    /**
     * freeze：冻结卡； unfreeze：解冻卡。
     */
    private String status;


}
