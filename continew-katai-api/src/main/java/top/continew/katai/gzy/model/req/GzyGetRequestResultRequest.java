package top.continew.katai.gzy.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/12 17:39
 */
@Getter
@Setter
@Builder
public class GzyGetRequestResultRequest {
    /** 请求所指向的会员号，如不传则默认为token下的会员；若涉及matrix产品，可传入指定的连接会员号 */
    private String memberId;

    /**
     *
     * string
     * Enum: "apply_card" "card_update" "card_freeze"
     * 请求类型。为空时，默认查询开卡结果
     */
    private String type;

    /** 商户请求流水号，每笔交易的唯一请求号，不可重复 */
    private String requestId;
}
