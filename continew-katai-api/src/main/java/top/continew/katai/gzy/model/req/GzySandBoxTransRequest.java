package top.continew.katai.gzy.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 10:01
 */
@Getter
@Setter
@Builder
public class GzySandBoxTransRequest {
    /**
     * 商户请求流水号。
     */
    private String requestId; // required, string(64)

    /**
     * 每张卡的唯一 ID 号。
     */
    private String cardID; // required, string(64)

    /**
     * CVV。
     */
    private String cvv; // required, string(4)

    /**
     * 卡的有效期，格式为 "MM/YY"。
     */
    private String expirationDate; // required, string(11)

    /**
     * 当交易类型为 void 或 refund 时要填写。
     */
    private String originTransactionId; // string(64)

    /**
     * ISO 4217 货币代码。
     */
    private String txnCurrency; // required, string(11)

    /**
     * 交易金额，请根据币种填写正确的金额。
     */
    private BigDecimal txnAmount; // required, bigDecimal

    /**
     * Enum: "auth" "void" "refund"，请根据您的使用场景填入响应的交易类型。
     */
    private String txnType; // required, string(11)

    /**
     * MCC。
     */
    private String mcc; // required, string(11)

    /**
     * 值: "Amazon"
     */
    private String merchantName; // required, string(50)

    /**
     * 国家二字码，值: "US"
     */
    private String merchantCountry; // required, string(2)

    /**
     * 国家对应的城市，值: "Newyork"
     */
    private String merchantCity; // required, string(100)

    /**
     * 邮编，值: "10001"
     */
    private String merchantPostcode; // required, string(11)
}
