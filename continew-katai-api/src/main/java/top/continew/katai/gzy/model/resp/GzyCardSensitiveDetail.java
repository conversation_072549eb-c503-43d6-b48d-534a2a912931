package top.continew.katai.gzy.model.resp;

import lombok.Data;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/12 17:51
 */
@Data
public class GzyCardSensitiveDetail {
    /**
     * 卡ID，每张卡的唯一编号
     */
    private String cardId;

    /**
     * 卡号
     */
    private String cardNo;


    /**
     * CVV（卡片安全码）
     */
    private String cvv;


    /**
     * 卡有效期，格式为 MM/YY
     */
    private String expirationDate;

}
