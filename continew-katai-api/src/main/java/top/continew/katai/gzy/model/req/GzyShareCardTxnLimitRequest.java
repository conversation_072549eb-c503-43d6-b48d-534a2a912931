package top.continew.katai.gzy.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 09:33
 */
@Getter
@Setter
@Builder
public class GzyShareCardTxnLimitRequest {
    /**
     * 当前页 / 默认第一页
     */
    private Integer pageIndex;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 查询所指向的会员号，如不传则默认为token下的会员；如涉及matrix产品，如不传则指向会员和连接会员所有信息查询，或传入指定会员查询。
     */
    private String memberId;

    /**
     * Matrix账户ID
     */
    private String matrixAccount;

    /**
     * 起始时间, 示例: 2022-03-18T08:43:28
     */
    private String createdAtStart;

    /**
     * 结束时间, 示例: 2022-03-18T08:43:28
     */
    private String createdAtEnd;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 卡ID
     */
    private String cardId;

    /**
     * 交易类型
     * 可选值: "auth", "verification", "auth_failed_return", "void", "refund", "refund_reversal", "corrective_auth", "corrective_refund", "corrective_refund_void", "limit_adjustment", "service_fee", "fund_in", "settlement_spread"
     */
    private String transactionType;
}
