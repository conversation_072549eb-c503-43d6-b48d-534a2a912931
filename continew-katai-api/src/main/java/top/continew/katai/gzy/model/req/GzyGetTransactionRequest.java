package top.continew.katai.gzy.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class GzyGetTransactionRequest {
    /**
     * 当前页/默认第一页
     */
    private Integer pageIndex;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 查询所指向的会员号，如不传则默认为token下的会员；
     * 如涉及matrix产品，如不传则指向会员和连接会员所有信息查询，
     * 或传入指定会员查询。
     */
    private String memberId;

    /**
     * Matrix账户ID
     */
    private String matrixAccount;

    /**
     * 起始时间
     * Example: createdAtStart=2022-03-18T08:43:28
     */
    private String createdAtStart;

    /**
     * 结束时间
     * Example: createdAtEnd=2022-03-18T08:43:28
     */
    private String createdAtEnd;

    /**
     * 卡ID
     */
    private String cardId;

    /**
     * 卡类型。值: share 或 recharge 可为空。如为空，则查询结果将显示所有类型的卡。
     */
    private String cardType;

    /**
     * 商户请求流水号，每笔交易的唯一请求号，不可重复。
     */
    private String requestId;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 您可对交易类型进行筛选，多个交易类型用逗号分隔。
     */
    private String transactionType;

    /**
     * 您可对交易状态进行筛选，多个交易状态用逗号分隔。
     */
    private String status;
}
