/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.transaction;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class HTGetTransactionsRequest {
    /**
     * 卡片币种 Enum: "USD" "EUR" "GBP" "JPY" "HKD"
     */
    private String cardCurrencyCode;
    /**
     * 卡号, 支持用后四位查询
     */
    private String cardNumber;
    /**
     * 卡片状态 Enum: "正常" "预注销" "已注销"
     */
    private String cardStatus;
    /**
     * 授权交易结束时间 Example: end=2022-10-01
     */
    private String end;
    /**
     * 每页数量 Enum: 25 50 100, Default: 25
     */
    private Integer limit;
    /**
     * 商户信息模糊匹配
     */
    private String merchant;
    /**
     * 授权币种 Enum: "USD" "EUR" "GBP" "JPY" "OTHER"
     */
    private String originalCurrencyCode;
    /**
     * 最大交易金额, 必须和授权币种一起使用
     */
    private String originalMaxAmount;
    /**
     * 最小交易金额, 必须和授权币种一起使用
     */
    private String originalMinAmount;
    /**
     * 当前页数 >= 1,Default: 1
     */
    private Integer page;
    /**
     * 结算币种 Enum: "USD" "CNY"
     */
    private String settlementCurrencyCode;
    /**
     * 授权交易开始时间 Example: start=2022-10-01
     */
    private String start;
    /**
     * 授权交易状态 Enum: "交易成功" "交易失败"
     */
    private String status;
    /**
     * 交易ID
     */
    private String transactionId;
    /**
     * 授权交易类型 Enum: "消费授权" "退款授权" "消费授权冲正" "退款授权冲正" "授权查询"
     */
    private String type;
}
