/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.customer;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class HTGetBalanceRecordRequest {

    /**
     * 卡号
     */
    private String cardNumber;
    /**
     * 余额币种 Enum: "USD" "CNY"
     */
    private String currencyCode;
    /**
     * 结束时间, 和开始时间相差不能超过3个月 Example: end=2022-09-13
     */
    private String end;
    /**
     * 每页数量 Enum: 25 50 100, Default: 25
     */
    private Integer limit;
    /**
     * 当前页数 >= 1,Default: 1
     */
    private Integer page;
    /**
     * 开始时间 Example: start=2022-09-13
     */
    private String start;
    /**
     * 余额明细类型 Enum: "开卡" "预销卡" "销卡" "锁卡" "解锁" "账户充值" "资金转出" "额度分配" "卡片充值" "卡片自动充值" "卡片充值回退"
     * "卡片自动充值回退" "销卡退款" "提现" "提现回退" "账户回收" "余额调账" "汇率差调账" "授权交易" "授权回退" "退款" "退款回退" "退款结算"
     * "账户充值手续费" "资金转出手续费" "开卡手续费" "开卡手续费回退" "授权手续费" "授权手续费回退" "销卡手续费" "销卡手续费回退" "卡片充值手续费"
     * "卡片充值手续费回退" "卡片提现手续费" "卡片提现手续费回退" "退款手续费" "低额授权手续费" "低额授权手续费回退"
     */
    private String type;
}
