/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.customer;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HTProduct {

    /**
     * 卡组织
     */
    private String association;
    /**
     * 授权手续费币种
     */
    private String authorizationFeeCurrencyCode;
    /**
     * 授权手续费起征点
     */
    private String authorizationFeeThreshold;
    /**
     * 授权手续费类型, Enum: 1 2 1表示按百分比收费 2表示按值收费
     */
    private Integer authorizationFeeType;
    /**
     * 授权手续费
     */
    private String authorizationFeeValue;
    /**
     * BIN
     */
    private String bin;
    /**
     * 产品取消描述
     */
    private String cancellationDescription;
    /**
     * 销卡费币种
     */
    private String cancellationFeeCurrencyCode;
    /**
     * 销卡费类型, Enum: 1 2 1表示按百分比收费 2表示按值收费
     */
    private Integer cancellationFeeType;
    /**
     * 销卡手续费
     */
    private String cancellationFeeValue;
    /**
     * 开卡币种
     */
    private String cardCurrencyCodes;
    /**
     * 已使用数量
     */
    private Integer cardQuantity;
    /**
     * 开卡手续费币种
     */
    private String creationFeeCurrencyCode;
    /**
     * 开卡手续费类型, Enum: 1 2 1表示按百分比收费 2表示按值收费
     */
    private Integer creationFeeType;
    /**
     * 开卡手续费
     */
    private String creationFeeValue;
    /**
     * 充值手续费币种
     */
    private String depositFeeCurrencyCode;
    /**
     * 充值手续费类型, Enum: 1 2 1表示按百分比收费 2表示按值收费
     */
    private Integer depositFeeType;
    /**
     * 充值手续费
     */
    private String depositFeeValue;
    /**
     * 产品描述
     */
    private String description;
    /**
     * 是否允许锁卡
     */
    private Boolean isAllowLock;
    /**
     * 是否允许提现
     */
    private Boolean isAllowWithdraw;
    /**
     * 低额手续费币种
     */
    private String lowerAuthorizationFeeCurrencyCode;
    /**
     * 低额手续费类型, Enum: 1 2 1表示按百分比收费 2表示按值收费
     */
    private Integer lowerAuthorizationFeeType;
    /**
     * 低额手续费
     */
    private String lowerAuthorizationFeeValue;
    /**
     * 最高授权金额
     */
    private String maxAuthorizationAmount;
    /**
     * 授权封顶手续费
     */
    private String maxAuthorizationFee;
    /**
     * 销卡封顶手续费
     */
    private String maxCancellationFeeValue;
    /**
     * 开卡封顶手续费
     */
    private String maxCreationFeeValue;
    /**
     * 充值封顶手续费
     */
    private String maxDepositFeeValue;
    /**
     * 退款封顶手续费
     */
    private String maxRefundFeeValue;
    /**
     * 提现封顶手续费
     */
    private String maxWithdrawFeeValue;
    /**
     * 最低授权金额
     */
    private String minAuthorizationAmount;
    /**
     * 授权最低手续费
     */
    private String minAuthorizationFee;
    /**
     * 销卡最低手续费
     */
    private String minCancellationFeeValue;
    /**
     * 最低开卡金额, minCardAmount = minRechargeAmount+openFee+rechargeFee
     */
    private String minCardAmount;
    /**
     * 开卡最低手续费
     */
    private String minCreationFeeValue;
    /**
     * 充值最低手续费
     */
    private String minDepositFeeValue;
    /**
     * 最低充值金额
     */
    private String minRechargeAmount;
    /**
     * 退款最低手续费
     */
    private String minRefundFeeValue;
    /**
     * 提现最低手续费
     */
    private String minWithdrawFeeValue;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 可开卡数量
     */
    private Integer quota;
    /**
     * 退款手续费币种
     */
    private String refundFeeCurrencyCode;
    /**
     * 退款费起征点
     */
    private String refundFeeThreshold;
    /**
     * 退款手续费类型, Enum: 1 2 1表示按百分比收费 2表示按值收费
     */
    private Integer refundFeeType;
    /**
     * 退款手续费
     */
    private String refundFeeValue;
    /**
     * 结算币种
     */
    private String settlementCurrencyCode;
    /**
     * 客户产品Token
     */
    private String token;
    /**
     * 提现手续费类型
     */
    private String withdrawFeeCurrencyCode;
    /**
     * 提现手续费类型, Enum: 1 2 1表示按百分比收费 2表示按值收费
     */
    private Integer withdrawFeeType;
    /**
     * 提现手续费
     */
    private String withdrawFeeValue;
}
