/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.task;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class HTGetTasksRequest {

    /**
     * 卡片Token
     */
    private String cardToken;
    /**
     * 每页数量 Enum: 25 50 100, Default: 25
     */
    private Integer limit;
    /**
     * 当前页数 >= 1,Default: 1
     */
    private Integer page;
    /**
     * 任务状态 Enum: "APPLY" "PROCESSING" "SUCCESS" "FAILED"
     */
    private String status;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 任务类型 Enum: "create" "recharge" "cancel" "lock" "unlock" "withdraw"
     */
    private String type;
}
