/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.card;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
public class HTOpenCardRequest {

    /**
     * 卡片额度
     */
    private BigDecimal amount;
    /**
     * 开卡数量
     */
    private Integer count;
    /**
     * 开卡币种
     */
    private String currencyCode;
    /**
     * 备注
     */
    private String note;
    /**
     * 产品Token
     */
    private String productToken;
    /**
     * Enum: "" "采购海外直邮产品" "海外亚马逊测试购买" "海外其它服务采购" 用途
     */
    private String usage;
}
