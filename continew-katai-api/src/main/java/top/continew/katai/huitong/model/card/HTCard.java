/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.card;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class HTCard {

    /**
     * 卡片额度(开卡币种)
     */
    private BigDecimal amount;
    /**
     * 卡组织
     */
    private String association;
    /**
     * 卡片已授权金额(开卡币种)
     */
    private BigDecimal authorizedAmount;
    /**
     * 卡片已授权次数
     */
    private Integer authorizedTimes;
    /**
     * BIN
     */
    private String bin;
    /**
     * 卡号
     */
    private String cardNumber;
    /**
     * 城市
     */
    private String city;
    /**
     * 国家
     */
    private String country;
    /**
     * 卡片创建时间
     */
    private String createdAt;
    /**
     * 开卡币种
     */
    private String currencyCode;
    /**
     * 卡片CVV
     */
    private String cvv;
    /**
     * 卡片有效期
     */
    private String expirationDate;
    /**
     * 名
     */
    private String firstName;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 单次最大授权金额
     */
    private BigDecimal maxAuthAmount;
    /**
     * 卡片可授权次数
     */
    private Integer maxAuthTimes;
    /**
     * 单次最小授权金额
     */
    private String minAuthAmount;
    /**
     * 备注
     */
    private String note;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品Token
     */
    private String productToken;
    /**
     * 卡片已充值次数
     */
    private Integer rechargedTimes;
    /**
     * 卡片可用额度(开卡币种)
     */
    private BigDecimal remainAmount;
    /**
     * 卡片可用额度(结算币种)
     */
    private BigDecimal remainSettlementAmount;
    /**
     * 卡片已结算金额(开卡币种)
     */
    private BigDecimal settledAmount;
    /**
     * 卡片已结算金额(结算币种)
     */
    private BigDecimal settledSettlementAmount;
    /**
     * 卡片额度(结算币种)
     */
    private BigDecimal settlementAmount;
    /**
     * 结算币种
     */
    private String settlementCurrencyCode;
    /**
     * 省份
     */
    private String state;
    /**
     * 卡片状态, Enum: "正常" "预注销" "已注销" "锁定卡"
     */
    private String status;
    /**
     * 卡片状态code, Enum: cardState.03 - 正常 cardState.05 - 锁定卡 cardState.06 - 预注销
     * cardState.07 - 已注销
     */
    private String statusCode;
    /**
     * 地址1
     */
    private String street1;
    /**
     * 地址2
     */
    private String street2;
    /**
     * 任务Id
     */
    private String taskId;
    /**
     * 卡片Token
     */
    private String token;
    /**
     * 卡片总充值金额(开卡币种)
     */
    private BigDecimal totalAmount;
    /**
     * 卡片总充值金额(结算币种)
     */
    private BigDecimal totalSettlementAmount;
    /**
     * 卡片类型, Enum: "Prepay" "Debit"
     */
    private String type;
    /**
     * 卡片创建时间
     */
    private String updatedAt;
    /**
     * 卡片可用日期开始
     */
    private String validFrom;
    /**
     * 卡片可用日期结束
     */
    private String validTo;
    /**
     * 邮编
     */
    private String zipCode;
}
