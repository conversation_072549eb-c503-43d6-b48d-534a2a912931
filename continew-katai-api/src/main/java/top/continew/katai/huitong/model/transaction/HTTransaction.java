/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.transaction;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class HTTransaction {

    /**
     * 授权交易时间(UTC时间)
     */
    private String authorizedAt;
    /**
     * 开卡币种
     */
    private String cardCurrencyCode;
    /**
     * 卡号
     */
    private String cardNumber;
    /**
     * 卡片状态, Enum: "正常" "预注销" "已注销"
     */
    private String cardStatus;
    /**
     * 卡片状态码, Enum: cardState.01-申请 cardState.02-处理中 cardState.03-正常 cardState.04-失败
     * cardState.05-锁定卡 cardState.06-预销卡 cardState.07-已销卡
     */
    private String cardStatusCode;
    /**
     * 客户交易金额(开卡币种)
     */
    private BigDecimal customerCardAmount;
    /**
     * 客户展示名称
     */
    private String customerDisplayName;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户编号
     */
    private String customerNo;
    /**
     * 客户结算金额(结算币种)
     */
    private BigDecimal customerSettlementAmount;
    /**
     * 商户MCC
     */
    private Long merchantCategoryCode;
    /**
     * 商户城市
     */
    private String merchantCity;
    /**
     * 商户国家
     */
    private String merchantCountry;
    /**
     * 商户名称
     */
    private String merchantName;
    /**
     * 备注
     */
    private String note;
    /**
     * 授权金额
     */
    private BigDecimal originalAmount;
    /**
     * 授权币种
     */
    private String originalCurrencyCode;
    /**
     * 响应代码
     */
    private String responseCode;
    /**
     * 响应描述
     */
    private String responseDesc;
    /**
     * 结算交易时间(UTC时间)
     */
    private String settledAt;
    /**
     * 结算币种
     */
    private String settlementCurrencyCode;
    /**
     * 结算状态, Enum: "交易成功" "交易处理中"
     */
    private String settlementStatus;
    /**
     * 结算状态码, Enum: settleState.01-未结算 settleState.02-已结算
     */
    private String settlementStatusCode;
    /**
     * 授权交易状态, Enum: "交易成功" "交易失败"
     */
    private String status;
    /**
     * 授权交易状态码, Enum: payState.01-处理中 payState.02-成功 payState.04-失败
     */
    private String statusCode;
    /**
     * 卡片Token
     */
    private String token;
    /**
     * 交易ID
     */
    private String transactionId;
    /**
     * 授权交易类型, Enum: "消费授权" "消费授权冲正" "退款授权" "退款授权冲正" "授权查询"
     */
    private String type;
    /**
     * UUID
     */
    private String uuid;
}
