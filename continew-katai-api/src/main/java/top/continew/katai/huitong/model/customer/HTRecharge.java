/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.customer;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HTRecharge {

    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 客户Token
     */
    private String customerToken;
    /**
     * 充值手续费用币种
     */
    private String rechargeFeeCurrencyCode;
    /**
     * 充值手续费用类型, Enum: 1 2 1表示按百分比收费 2表示按值收费
     */
    private Integer rechargeFeeType;
    /**
     * 充值手续费用值
     */
    private String rechargeFeeValue;
    /**
     * 充值方式
     */
    private String rechargeType;
}
