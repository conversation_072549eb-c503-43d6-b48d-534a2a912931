/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.katai.huitong.model.task;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HTTask {

    /**
     * 任务创建时间
     */
    private String createAt;
    /**
     * ID
     */
    private Long id;
    /**
     * 任务内容
     */
    private String name;
    /**
     * 备注
     */
    private String note;
    /**
     * 任务数量, 成功任务数量
     */
    private Integer records;
    /**
     * 当任务类型为create时，返回卡片token，其他类型返回空
     */
    private String results;
    /**
     * 任务状态, Enum: "APPLY" "SUCCESS" "FAILED" "PROCESSING"
     */
    private String status;
    /**
     * 成功任务数量
     */
    private Integer successRecords;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 任务类型, Enum: "create" "recharge" "cancel" "lock" "unlock" "withdraw"
     */
    private String type;
    /**
     * 任务更新时间
     */
    private String updateAt;
}
