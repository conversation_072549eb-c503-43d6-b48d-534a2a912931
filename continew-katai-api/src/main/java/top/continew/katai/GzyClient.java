package top.continew.katai;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.atlassian.guava.common.util.concurrent.RateLimiter;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.gzy.GzyConfig;
import top.continew.katai.gzy.model.req.*;
import top.continew.katai.gzy.model.resp.*;
import top.continew.katai.utils.Common;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/11 14:13
 */
@Slf4j
public class GzyClient extends Client {
    private final String appId;
    private final String appSecret;
    private final String privateKey;
    @Setter
    private String accessToken;

    //获取交易信息、卡详情限制2s/5次。其他接口没有限制。
    //这里统一做一个限制，防止接口被调用的太频繁
    private final RateLimiter rateLimiter = RateLimiter.create(2);


    /**
     * 获取光子易的公钥
     * @return
     */
    private static String getGzyPublicKey() {
//        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2c7dF19+mXIZeWVgd2HWX13phv1emuRprZB7kP2mJVIYBdlhLS/fK8/FfmBfkxAKhHrpvE78Ic22bL2H3f3jYfn2KfwXiDByOXpcRbbQrcbnU6iDnyU7nw3yF4wykIxVDwGyYEDWXLd0WCtC719Luw4+lUCj03xrxdDIdoEYxvQIDAQAB";
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7JLUY0Y3YrHL5GjVtPrpybi5ht0I5yjzL5HeYP20nbs5JSyXRtAwp1d9ZbOxCfytapNPtXklZZqnzslhOcdCR1jSIICAOlbP1WaBsZ44PJmcEFVFFPz9mzf/1KBjbtMON0GedOxPAN/jE13rovUVBnIfbgW1/csFV8tICs3ABGQIDAQAB";
        return publicKey;
    }

    public GzyClient(GzyConfig config) {
        super(config);
        this.appId = config.getAppId();
        this.appSecret = config.getAppSecret();
        this.privateKey = config.getPrivateKey();
    }

    public GzyAuthCodeResponse getAuthCode() {
        OpenApiRequest req = new OpenApiRequest();
        req.addHeader("Authorization", "basic " + Base64.encode(this.appId + "/" + this.appSecret));
        log.info("appId:{},appSecret:{},base64:{}", this.appId, this.appSecret, Base64.encode(this.appId + "/" + this.appSecret));
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/oauth2/token/issueAuthCode");
        return Common.toModel(callApi(params, req), "body", GzyAuthCodeResponse.class);
    }

    public List<GzyCardBinResponse> getCardBin() {
        //https://x-api.photonpay.com/vcc/openApi/v4/getCardBin
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/vcc/openApi/v4/getCardBin");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        return Common.toModelList(callApi(params, req), "body", GzyCardBinResponse.class);

//        String json = "[ {" +
//                "    \"cardBin\" : \"367178\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"Discover\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"297\"," +
//                "    \"availableCard\" : \"0\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"367179\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"Discover\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"297\"," +
//                "    \"availableCard\" : \"0\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"54502000\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"MasterCard\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"1072\"," +
//                "    \"availableCard\" : \"222\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"558325\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"MasterCard\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"1072\"," +
//                "    \"availableCard\" : \"6\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"658706\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"Discover\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"297\"," +
//                "    \"availableCard\" : \"2\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"658710\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"Discover\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"297\"," +
//                "    \"availableCard\" : \"1\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"658711\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"Discover\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"297\"," +
//                "    \"availableCard\" : \"0\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"658718\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"Discover\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"297\"," +
//                "    \"availableCard\" : \"0\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"658719\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"Discover\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"297\"," +
//                "    \"availableCard\" : \"0\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  }, {" +
//                "    \"cardBin\" : \"658720\"," +
//                "    \"cardType\" : \"share,recharge\"," +
//                "    \"cardScheme\" : \"Discover\"," +
//                "    \"cardCurrency\" : \"USD\"," +
//                "    \"billingAddressUpdatable\" : \"N\"," +
//                "    \"expiryDateCustomization\" : \"Y\"," +
//                "    \"remainingAvailableCard\" : \"297\"," +
//                "    \"availableCard\" : \"0\"," +
//                "    \"cardFormFactor\" : \"virtual_card\"" +
//                "  } ]";
//
//        JSONArray array = JSON.parseArray(json);
//        List<GzyCardBinResponse> list = new ArrayList<>();
//        for (int i = 0; i < array.size(); i++) {
//            JSONObject object = array.getJSONObject(i);
//            GzyCardBinResponse resp = object.toJavaObject(GzyCardBinResponse.class);
//            list.add(resp);
//        }
//        return list;
    }




    /**
     * 创建卡
     * @param createCardReq
     * @return
     */
    public GzyCreateCardResponse createCard(GzyCreateCardRequest createCardReq) {
        //https://x-api.photonpay.com/vcc/openApi/v4/openCard
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/vcc/openApi/v4/openCard");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("X-PD-SIGN", sign(JSON.toJSONString(createCardReq)));
        req.setBody(createCardReq);
        return Common.toModel(callApi(params, req), "body", GzyCreateCardResponse.class);
    }

    /**
     * 获取开卡、更新卡信息、冻结等请求的结果
     * @return
     */
    public GzyGetRequestResultResponse getRequestResult(GzyGetRequestResultRequest resultRequest) {
        //https://x-api.photonpay.com/vcc/openApi/v4/getRequestResult
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/vcc/openApi/v4/getRequestResult");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);

        Map<String, String> queryMap = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(resultRequest));
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() != null) {
                queryMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        req.setQuery(queryMap);

        return Common.toModel(callApi(params, req), "body", GzyGetRequestResultResponse.class);
    }

    /**
     * 获取卡信息
     * @return
     */
    public GzyCardDetail getCardDetail(String cardId) {
        //https://x-api.photonpay.com/vcc/openApi/v4/getCardDetail
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/vcc/openApi/v4/getCardDetail");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.setQuery(Map.of("cardId", cardId));
        return Common.toModel(callApi(params, req), "body", GzyCardDetail.class);
    }

    /**
     * 获取卡信息
     * @return
     */
    public GzyCardSensitiveDetail getCvv(String cardId) {
        //https://x-api.photonpay.com/vcc/openApi/v4/getCvv
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/vcc/openApi/v4/getCvv");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.setQuery(Map.of("cardId", cardId));
        return Common.toModel(callApi(params, req), "body", GzyCardSensitiveDetail.class);
    }


    /**
     * 更新卡信息
     * @param updateCardReq
     * @return
     */
    public GzyCardDetail updateCard(GzyUpdateCardRequest updateCardReq) {
        //https://x-api.photonpay.com/vcc/openApi/v4/updateCard
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/vcc/openApi/v4/updateCard");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("X-PD-SIGN", sign(JSON.toJSONString(updateCardReq)));
        req.setBody(updateCardReq);
        return Common.toModel(callApi(params, req), "body", GzyCardDetail.class);
    }

    /**
     * 冻结或解冻卡
     * @param freezeCardRequest
     */
    public void freezeCard(GzyFreezeCardRequest freezeCardRequest) {
        //https://x-api.photonpay.com/vcc/openApi/v4/freezeCard
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/vcc/openApi/v4/freezeCard");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("X-PD-SIGN", sign(JSON.toJSONString(freezeCardRequest)));
        req.setBody(freezeCardRequest);
        callApi(params, req);
    }


    /**
     * 交易模拟
     * @param sandBoxTransRequest
     */
    public void sandBoxTransaction(GzySandBoxTransRequest sandBoxTransRequest) {
        //https://x-api.photonpay.com/vcc/open/v2/sandBoxTransaction
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/vcc/open/v2/sandBoxTransaction");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("X-PD-SIGN", sign(JSON.toJSONString(sandBoxTransRequest)));
        req.setBody(sandBoxTransRequest);
        callApi(params, req);
    }


    /**
     * 销卡
     * @param cancelCardRequest
     */
    public void cancelCard(GzyCancelCardRequest cancelCardRequest) {
        //https://x-api.photonpay.com/vcc/openApi/v4/cancelCard
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/vcc/openApi/v4/cancelCard");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("X-PD-SIGN", sign(JSON.toJSONString(cancelCardRequest)));
        req.setBody(cancelCardRequest);
    }


    /**
     * 获取交易记录
     *
     * @param transactionReq
     * @return
     */
    public GzyGetTransactionResponse getTransactions(GzyGetTransactionRequest transactionReq) {
        //https://x-api.photonpay.com/vcc/openApi/v4/pagingVccTradeOrder
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/vcc/openApi/v4/pagingVccTradeOrder");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        Map<String, String> queryMap = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(transactionReq));
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() != null) {
                queryMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        req.setQuery(queryMap);
        Map<String, ?> resultMap = callApi(params, req);
        GzyGetTransactionResponse resp = new GzyGetTransactionResponse();
        resp.setList(Common.toModelList(resultMap, "body", GzyTransactionItem.class));
        resp.setPageIndex(MapUtil.getInt(resultMap, "pageIndex", transactionReq.getPageIndex()));
        resp.setPageSize(MapUtil.getInt(resultMap, "pageSize", transactionReq.getPageSize()));
        resp.setTotal(MapUtil.getInt(resultMap, "total", 0));
        return resp;
    }

    /**
     * 获取卡列表
     *
     * @param cardRequest
     * @return
     */
    public GzyGetCardResponse getCards(GzyGetCardRequest cardRequest) {
        //https://x-api.photonpay.com/vcc/openApi/v4/pagingVccCard
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/vcc/openApi/v4/pagingVccCard");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);

        Map<String, String> queryMap = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(cardRequest));
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() != null) {
                queryMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        req.setQuery(queryMap);


        Map<String, ?> resultMap = callApi(params, req);
        GzyGetCardResponse resp = new GzyGetCardResponse();
        resp.setList(Common.toModelList(resultMap, "body", GzyCardDetail.class));
        resp.setPageIndex(MapUtil.getInt(resultMap, "pageIndex", cardRequest.getPageIndex()));
        resp.setPageSize(MapUtil.getInt(resultMap, "pageSize", cardRequest.getPageSize()));
        resp.setTotal(MapUtil.getInt(resultMap, "total", 0));
        return resp;
    }


    /**
     * 共享卡交易额度明细
     * 共享卡的每一笔交易限额变动明细查看
     * @param limitRequest
     * @return
     */
    public GzyShareCardTxnLimitResponse getShareCardTxnLimitDetail(GzyShareCardTxnLimitRequest limitRequest) {
        //https://x-api.photonpay.com/vcc/openApi/v4/pagingShareCardTxnLimitDetail
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/vcc/openApi/v4/pagingShareCardTxnLimitDetail");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);

        Map<String, String> queryMap = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(limitRequest));
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() != null) {
                queryMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        req.setQuery(queryMap);

        Map<String, ?> resultMap = callApi(params, req);
        GzyShareCardTxnLimitResponse resp = new GzyShareCardTxnLimitResponse();
        resp.setList(Common.toModelList(resultMap, "body", GzyShareCardTxnLimitDetail.class));
        resp.setPageIndex(MapUtil.getInt(resultMap, "pageIndex", limitRequest.getPageIndex()));
        resp.setPageSize(MapUtil.getInt(resultMap, "pageSize", limitRequest.getPageSize()));
        resp.setTotal(MapUtil.getInt(resultMap, "total", 0));
        return resp;
    }

    public GzyGetAccountResponse getAccount(GzyGetAccountRequest request) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/wallet/openApi/v4/account/single");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        Map<String, String> queryMap = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(request));
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() != null) {
                queryMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        req.setQuery(queryMap);
        return Common.toModel(callApi(params, req), "body", GzyGetAccountResponse.class);
    }


    public GzyTokenResponse getToken() {
        OpenApiRequest req = new OpenApiRequest();
        req.addHeader("Authorization", "basic " + Base64.encode(this.appId + "/" + this.appSecret));
        log.info("光子易-获取令牌:appId:{},appSecret:{},base64:{}", this.appId, this.appSecret, Base64.encode(this.appId + "/" + this.appSecret));
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/oauth2/token/accessToken");
        return Common.toModel(callApi(params, req), "body", GzyTokenResponse.class);
    }


    private Map<String, ?> callApi(Params params, OpenApiRequest apiRequest) {
        RuntimeOptions runtime = new RuntimeOptions();
        apiRequest.addHeader("Content-Type", "application/json");
        if (rateLimiter.tryAcquire(30, TimeUnit.SECONDS)) {
            return doCallApi(params, apiRequest, runtime);
        }else {
            throw new ThirdException(Map.of("code", "429", "message", "接口限流，请稍后再试"));
        }
    }

    private Map<String, ?> doCallApi(Params params, OpenApiRequest apiRequest, RuntimeOptions runtime) {
        //输出当前时间，到毫秒级的
        log.info("光子易-请求URL:{},{}", params.getMethod(), params.getPathname());
        log.info("光子易-请求body:{}", JSON.toJSONString(apiRequest.getBody()));
        log.info("光子易-请求query:{}", JSON.toJSONString(apiRequest.getQuery()));
        log.info("光子易-请求headers:{}", JSON.toJSONString(apiRequest.getHeaders()));
        long startTime = System.currentTimeMillis();
        Map<String, ?> response;

        try {
            response = this.doRequest(params, apiRequest, runtime);
        }catch(ThirdException e) {
            log.error("光子易-请求异常:{}, {}", e.getCode(), e.getMessage());
            if("1002".equals(e.getCode())) {
                e.setMessage("token失效，请再试一次");
            }
            throw e;
        }

        long costTime = System.currentTimeMillis() - startTime;
        JSONObject data = JSONObject.from(response.get("body"));
        String code = data.getString("code");
        log.info("光子易-响应耗时:{}ms，响应码:{}", costTime, code);

        if (!"0000".equals(code)) {
            JSONArray reasonArray = data.getJSONArray("data");
            StringJoiner sj = new StringJoiner(",");
            if(null != reasonArray){
                for (int i = 0; i < reasonArray.size(); i++) {
                    JSONObject reasonData = reasonArray.getJSONObject(i);
                    if(reasonData != null && StrUtil.isNotBlank(reasonData.getString("message"))) {
                        sj.add(reasonData.getString("message"));
                    }
                }
            }

            throw new ThirdException(Map.of("code", data.get("code"), "message", data.get("msg") + "." + sj.toString()));
        }

        Map<String, Object> result = new HashMap<>();
        result.put("header", response.get("header"));
        result.put("body", data.getString("data"));
        result.put("total", data.getInteger("total"));
        result.put("pageIndex", data.getInteger("pageIndex"));
        result.put("pageSize", data.getInteger("pageSize"));
        return result;
    }



    public String sign(String encryptedStr) {
        String str = "";
        try {
            //将私钥加密数据字符串转换为字节数组
            byte[] data = encryptedStr.getBytes(StandardCharsets.UTF_8);

            byte[] bytes = Base64.decode(this.privateKey);
            // 构造PKCS8EncodedKeySpec对象
            PKCS8EncodedKeySpec pkcs = new PKCS8EncodedKeySpec(bytes);
            // 指定的加密算法
            KeyFactory factory = KeyFactory.getInstance("RSA");
            // 取私钥对象
            PrivateKey key = factory.generatePrivate(pkcs);
            // ⽤私钥对信息⽣成数字签名
            Signature signature = Signature.getInstance("MD5withRSA");
            signature.initSign(key);
            signature.update(data);
            str = Base64.encode(signature.sign());
        } catch (Exception e) {
            log.error("私钥加签出错", e);
        }
        return str;
    }



    public static boolean checkSign(String sign, String payload) {
        try {
            byte[] data = payload.getBytes(StandardCharsets.UTF_8);
            byte[] bytes = org.apache.commons.codec.binary.Base64.decodeBase64(getGzyPublicKey());

            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(bytes);
            KeyFactory factory = KeyFactory.getInstance("RSA");
            PublicKey key = factory.generatePublic(keySpec);

            Signature signature = Signature.getInstance("MD5withRSA");
            signature.initVerify(key);
            signature.update(data);
            return signature.verify(org.apache.commons.codec.binary.Base64.decodeBase64(sign));
        } catch (Exception e) {
            log.error("光子易验签失败", e);
            return false;
        }
    }


    /**
     * 设置授权的请求头
     * @param req
     */
    private void setHeaderAuth(OpenApiRequest req) {
        if(StrUtil.isNotBlank(this.accessToken)) {
            req.addHeader("X-PD-TOKEN", this.accessToken);
        }else {
            req.addHeader("X-PD-AUTHORIZATION", "basic " + Base64.encode(this.appId + "/" + this.appSecret));
        }
    }

}
