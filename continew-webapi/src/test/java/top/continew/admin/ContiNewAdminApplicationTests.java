/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.telegram.telegrambots.meta.api.methods.ParseMode;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardStatusEnum;
import top.continew.admin.biz.enums.CardTransactionStatusEnum;
import top.continew.admin.biz.enums.CardTransactionTypeEnum;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.katai.strategy.impl.CardVpOpsStrategyImpl;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.model.entity.PersonalAccountDO;
import top.continew.admin.biz.model.entity.WhiteEmailDO;
import top.continew.admin.biz.robot.MyTelegramBot;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.AdsPowerUtils;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.admin.biz.utils.RabbitUtils;
import top.continew.admin.job.AdAccountJob;
import top.continew.admin.job.AdAccountOrderJob;
import top.continew.admin.system.service.FileService;
import top.continew.katai.HuiTongClient;
import top.continew.katai.cardvp.model.auth.CVTransfer;
import top.continew.katai.huitong.HuiTongConfig;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@SpringBootTest
class ContiNewAdminApplicationTests {

    @Resource
    private CardOpsStrategyFactory cardOpsStrategyFactory;

    @Resource
    private AdAccountOrderJob adAccountOrderJob;

    @Resource
    private BusinessManagerService businessManagerService;

    //@Resource
    private AdAccountService adAccountService;

    //@Resource
    private FileService fileService;

    @Resource
    private CardService cardService;

    @Resource
    private CardTransactionService cardTransactionService;

    @Resource
    private AdsPowerService adsPowerService;

    @Resource
    private MyTelegramBot myTelegramBot;

    @Resource
    private WhiteEmailService whiteEmailService;
    @Resource
    private PersonalAccountService personalAccountService;
    @Resource
    private AdAccountSyncService adAccountSyncService;
    @Resource
    private AdAccountEffectiveSyncService adAccountEffectiveSyncService;

    private final HuiTongClient client = new HuiTongClient(new HuiTongConfig().setCustomerToken("50962AA05A2979E4")
        .setSecret("76E74E1326DBF52A2F561C8E788AC0B113922E16A7F2C975EEDAAD9D34F1348")
        .setServerPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuUIjkAKkRvJ+SwfiUgIf6PQvCx9UbJpmfZ6E7DQaJ3uITID4G9BEGKHEGMaGrBm17xct/ySCrzQq53VghkK/CO4nCnPA8xfP9yuJsQiftXPoc7f76Y7D0m//vCK6kS3ig0CGAOgtPj8QI6wKWqZjY29ZFBgAhlGS4vp4Oa9xNdh+5NuhFUtmkzSvVJyVA1YcntqdGgNeK/6niObBWCDVXlC02h5ixu+Cy2+G6D6xKc/S6UlfmxnsvvQhRvxj/wKwqaiAz9ujiPEc0WWM0j1kUfb4aC3gbDw3JikHeCnt8jCTP0Z5LchGFO0ZDF+e//g48woOIfuBgosTt8iij18QsQIDAQAB")
        .setCustomerPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQD2ygRcp+UZNdw9t83bRqe86tmHCbD3OtH3oOA/7KC9Y/IPauFTRh25Xtk1kE08c7RapOvMk+5ZN5eyeARevAoRC/UHcVB0wLf+BKSQmzJjwELyaXUKBTbXvptVn8OrVmQZCxgZcMHdDbjBOIvJ2AgQi2hwgoKbXguZTjf56LpLHVJMl73g6n2MYiQPltiL/BUb9zzW1eFVvBEPIyy/9BvJMqiqQadXcp3XJA+W+Z9wyOn8cPskCkhVxGImk0PXZaWARPQWkauNPxVIBgzcsZ9AjaVi2Hp4Ne+0RzOqh1ppZJfYonCXZX8MvzoQmDvvyo82xUcYiFcoNgIkrpPd7rqpAgMBAAECggEBAI/6vl68SUnTBYIwaPZkG1p5fZ1uBwPGGlepUQuaZxD3bXKXPYxS3OaYMoswfA8nxfIYQ8WgDK+aYnNp18rZU9osKrFD59el0/JsDQKcuOI/eg9pfTHyZtOpzYA/7G1awp1lvpKhGUl5Ru/bqJqC4fmdC7qQOdihHo07eCbiI0XMWTSF0c+zuzzPz3iToSB1ZLpYT0O1MSrsiDmDBNEbjIKtT/a4Gsp/MvaERs/AtRA0OcEXL2uemYM20CdHCktKTRrFtdXL3GwIgWPoEth7RRFXlJJTtXZa1aNYGs0y0Nljh4755HiEH2ThAVrrh+pfA9M3jiS3tbm03PR6tu3Und0CgYEA/xu5HboDXpJoUHiVYWKEqi6Ojm2bxiWorDr8MzaE95rX4Y1NYsa+duY/iGbpc6TGsdSyPX+FjF2qxcsmyIVqQ4MQvNnVLnIeGoHkwPfdcggxkUPo1OlKxO8pxwsZrBHuHWMtqjYhfLznPc3HL9Az2ZSg6sd8T4kUTE2Rufhsd8CgYEA96bZiOpRoeU4PwWxqhPA4rV2btGO9aY/GbXsoaVPyNFbaUdYs76F5XBnXa3aXvFgBj+hVFPxlKO+CQZrz4hqYjI8AkGBIyh2Xm+oJ0MAysJmGKoLm9PZm5bvWsRL2Mk2eDhrJMgU2BlUqTYwy5dOikvLt28eA3tgT7Db91YUdHcCgYEAhEEjHz4oMQdF/TIGf5TqmfHPWhBBfCcLMRz3tMb+5/4PPF6fhzquqYonLtjNkJAfw2pCiNy+9VJ/awOApIxvHk2iHbOChnIqdALEGnuPhchS9XcoPExI/KQm0ZvRiQsqo36NjMTMO+VQVrfzYnvkZaFudcRoqYDF4zHYKbpdT5ECgYBaonzAqAkCODKylfHkreAM2J6khhtM+e/kB/m3WxntA/nxeMQU3GiChPW/ii3+S1Z6UvVHOIWUgay9/tScGm/cyke+B50scdWIUFL5M4NpsWXOJwWinRDp6X5l+KtNhq5hpzxHNNa0E+kxGH0ZNaHv6TeARbF1UsT3xWFrjtxjlwKBgD7JXe3zBgjesvDCr9AUIXcoI3e9SNqsBjne6hhNeCaGitoZrN8mZCxTr37J+49evKxyyC7v22885O8XhopT0gBRxMp7Kv4PvFd1p7XiYsP0qhTOvSoGIZCNOgVm5FTpA0HTD14/A69p0Tbjjz+A24PB2joiijW/NqEKgRWIavCN")
        .setProtocol("https")
        .setEndpoint("api.huitongcard.com"));

    @Test
    void contextLoads() throws IOException {
        List<String> cardNumbers = Files.readAllLines(Path.of("C:\\Users\\<USER>\\Downloads\\新建 文本文档1.txt"));
        List<WhiteEmailDO> whiteEmailDOS = new ArrayList<>();
        for (String cardNumber : cardNumbers) {
            WhiteEmailDO whiteEmailDO = new WhiteEmailDO();
            whiteEmailDO.setEmail(cardNumber);
            whiteEmailDOS.add(whiteEmailDO);
        }
        whiteEmailService.saveBatch(whiteEmailDOS);
    }

    public static String createBMAdminAddMessage(String bmId, String userName, String userEmail) {
        return "📣 *BM新增管理员提醒* 📣\n\n" + "🔍 **BM ID**: `" + bmId + "`\n" + "👤 **用户名称**: *" + CommonUtils.escapeMarkdown(userName) + "*\n" + "📧 **用户邮箱**: *" + CommonUtils.escapeMarkdown(userEmail) + "*\n\n" + "请注意新管理员的权限设置！";
    }

    @Data
    @ExcelIgnoreUnannotated
    public static class adAccountCard {
        @ExcelProperty("card_number")
        private String cardNumber;
        @ExcelProperty("platform_ad_id")
        private String platformAdId;
    }

    @Test
    void cardWithdraw() throws IOException {
        List<String> cardNumbers = Files.readAllLines(Path.of("C:\\Users\\<USER>\\Downloads\\新建 文本文档.txt"));
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(CardPlatformEnum.CARD_VP);
        for (String cardNumber : cardNumbers) {
            CardDO cardDO = new CardDO();
            cardDO.setCardNumber(cardNumber);
            cardOpsStrategy.withdrawCard(cardDO, null);
            cardOpsStrategy.inactiveCard(cardDO);
        }
    }

    static void withdrawPhotonPay() throws IOException {
        List<String> cardNumbers = Files.readAllLines(Path.of("C:\\Users\\<USER>\\Downloads\\新建 文本文档.txt"));
        for (String cardNumber : cardNumbers) {
            System.out.println(HttpUtil.get("https://financeapi.fbpricepro.xyz/api/withdraw?cardNumber=" + cardNumber));
        }
    }

    static void deleteAds() {
        List<AdsPowerUtils.Ads> adsList = AdsPowerUtils.getAdsList();
        List<String> deleteIds = new ArrayList<>();
        for (AdsPowerUtils.Ads ads : adsList) {
            if (ads.getLast_open_time() != 0 && ads.getLast_open_time() < 1743436800) {
                deleteIds.add(ads.getUser_id());
            }
        }
        System.out.println(deleteIds.size());
        AdsPowerUtils.deleteAds(deleteIds);
    }

    @Test
    void getHuitongCardList() {
        String startId = "";
        int page = 1;
        while (true) {
            System.out.println(page);
            System.out.println(startId);
            List<CardDO> list = getHuitongCardList(startId);
            if (list.isEmpty()) {
                break;
            }
            startId = list.get(list.size() - 1).getCardName();
            page++;
            List<String> existId = cardService.listByPlatform(CardPlatformEnum.HUI_TONG.getValue())
                .stream()
                .map(CardDO::getPlatformCardId)
                .toList();
            list = list.stream().filter(v -> !existId.contains(v.getPlatformCardId())).toList();
            //            if (!list.isEmpty()) {
            //                JSONObject cardJson = getFullCardNo(list.stream().map(CardDO::getPlatformCardId).toList());
            //                for (CardDO cardDO : list) {
            //                    String cardNumber = cardJson.getString(cardDO.getPlatformCardId());
            //                    if (StringUtils.isNotBlank(cardNumber)) {
            //                        cardDO.setCardNumber(cardNumber);
            //                    }
            //                }
            //                System.out.println(JSONObject.toJSONString(list));
            //            }
            // cardService.saveBatch(list);
        }
    }

    @Test
    void getHuitongCardTransactionList() {
        int page = 1;
        List<CardDO> cardList = cardService.listByPlatform(CardPlatformEnum.HUI_TONG.getValue());
        Map<String, String> cardMap = cardList.stream()
            .collect(Collectors.toMap(CardDO::getPlatformCardId, CardDO::getCardNumber));
        while (true) {
            System.out.println(page);
            List<CardTransactionDO> list = getHuitongCardTransactionList(page, "2024-11-01", "2024-12-31");
            System.out.println(JSONObject.toJSONString(list));
            if (list.isEmpty()) {
                break;
            }
            page++;
            list = list.stream().filter(v -> cardMap.containsKey(v.getPlatformCardId())).toList();
            if (!list.isEmpty()) {
                for (CardTransactionDO cardDO : list) {
                    String cardNumber = cardMap.get(cardDO.getPlatformCardId());
                    if (StringUtils.isNotBlank(cardNumber)) {
                        cardDO.setCardNumber(cardNumber);
                    }
                }
                System.out.println(JSONObject.toJSONString(list));
            }
            cardTransactionService.saveBatch(list);
        }
    }

    public JSONObject getFullCardNo(List<String> cardIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("checkType", "cardInfo");
        params.put("optType", "eyeAll");
        params.put("terminal", "terminal.web");
        String userId = "*********";
        String time = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        params.put("userId", userId);
        params.put("time", time);
        params.put("cardId", StringUtils.join(cardIds, ","));
        Map<String, String> headers = new HashMap<>();
        headers.put("x-enterno", "E20241101021639");
        headers.put("x-timezone", "TimeZone.CST");
        headers.put("x-token", "L20250402101625136243DAEB6D284F2F22E9");
        headers.put("x-user", "*********#terminal.web");
        headers.put("x-sign", getSign(userId, time) + "#" + time);
        headers.put("origin", "https://www.huitongcard.com");
        headers.put("Host", "www.huitongcard.com");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("referer", "https://www.huitongcard.com/admin/card/index");
        String cookie = "_gcl_au=1.1.108417122.1743513048; web-user-key={%22resp%22:%220000%22%2C%22isLog%22:%221%22%2C%22userId%22:%22*********%22%2C%22invitePeople%22:%22*********%22%2C%22telCode%22:%221%22%2C%22name%22:%22YMDJ618%22%2C%22icon%22:%22path=%22%2C%22userType%22:%22userType.02%22%2C%22mobileNo%22:%225182780255%22%2C%22email%22:%<EMAIL>%22%2C%22userIdNumber%22:%22%22%2C%22userLevel%22:%22userLevel.01%22%2C%22isAuth%22:1%2C%22enterNo%22:%22E20241101021639%22%2C%22enterType%22:%22enterType.03%22%2C%22enterName%22:%22YMDJ618%22%2C%22enterNameEn%22:%22YMDJ618%22%2C%22regeditNo%22:%22%22%2C%22contacts%22:%22%22%2C%22country%22:%22%22%2C%22countryName%22:%22%22%2C%22status%22:%22enterState.02%22%2C%22isVerifyPayPwd%22:%220%22%2C%22isPayPass%22:0%2C%22isMSO%22:false%2C%22isBl%22:false%2C%22token%22:%22L20250402101625136243DAEB6D284F2F22E9%22%2C%22expireMinute%22:%2230%22%2C%22memberType%22:%22memberType.01%22%2C%22memberRole%22:%22ADMIN%22%2C%22isMainAdmin%22:true%2C%22isAdmin%22:true%2C%22isVccLogin%22:true%2C%22needLoginPwdUpdate%22:false%2C%22loginTime%22:%222025-04-02%2010:16%22%2C%22isVccAdmin%22:true}";
        System.out.println(params);
        String response = HttpRequest.get("https://www.huitongcard.com/vcc-api/auth/card/getCardInfo")
            .headerMap(headers, true)
            .cookie(cookie)
            .form(params)
            .execute()
            .body();
        JSONObject jsonObject = JSONObject.parseObject(response);
        System.out.println(jsonObject);
        JSONObject data = jsonObject.getJSONObject("data");
        if (data == null) {
            return new JSONObject();
        }
        JSONArray cards = data.getJSONArray("cards");
        JSONObject result = new JSONObject();
        for (int i = 0; i < cards.size(); i++) {
            JSONObject card = cards.getJSONObject(i);
            result.put(card.getString("cardId"), card.getString("cardNo"));
        }
        return result;
    }

    public List<CardTransactionDO> getHuitongCardTransactionList(int page, String start, String end) {
        Map<String, Object> params = new HashMap<>();
        params.put("status", "payState.03");
        params.put("timeZone", "TimeZone.CST");
        params.put("terminal", "terminal.web");
        params.put("startDate", start);
        params.put("endDate", end);
        String userId = "*********";
        String time = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        params.put("userId", userId);
        params.put("time", time);
        params.put("pageNum", page);
        params.put("pageSize", "100");
        Map<String, String> headers = new HashMap<>();
        headers.put("x-enterno", "E20241101021639");
        headers.put("x-timezone", "TimeZone.CST");
        headers.put("x-token", "L202506261729172102E061A7B0FFC623A001");
        headers.put("x-user", "*********#terminal.web");
        headers.put("x-sign", getSign(userId, time) + "#" + time);
        headers.put("origin", "https://www.huitongcard.com");
        headers.put("Host", "www.huitongcard.com");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("referer", "https://www.huitongcard.com/admin/card/index");
        String cookie = "_gcl_au=1.1.108417122.1743513048; acw_tc=9b668e9617509297519616513e5ef0ae4cc2b723af3fbc82349d8daaf2; cdn_sec_tc=9b668e9617509297519616513e5ef0ae4cc2b723af3fbc82349d8daaf2; web-user-key={%22resp%22:%220000%22%2C%22isLog%22:%221%22%2C%22userId%22:%22*********%22%2C%22invitePeople%22:%22*********%22%2C%22telCode%22:%221%22%2C%22name%22:%22YMDJ618%22%2C%22icon%22:%22path=%22%2C%22userType%22:%22userType.02%22%2C%22mobileNo%22:%225182780255%22%2C%22email%22:%<EMAIL>%22%2C%22userIdNumber%22:%22%22%2C%22userLevel%22:%22userLevel.01%22%2C%22isAuth%22:1%2C%22enterNo%22:%22E20241101021639%22%2C%22enterType%22:%22enterType.03%22%2C%22enterName%22:%22YMDJ618%22%2C%22enterNameEn%22:%22YMDJ618%22%2C%22regeditNo%22:%22%22%2C%22contacts%22:%22%22%2C%22country%22:%22%22%2C%22countryName%22:%22%22%2C%22status%22:%22enterState.02%22%2C%22isVerifyPayPwd%22:%220%22%2C%22isPayPass%22:0%2C%22isMSO%22:false%2C%22isBl%22:false%2C%22token%22:%22L202506261729172102E061A7B0FFC623A001%22%2C%22expireMinute%22:%2260%22%2C%22memberType%22:%22memberType.01%22%2C%22memberRole%22:%22ADMIN%22%2C%22isMainAdmin%22:true%2C%22isAdmin%22:true%2C%22isVccLogin%22:true%2C%22needLoginPwdUpdate%22:false%2C%22loginTime%22:%222025-06-26%2017:29%22%2C%22isVccAdmin%22:true}";
        String response = HttpRequest.get("https://www.huitongcard.com/vcc-api/auth/payment/listV2")
            .headerMap(headers, true)
            .cookie(cookie)
            .form(params)
            .execute()
            .body();
        JSONObject jsonObject = JSONObject.parseObject(response);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray records = data.getJSONArray("list");
        List<CardTransactionDO> result = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            JSONObject record = records.getJSONObject(i);
            CardTransactionDO transactionDO = new CardTransactionDO();
            transactionDO.setPlatform(CardPlatformEnum.HUI_TONG);
            transactionDO.setPlatformCardId(record.getString("cardId"));
            transactionDO.setCardNumber(record.getString("cardNo"));
            transactionDO.setTransactionId(record.getString("paymentNo"));
            String paymentType = record.getString("paymentTypeName");
            switch (paymentType) {
                case "消费" -> transactionDO.setTransType(CardTransactionTypeEnum.AUTHORIZATION);
                case "消费冲正" -> transactionDO.setTransType(CardTransactionTypeEnum.AUTHORIZATION_BACK);
                case "退款" -> transactionDO.setTransType(CardTransactionTypeEnum.REFUND);
                default -> transactionDO.setTransType(CardTransactionTypeEnum.OTHER);
            }
            transactionDO.setOriginTransType(paymentType);
            transactionDO.setTransStatus(CardTransactionStatusEnum.SUCCESS);
            transactionDO.setTransAmount(record.getBigDecimal("amount").negate());
            transactionDO.setTransCurrency("USD");
            transactionDO.setTransTime(LocalDateTimeUtil.parse(record.getString("transTime"), "yyyy-MM-dd HH:mm:ss"));
            transactionDO.setChinaTime(LocalDateTimeUtil.parse(record.getString("transTime"), "yyyy-MM-dd HH:mm:ss"));
            transactionDO.setStatTime(transactionDO.getTransTime());
            transactionDO.setTransDetail(record.getString("merchantName"));
            transactionDO.setRemark(record.getString("remark"));
            result.add(transactionDO);

        }
        return result;
    }

    public List<CardDO> getHuitongCardList(String startId) {
        Map<String, Object> params = new HashMap<>();
        params.put("vccModel", "vccModel.01");
        params.put("terminal", "terminal.web");
        String userId = "*********";
        String time = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        params.put("userId", userId);
        params.put("time", time);
        params.put("startId", startId);
        params.put("pageSize", "100");
        Map<String, String> headers = new HashMap<>();
        headers.put("x-enterno", "E20241101021639");
        headers.put("x-timezone", "TimeZone.CST");
        headers.put("x-token", "L20250402101625136243DAEB6D284F2F22E9");
        headers.put("x-user", "*********#terminal.web");
        headers.put("x-sign", getSign(userId, time) + "#" + time);
        headers.put("origin", "https://www.huitongcard.com");
        headers.put("Host", "www.huitongcard.com");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put("referer", "https://www.huitongcard.com/admin/card/index");
        String cookie = "_gcl_au=1.1.108417122.1743513048; web-user-key={%22resp%22:%220000%22%2C%22isLog%22:%221%22%2C%22userId%22:%22*********%22%2C%22invitePeople%22:%22*********%22%2C%22telCode%22:%221%22%2C%22name%22:%22YMDJ618%22%2C%22icon%22:%22path=%22%2C%22userType%22:%22userType.02%22%2C%22mobileNo%22:%225182780255%22%2C%22email%22:%<EMAIL>%22%2C%22userIdNumber%22:%22%22%2C%22userLevel%22:%22userLevel.01%22%2C%22isAuth%22:1%2C%22enterNo%22:%22E20241101021639%22%2C%22enterType%22:%22enterType.03%22%2C%22enterName%22:%22YMDJ618%22%2C%22enterNameEn%22:%22YMDJ618%22%2C%22regeditNo%22:%22%22%2C%22contacts%22:%22%22%2C%22country%22:%22%22%2C%22countryName%22:%22%22%2C%22status%22:%22enterState.02%22%2C%22isVerifyPayPwd%22:%220%22%2C%22isPayPass%22:0%2C%22isMSO%22:false%2C%22isBl%22:false%2C%22token%22:%22L20250402101625136243DAEB6D284F2F22E9%22%2C%22expireMinute%22:%2230%22%2C%22memberType%22:%22memberType.01%22%2C%22memberRole%22:%22ADMIN%22%2C%22isMainAdmin%22:true%2C%22isAdmin%22:true%2C%22isVccLogin%22:true%2C%22needLoginPwdUpdate%22:false%2C%22loginTime%22:%222025-04-02%2010:16%22%2C%22isVccAdmin%22:true}";
        String response = HttpRequest.get("https://www.huitongcard.com/vcc-api/auth/card/list")
            .headerMap(headers, true)
            .cookie(cookie)
            .form(params)
            .execute()
            .body();
        JSONObject jsonObject = JSONObject.parseObject(response);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray records = data.getJSONArray("records");
        List<CardDO> result = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            JSONObject record = records.getJSONObject(i);
            CardDO cardDO = new CardDO();
            cardDO.setCardNumber(record.getString("cardNo"));
            cardDO.setPlatform(CardPlatformEnum.HUI_TONG);
            cardDO.setBalance(record.getBigDecimal("balance"));
            cardDO.setStatus(CardStatusEnum.NORMAL);
            cardDO.setOpenTime(LocalDateTimeUtil.parse(record.getString("createTime"), "yyyy-MM-dd HH:mm:ss"));
            cardDO.setPlatformCardId(record.getString("cardId"));
            cardDO.setRemark(record.getString("remark"));
            String platformAdId = "";
            if (StringUtils.isNotBlank(cardDO.getRemark()) && !CommonUtils.containChineseChar(cardDO.getRemark())) {
                if (cardDO.getRemark().contains("-")) {
                    String[] strs = cardDO.getRemark().split("-");
                    if (strs.length > 1) {
                        platformAdId = strs[1];
                    }
                } else {
                    platformAdId = cardDO.getRemark();
                }
            }
            if (StringUtils.isNumeric(platformAdId)) {
                cardDO.setPlatformAdId(platformAdId);
            } else {
                cardDO.setPlatformAdId("");
            }
            cardDO.setCardName(record.getString("id"));
            result.add(cardDO);
        }
        return result;
    }

    public String getSign(String userId, String time) {

        String md5Key = "9$#SDFHFKSDKSDJFI8&^54339()LD;FD";
        String str = "userId=" + userId + "&time=" + time + "&key=" + md5Key;
        return SecureUtil.md5(str);
    }

//    public static void main(String[] args) throws IOException {
//        // withdrawPhotonPay();
//        // System.out.println(RabbitUtils.getAllAdSpent("kuecex1",true));
//        //System.out.println(RabbitUtils.getAllAdSpentV2("47557", true, "socks://NwzWQBze:33r1mVk7@178.212.138.34:63201", "{\"sec-ch-ua-full-version-list\":\"\\\"Google Chrome\\\";v=\\\"131.0.6778.140\\\", \\\"Chromium\\\";v=\\\"131.0.6778.140\\\", \\\"Not_A Brand\\\";v=\\\"24.0.0.0\\\"\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"x-fb-upl-sessionid\":\"upl_1750753686948_15b46c72-19e6-4912-b2ee-44bce613b815\",\"referer\":\"https://business.facebook.com/billing_hub/accounts/details/?business_id=****************&asset_id=***************&payment_account_id=***************\",\"x-bh-flowsessionid\":\"\",\"sec-ch-ua\":\"\\\"Google Chrome\\\";v=\\\"131\\\", \\\"Chromium\\\";v=\\\"131\\\", \\\"Not_A Brand\\\";v=\\\"24\\\"\",\"x-fb-friendly-name\":\"BillingHubPaymentSettingsViewQuery\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-model\":\"\\\"\\\"\",\"x-asbd-id\":\"359341\",\"x-fb-lsd\":\"bUkoCyUdwAQlbiTilYu9af\",\"sec-ch-prefers-color-scheme\":\"light\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36\",\"content-type\":\"application/x-www-form-urlencoded\",\"sec-ch-ua-platform-version\":\"\\\"10.0.0\\\"\",\"accept\":\"*/*\",\"cookie\":\"datr=X6y-ZwCoRxfNxZVKjGpHqKiB; sb=X6y-ZzyLrPsiljWJl7u4L6Gl; c_user=**************; ps_l=1; ps_n=1; locale=zh_CN; cppo=1; presence=C%7B%22t3%22%3A%5B%5D%2C%22utc3%22%3A1740549416697%2C%22v%22%3A1%7D; i_user=**************; ar_debug=1; fr=1GLQAZXcCFQDEOqIQ.AWcRnlFyo7h9nEZVEfNT4ansvIFH8kpgJY5ifv4eYLsEWhEaZQk.BoVQx1..AAA.0.0.BoVQx1.AWewiljA58Ky2iTCobuGW2C9V7I; xs=20%3Aw_PEJbnQlE-S1w%3A2%3A1740549364%3A-1%3A-1%3A%3AAcXmuZCCiNF4qygyVWh2xBR5pCxFFEij37Q2nc-lSwdw; wd=2048x1018; dpr=1.0000000149011612; alsfid={\\\"id\\\":\\\"fb5197a67\\\",\\\"timestamp\\\":1750753802779.4}\",\"origin\":\"https://business.facebook.com\"}"));
//        //System.out.println(FacebookUtils.isAuthTransaction("FACEBK *77NRSFF"));
//        List<String> adsList = Files.readAllLines(new File("C:\\Users\\<USER>\\Downloads\\新建 文本文档.txt").toPath());
//        List<String> emails = new ArrayList<>();
//        System.out.println("提取到的邮箱地址：");
//        for (String ads : adsList) {
//            if (StringUtils.isBlank(ads)) {
//                continue;
//            }
//            emails.addAll(extractEmails(ads));
//        }
//        Files.write(new File("C:\\Users\\<USER>\\Downloads\\新建 文本文档1.txt").toPath(), CollUtil.join(emails, "\n")
//            .getBytes());
//    }

    public static List<String> extractEmails(String text) {
        // 正则表达式匹配邮箱
        String emailRegex = "[a-zA-Z0-9]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}";
        Pattern pattern = Pattern.compile(emailRegex);
        Matcher matcher = pattern.matcher(text);
        List<String> result = new ArrayList<>();
        while (matcher.find()) {
            System.out.println(matcher.group());
            result.add(matcher.group());
        }
        return result;
    }

    public static void queryCardNumberCountry(String cardNumber) {
        String prefix = StringUtils.substring(cardNumber, 0, 6);
        String response = HttpRequest.get("https://lookup.binlist.net/" + prefix)
            .header("Accept-Version", "3")
            .execute()
            .body();
        System.out.println(response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        String scheme = jsonObject.getString("scheme");
        JSONObject countryJson = jsonObject.getJSONObject("country");
        String country = countryJson.getString("alpha2");
        System.out.printf("%s\t%s\t%s%n", cardNumber, scheme, country);
    }

    public static String _0x430879(String _0x575316, String _0x3887bf) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < _0x575316.length(); i++) {
            char c = _0x575316.charAt(i);
            char keyChar = _0x3887bf.charAt(i % _0x3887bf.length());
            result.append((char)(c ^ keyChar));
        }
        return result.toString();
    }

    public static String xorDecrypt(String encrypted, String key) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < encrypted.length(); i++) {
            char encryptedChar = encrypted.charAt(i);
            char keyChar = key.charAt(i % key.length());
            result.append((char)(encryptedChar ^ keyChar));
        }
        return result.toString();
    }


    @Test
    void testGetAllAdInfos() {
        List<PersonalAccountDO> list = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
                .eq(PersonalAccountDO::getIsSync, true).eq(PersonalAccountDO::getBrowserNo, "37918").last("limit 300"));
        for (PersonalAccountDO personalAccountDO : list) {
            try {
                // 使用 CompletableFuture 实现超时控制
                CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                    adAccountEffectiveSyncService.syncAdSetsEffectiveData(
                        personalAccountDO.getBrowserNo(),
                        personalAccountDO.getProxy(),
                        personalAccountDO.getHeaders(),
                        true,
                        "2025-07-31"
                    )
                );

                // 设置超时时间为1分钟
                future.get(60, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                System.out.println("账户 " + personalAccountDO.getBrowserNo() + " 同步超时，跳过当前账户，继续处理下一个账户");
            } catch (Exception e) {
                System.out.println("账户 " + personalAccountDO.getBrowserNo() + " 同步过程中发生异常: " + e.getMessage());
            }
        }
//        String resp = RabbitUtils.getAdInfosByTimeRangeV2("123", true, "2025-07-01", "2025-07-30", "socks://NwzWQBze:33r1mVk7@45.129.129.164:63339", "{\"sec-ch-ua-full-version-list\":\"\\\"Google Chrome\\\";v=\\\"129.0.6668.71\\\", \\\"Not=A?Brand\\\";v=\\\"8.0.0.0\\\", \\\"Chromium\\\";v=\\\"129.0.6668.71\\\"\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"x-fb-upl-sessionid\":\"upl_1753599870552_a87a4f68-085c-4272-8730-fb439d944b3e\",\"referer\":\"https://business.facebook.com/billing_hub/accounts/details/?business_id=****************&asset_id=****************&payment_account_id=****************\",\"x-bh-flowsessionid\":\"\",\"sec-ch-ua\":\"\\\"Google Chrome\\\";v=\\\"129\\\", \\\"Not=A?Brand\\\";v=\\\"8\\\", \\\"Chromium\\\";v=\\\"129\\\"\",\"x-fb-friendly-name\":\"BillingHubPaymentSettingsViewQuery\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-model\":\"\\\"\\\"\",\"x-asbd-id\":\"359341\",\"x-fb-lsd\":\"HGSDGSflP27iQFIR1fjgJL\",\"sec-ch-prefers-color-scheme\":\"light\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36\",\"content-type\":\"application/x-www-form-urlencoded\",\"sec-ch-ua-platform-version\":\"\\\"15.0.0\\\"\",\"accept\":\"*/*\",\"cookie\":\"datr=V5EdaNigw3oNQGzZkyCPZPpK; sb=V5EdaBFYIhmBEgfPeESxFLHD; locale=en_US; c_user=**************; ps_l=1; ps_n=1; ar_debug=1; cppo=1; presence=EDvF3EtimeF1751598987EuserFA2**************A2EstateFDutF0CEchF_7bCC; fr=1sqEZCS7hwelnbeZN.AWcNgbljyniQgdzHTAjTYvPp3DC6qOfj8bzKhkaR2F-_ki9Sny8.Bohc9w..AAA.0.0.Bohc9w.AWfCiWHuyn22l7cReab_yiKmAWY; xs=12%3AZEfmKVMd4ODGNA%3A2%3A1746768448%3A-1%3A-1%3A%3AAcWDdIlQfMe_zPMODtNxm5TkCsDTxz-XJJUJf_yVIiOr; dpr=1.75; wd=2195x1100; alsfid={\\\"id\\\":\\\"f2229f108\\\",\\\"timestamp\\\":1753599927025.5999}\",\"origin\":\"https://business.facebook.com\"}");
////        String resp = RabbitUtils.getAllAdInfos("123", true,  "socks://NwzWQBze:33r1mVk7@45.129.129.164:63339", "{\"sec-ch-ua-full-version-list\":\"\\\"Google Chrome\\\";v=\\\"129.0.6668.71\\\", \\\"Not=A?Brand\\\";v=\\\"8.0.0.0\\\", \\\"Chromium\\\";v=\\\"129.0.6668.71\\\"\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"x-fb-upl-sessionid\":\"upl_1753599870552_a87a4f68-085c-4272-8730-fb439d944b3e\",\"referer\":\"https://business.facebook.com/billing_hub/accounts/details/?business_id=****************&asset_id=****************&payment_account_id=****************\",\"x-bh-flowsessionid\":\"\",\"sec-ch-ua\":\"\\\"Google Chrome\\\";v=\\\"129\\\", \\\"Not=A?Brand\\\";v=\\\"8\\\", \\\"Chromium\\\";v=\\\"129\\\"\",\"x-fb-friendly-name\":\"BillingHubPaymentSettingsViewQuery\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-model\":\"\\\"\\\"\",\"x-asbd-id\":\"359341\",\"x-fb-lsd\":\"HGSDGSflP27iQFIR1fjgJL\",\"sec-ch-prefers-color-scheme\":\"light\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36\",\"content-type\":\"application/x-www-form-urlencoded\",\"sec-ch-ua-platform-version\":\"\\\"15.0.0\\\"\",\"accept\":\"*/*\",\"cookie\":\"datr=V5EdaNigw3oNQGzZkyCPZPpK; sb=V5EdaBFYIhmBEgfPeESxFLHD; locale=en_US; c_user=**************; ps_l=1; ps_n=1; ar_debug=1; cppo=1; presence=EDvF3EtimeF1751598987EuserFA2**************A2EstateFDutF0CEchF_7bCC; fr=1sqEZCS7hwelnbeZN.AWcNgbljyniQgdzHTAjTYvPp3DC6qOfj8bzKhkaR2F-_ki9Sny8.Bohc9w..AAA.0.0.Bohc9w.AWfCiWHuyn22l7cReab_yiKmAWY; xs=12%3AZEfmKVMd4ODGNA%3A2%3A1746768448%3A-1%3A-1%3A%3AAcWDdIlQfMe_zPMODtNxm5TkCsDTxz-XJJUJf_yVIiOr; dpr=1.75; wd=2195x1100; alsfid={\\\"id\\\":\\\"f2229f108\\\",\\\"timestamp\\\":1753599927025.5999}\",\"origin\":\"https://business.facebook.com\"}");
////
//        System.out.println(resp);


    }

}
