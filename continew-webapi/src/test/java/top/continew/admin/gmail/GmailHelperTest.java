package top.continew.admin.gmail;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.biz.gmail.GMailHelper;

import java.net.SocketException;
import java.net.SocketTimeoutException;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/23 11:00
 */
@SpringBootTest
public class GmailHelperTest {
    @Resource
    private GMailHelper gmailHelper;


    @Test
    public void test() throws SocketException, SocketTimeoutException {
        gmailHelper.checkNewEmails();

    }
}
