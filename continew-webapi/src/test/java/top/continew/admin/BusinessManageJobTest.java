package top.continew.admin;

import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.job.BusinessManageJob;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/17 17:26
 */
@SpringBootTest
public class BusinessManageJobTest {
    @Resource
    private BusinessManageJob businessManageJob;

    @Test
    public void test() {
        JobArgs jobArgs = new JobArgs();
        jobArgs.setJobParams("2025-03-13");
        businessManageJob.statBusinessManageData(jobArgs);
    }
}
