package top.continew.admin.katai;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.biz.katai.strategy.impl.AmzOpsStrategyImpl;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.katai.AmzClient;
import top.continew.katai.amz.AmzConfig;
import top.continew.katai.amz.model.req.AmzCreateCardRequest;
import top.continew.katai.amz.model.req.AmzGetTaskDetailRequest;
import top.continew.katai.amz.model.req.AmzRechargeRequest;
import top.continew.katai.amz.model.resp.AmzCardTypeResponse;
import top.continew.katai.amz.model.resp.AmzTaskDetailResponse;
import top.continew.katai.amz.model.resp.AmzTaskResponse;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * AMZ客户端测试类
 *
 * <AUTHOR>
 * @since 2025/1/1
 */
@SpringBootTest
@Slf4j
public class AmzClientTest {
    @Resource
    private AmzOpsStrategyImpl amzOpsStrategy;

    private AmzClient getAmzClient() {
        AmzConfig config = new AmzConfig();
        config.setAppId("2025840271668471");
        config.setAppKey("510f473c8afba0679f2cc3fd654b1476fa99a2c6");
        config.setPrivateKey("MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCv4PZioynDI1xt" +
                "lRQX3Sx89HCombMmVwAHmBOYWcWj1231uZfqCLyz5LpKk/cyCjQXXg7iTVl6e2Ma" +
                "atytSsYCFbOrxpR4PdcCgFlX1j6a06YQBmjQLZR6ASx7xMeYqfQnxrLxh4lXM3Gf" +
                "h3Ps5E/gjwZUBz0Lu6kb5U9H/BPyxzZLtaN2VxaE1iXVQFzrOS38aFkp2+P0jc6P" +
                "nMrY5g66MM1NRNGAapseq7dvu2/Lw3kEEQR8VRwOcQ9tIJX2996bfodNUs65Ayke" +
                "+Vnn4ongFM5nDhTbrehiSpl98ZIngbq1LrDDIIFlLRSAIfVTEzFc/ybCnUfndNaV" +
                "HaYYrGNVAgMBAAECggEAOwj56tX1uKhv5+B4ZCI7Qp4SCqW8uovNWL81JJhNfNdc" +
                "83Qeh9Hy9Tc7SLkt6j/+iDFsMY3hEPnFpCJmfExy9lQugOQhBdNMDmzikFc7oGCW" +
                "hx5/pBEIm4M1WT4N+TJi388UVXo3IWwFIZUn1kY0gZ3AdAgR95RACTsJR5JXZ8/j" +
                "57fqLMRN0aXKgUun3BCUxiSgDBNhnqYIorHal0ZmFVxt8D8dD0XMBleIQzszG1Rn" +
                "hnHF8qGoPyGtJuXByyBJjSDG1UWYtLoiVEYpl90EG4Ky2qzDzjGYoGEThQUBEggS" +
                "8f0W+a6pxVdMENdv9lizeTRsfSZbTIhKMpvXrdnaQQKBgQDaxfJ+Ro6OD4NVwe5G" +
                "5J95QaLkSDhulO1qSkFXgfMf44baNeAHC3zpmcVu8pEe6/Edvs+SDcw2giJorOR9" +
                "lPuNPfrpY+rGVPqFCPjde3KFwWKRoX0doN5pXlthUuUa4fF/qbFhUfazQnJtkSad" +
                "naYrLJ3Iq+ibQz8mWYbjfPMjTQKBgQDNznmZFmKJLi1EyUIbJkW4d+jg8DcYKE5n" +
                "GhtcJQ84KDaDtqDBQjnVaEbR9uwZK+O7nlYuKXWpU64w+TlpHOeqSDDIOY9YIGka" +
                "VzZYdrHWctNOg7PxNrKmKfgQpXfg1ZkcOm83wzYZx2LdxHbmPxDJvszgql/y3+Ql" +
                "7FZs2BSsKQKBgFk48bTeQbVeTknjVbJD+2YQhsZjTeCLjGgU8KEntmgC+zRzVHKL" +
                "FN6QgBzHgLgDscpXz3/ZcLeqSy41lNpCsHTiGjqlLVLFxYYMKrLpbcNvIywRmF1F" +
                "BnAis1H15MoZ68wNAPKX4u0o5FIbKIyPhv2ErLTyp2LrAg455PFSPDbhAoGAA44J" +
                "c6d7VDtD9Og1CBsFIUaQ8zGzAevQTt+YkWLifGnVZzkEVdI7BYCjmITXVyf26wq1" +
                "Gl8E9UPIwnfLkhE5vys4DH1SCIemyRmwip6iO74IAFcuCICVXWFkM4VoKK4H0wD/" +
                "YsswaAmVn5cBJsD4HUP6bfcDaUKMwkruwzqIQOECgYAHQjxSvKN82oHGfmiXIEhS" +
                "fO4CA7DXTPbe1O+jLUsEI4fIdldNJsIQy8DpiYg6kY5hrEgfILaP3th1GQHq73pP" +
                "WiCO1ALR1SqrnOA4Rvx9X1Ojm+PgQEHrScv2k/q4iJyyDGmOnvN6IMbwpCnN/Pw1" +
                "8KSp7hYIHYopSoFQAyyRAQ==");
        config.setPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr+D2YqMpwyNcbZUUF90s" +
                "fPRwqJmzJlcAB5gTmFnFo9dt9bmX6gi8s+S6SpP3Mgo0F14O4k1ZentjGmrcrUrG" +
                "AhWzq8aUeD3XAoBZV9Y+mtOmEAZo0C2UegEse8THmKn0J8ay8YeJVzNxn4dz7ORP" +
                "4I8GVAc9C7upG+VPR/wT8sc2S7WjdlcWhNYl1UBc6zkt/GhZKdvj9I3Oj5zK2OYO" +
                "ujDNTUTRgGqbHqu3b7tvy8N5BBEEfFUcDnEPbSCV9vfem36HTVLOuQMpHvlZ5+KJ" +
                "4BTOZw4U263oYkqZffGSJ4G6tS6wwyCBZS0UgCH1UxMxXP8mwp1H53TWlR2mGKxj" +
                "VQIDAQAB");
        config.setEndpoint("ymapi.amzkeys.com:15970");
        config.setReadTimeout(30000);
        config.setConnectTimeout(10000);
        return new AmzClient(config);
    }

    /**
     * 测试获取卡片类型
     */
    @Test
    public void testGetCardTypes() {
        try {
            AmzClient client = getAmzClient();
            List<AmzCardTypeResponse> result = client.getCardTypes();
            log.info("AMZ获取卡片类型结果: {}", result);
            //2025-06-27 18:00:03 INFO  [main] top.continew.admin.katai.AmzClientTest - AMZ获取卡片类型结果: [AmzCardTypeResponse(cardType=532959, newCardFee=0.5, serviceFee=0.5, minOpenCardAmount=10.00, minRechargeAmount=10.00), AmzCardTypeResponse(cardType=485953, newCardFee=3, serviceFee=3, minOpenCardAmount=30.00, minRechargeAmount=40.00), AmzCardTypeResponse(cardType=428837, newCardFee=3, serviceFee=3, minOpenCardAmount=10.00, minRechargeAmount=10.00), AmzCardTypeResponse(cardType=540524, newCardFee=3, serviceFee=5, minOpenCardAmount=10.00, minRechargeAmount=10.00), AmzCardTypeResponse(cardType=481390, newCardFee=3, serviceFee=3, minOpenCardAmount=50.00, minRechargeAmount=50.00), AmzCardTypeResponse(cardType=474115, newCardFee=3, serviceFee=3, minOpenCardAmount=50.00, minRechargeAmount=50.00), AmzCardTypeResponse(cardType=472593, newCardFee=2.00, serviceFee=3, minOpenCardAmount=50.00, minRechargeAmount=50.00), AmzCardTypeResponse(cardType=428816, newCardFee=3, serviceFee=3, minOpenCardAmount=10.00, minRechargeAmount=10.00), AmzCardTypeResponse(cardType=467845, newCardFee=4, serviceFee=5, minOpenCardAmount=50.00, minRechargeAmount=50.00), AmzCardTypeResponse(cardType=552905, newCardFee=3, serviceFee=5, minOpenCardAmount=10.00, minRechargeAmount=10.00), AmzCardTypeResponse(cardType=458178, newCardFee=0, serviceFee=0, minOpenCardAmount=10.00, minRechargeAmount=10.00), AmzCardTypeResponse(cardType=450306, newCardFee=0.5, serviceFee=0.5, minOpenCardAmount=10.00, minRechargeAmount=10.00), AmzCardTypeResponse(cardType=4367971, newCardFee=0.5, serviceFee=0.5, minOpenCardAmount=10.00, minRechargeAmount=20.00), AmzCardTypeResponse(cardType=4462221, newCardFee=0.5, serviceFee=0.5, minOpenCardAmount=10.00, minRechargeAmount=20.00)]
        } catch (Exception e) {
            log.error("测试获取卡片类型失败", e);
        }
    }

    /**
     * 测试创建卡片
     */
    @Test
    public void testCreateCard2() {
//        try {
//            JSONObject data = new JSONObject();
//            data.put("cardBin", "4866993");
//            CardDO result = amzOpsStrategy.openCard(data);
//            log.info("AMZ创建卡片: {}", result);
//        } catch (Exception e) {
//            log.error("测试创建卡片失败", e);
//        }

        //AmzTaskResponse(taskId=3404591, orderNo=null, taskStatus=1, item=[{"card_type":"4866993","request_id":"***********","card_no":"****************","cvv":"497","valid_date":"2028-06","open_card_amount":"10.00","create_time":"2025-06-30 18:07:34","currency":"USD"}], data=AmzCardResponse(cardType=4866993, cardStatus=null, requestId=***********, cardNo=****************, cvv=497, validDate=2028-06, openCardAmount=10.00, createTime=2025-06-30 18:07:34, totalPrice=null)
        //{\"account_type\":\"USD\",\"amount\":10.00,\"card_type\":486699,\"request_id\":***********}
        CardDO cardDO = amzOpsStrategy.getApplyCardResult("3404591");
        log.info("card:{}", JSON.toJSONString(cardDO));

    }

    @Test
    public void testRecharge2() {
        CardDO cardDO = amzOpsStrategy.getApplyCardResult("3404591");
        amzOpsStrategy.rechargeCard(cardDO, new BigDecimal("20.00"));
    }

    @Test
    public void testFreeze() {
        CardDO cardDO = amzOpsStrategy.getApplyCardResult("3404591");
        amzOpsStrategy.inactiveCard(cardDO);
    }

    /**
     * 测试创建卡片
     */
    @Test
    public void testCreateCard() {
        try {
            AmzClient client = getAmzClient();

            //AmzTaskResponse(taskId=3404439, orderNo=card_20250630171116999872, taskStatus=null, item=null, data=null)
            AmzCreateCardRequest request = AmzCreateCardRequest.builder()
                    .accountType("USD")
                    .amount(new BigDecimal("10.00"))
                    .cardType(4866993)
                    .number(1)
                    .build();

            AmzTaskResponse task = client.createCard(request);
            log.info("AMZ创建卡片任务ID: {}", task);
        } catch (Exception e) {
            log.error("测试创建卡片失败", e);
        }

    }

    /**
     * 测试充值
     */
    @Test
    public void testRecharge() {
        try {
            AmzClient client = getAmzClient();

            AmzRechargeRequest request = AmzRechargeRequest.builder()
                    .cardType(123456)
                    .requestId(System.currentTimeMillis())
                    .amount(new BigDecimal("50.00"))
                    .accountType("USD")
                    .build();

            AmzTaskResponse task = client.recharge(request);
            log.info("AMZ充值任务ID: {}", task);
        } catch (Exception e) {
            log.error("测试充值失败", e);
        }
    }

    @Test
    public void testGetCardList() {
        List<CardDO> list = amzOpsStrategy.getCardList(null, null, 3);
        for (CardDO cardDO : list) {
            log.info("card:{}", com.alibaba.fastjson.JSON.toJSONString(cardDO));
            log.info("card balance:{},{}", JSON.toJSONString(cardDO),
                    amzOpsStrategy.getCardBalance(cardDO));
            break;
        }
    }

}