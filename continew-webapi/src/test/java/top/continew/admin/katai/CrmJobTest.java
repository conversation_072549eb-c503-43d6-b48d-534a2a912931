package top.continew.admin.katai;

import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.job.CardJob;
import top.continew.admin.job.CrmJob;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/14 10:10
 */
@SpringBootTest
public class CrmJobTest {
    @Resource
    private CrmJob crmJob;

    @Test
    public void testUpdateLongTermFollowUp() {
        crmJob.updateLongTermFollowUp();
    }
}
