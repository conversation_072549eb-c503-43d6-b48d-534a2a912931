package top.continew.admin.katai;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.katai.strategy.impl.GzyOpsStrategyImpl;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.service.CardService;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 14:23
 */
@SpringBootTest
@Slf4j
public class GzyClientTest {
    @Resource
    private GzyOpsStrategyImpl gzyOpsStrategy;
    @Resource
    private CardService cardService;

    @Test
    public void testGetTransactionList() {
        LocalDateTime end = LocalDateTimeUtil.now();
        LocalDateTime start = end.minusDays(7);
        gzyOpsStrategy.getCardTransactionList(start, end, null);
    }


    @Test
    public void testGetCardBalanceList() {
        LocalDateTime end = LocalDateTimeUtil.now();
        LocalDateTime start = end.minusDays(7);
        List<CardBalanceDO> list = gzyOpsStrategy.getCardBalanceList(start, end);
        for (CardBalanceDO cardBalanceDO : list) {
            log.info("cardBalance:{}-{}-{}", cardBalanceDO.getCardNumber(), cardBalanceDO.getAmount(), cardBalanceDO.getAfterAmount());
        }
    }

    @Test
    public void testGetCardList() {
        LocalDateTime end = LocalDateTimeUtil.now();
        LocalDateTime start = end.minusDays(7);
        List<CardDO> list = gzyOpsStrategy.getCardList(start, end, null);
        for (CardDO cardDO : list) {
            log.info("card:{}", JSON.toJSONString(cardDO));

            log.info("card detail:{},{}", JSON.toJSONString(cardDO), gzyOpsStrategy.getCardDetail(cardDO.getPlatformCardId()));
            break;
        }
    }

    @Test
    public void testGetCardDetail() {


        for(int i = 0; i < 10; i++) {
            CardDO card = gzyOpsStrategy.getCardDetail("XR1900952556939960320");
            log.info("cardNumber:{}", card.getCardNumber());
        }
    }

    @Test
    public void testGetCardBinList() {
        List<LabelValueResp<String>> list = cardService.getCardBinList(CardPlatformEnum.PHOTON_PAY);

        for (LabelValueResp<String> stringLabelValueResp : list) {
            log.info("label:{}", stringLabelValueResp.getLabel());
        }
    }
}
