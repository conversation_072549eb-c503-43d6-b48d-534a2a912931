package top.continew.admin.katai;

import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.job.CardJob;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/14 10:10
 */
@SpringBootTest
public class CardJobTest {
    @Resource
    private CardJob cardJob;

    @Test
    public void testSyncCardJob() {
        cardJob.syncCardData();
    }

    @Test
    public void testSyncCardTransactionData() {
        JobArgs jobArgs = new JobArgs();
        jobArgs.setJobParams("7200");
        cardJob.syncCardTransactionData(jobArgs);
    }

    @Test
    public void syncCardBillData() {
        JobArgs jobArgs = new JobArgs();
        jobArgs.setJobParams("7200");
        cardJob.syncCardBillData(jobArgs);
    }

    @Test
    public void testLoadGzyCardSensitiveInfo() {
        cardJob.loadGzyCardSensitiveInfo();
    }

    @Test
    public void testConvertCardTransactionTime() {
        cardJob.convertCardTransactionTime();
    }
}
