-- liquibase formatted sql

-- changeset hans:1
-- comment 初始化业务表结构
create table biz_ad_account
(
    id                  bigint                  not null comment 'ID'
        primary key,
    business_manager_id bigint null comment '关联BM5 ID',
    platform_account_id varchar(64) null comment '平台用户ID',
    platform_ad_id      varchar(64)             not null comment '平台广告ID',
    full_card_number    varchar(64)  default '' not null comment '完整卡号',
    fuzzy_card_number   varchar(64)  default '' not null comment '模糊卡号',
    timezone            varchar(64)             not null comment '时区',
    account_status      int          default 1  not null comment '账号状态',
    keep_status         int          default 1  not null comment '养号状态',
    sale_status         int          default 1  not null comment '出售状态',
    personal_area       varchar(64)  default '' not null comment '个号地区',
    browser_no          varchar(20)  default '' not null comment '浏览器编号',
    clear_status        int          default 1  not null comment '清零状态',
    remark              varchar(255) default '' not null comment '备注',
    create_time         datetime                not null comment '创建时间',
    create_user         bigint                  not null comment '创建人',
    update_time         datetime null comment '更新时间',
    update_user         bigint null comment '更新人',
    constraint biz_ad_account_platform_ad_id_uindex
        unique (platform_ad_id)
) comment '广告账号';

create table biz_ad_account_balance_record
(
    id            bigint                  not null comment 'ID'
        primary key,
    ad_account_id bigint                  not null comment '关联广告ID',
    action        int                     not null comment '交易动作',
    type          int                     not null comment '交易类型',
    amount        decimal(10, 2)          not null comment '交易金额',
    trans_time    datetime                not null comment '交易时间',
    remark        varchar(255) default '' not null comment '备注',
    create_time   datetime                not null comment '创建时间',
    create_user   bigint                  not null comment '创建人'
) comment '广告户余额变更记录';

create table biz_ad_account_browser_log
(
    id                  bigint                  not null comment 'ID'
        primary key,
    name                varchar(64)  default '' not null comment '名称',
    label               varchar(64)  default '' not null comment '标签',
    data                longtext null comment '操作内容',
    ops_time            bigint                  not null comment '操作时间',
    platform_account_id varchar(64)  default '' not null comment '平台账号ID',
    active_code         varchar(20)  default '' not null comment '激活码',
    env                 varchar(255) default '' not null comment '操作环境',
    create_time         datetime                not null comment '创建时间',
    create_user         bigint null comment '创建人'
) comment '账号浏览器操作记录';

create table biz_ad_account_order
(
    id             bigint                      not null comment 'ID'
        primary key,
    order_no       varchar(64)                 not null comment '订单号',
    customer_id    bigint                      not null comment '关联客户',
    ad_account_id  bigint                      not null comment '关联广告户',
    customer_bm_id varchar(255)   default ''   not null comment '客户BM ID',
    pay_amount     decimal(10, 2) default 0.00 not null comment '开户费',
    pay_time       datetime                    not null comment '支付时间',
    end_time       datetime null comment '终止时间',
    status         int                         not null comment '状态',
    remark         varchar(255)   default ''   not null comment '备注',
    create_user    bigint                      not null comment '创建人',
    create_time    datetime                    not null comment '创建时间',
    update_time    datetime null comment '更新时间',
    update_user    bigint                      not null comment '更新人'
) comment '下户订单';

create table biz_business_manager
(
    id          bigint                 not null comment 'ID'
        primary key,
    channel_id  bigint                 not null comment '关联渠道',
    platform_id varchar(64)            not null comment 'BM5 ID',
    browser_no  varchar(64) default '' not null comment '浏览器编号',
    create_time datetime               not null comment '创建时间',
    create_user bigint                 not null comment '创建人',
    update_time datetime null comment '更新时间',
    update_user bigint null comment '更新人'
) comment 'BM5账号';

create table biz_business_manager_channel
(
    id          bigint      not null comment 'ID'
        primary key,
    name        varchar(64) not null comment '名称',
    create_time datetime    not null comment '创建时间',
    create_user bigint      not null comment '创建人',
    update_time datetime null comment '更新时间',
    update_user bigint null comment '更新人'
) comment 'bm5账号渠道';

create table biz_card
(
    id               bigint                 not null comment 'ID'
        primary key,
    card_number      varchar(64)            not null comment '卡号',
    platform         int                    not null comment '所属平台',
    balance          decimal(10, 2)         not null comment '余额',
    status           tinyint(1)  default 1  not null comment '状态(1=正常，2=锁定，3=冻结）',
    open_time        datetime null comment '开卡日期',
    platform_card_id varchar(64) default '' not null comment '第三方平台ID',
    create_time      datetime               not null comment '创建时间',
    update_time      datetime null comment '更新时间',
    constraint biz_card_card_number_uindex
        unique (card_number)
) comment '卡片';

create table biz_card_balance
(
    id             bigint                 not null comment 'ID'
        primary key,
    platform       int                    not null comment '卡台平台',
    card_number    varchar(64)            not null comment '卡号',
    type           int                    not null comment '类型（1=充值，2=提现，3=开卡手续费）',
    origin_type    varchar(64) default '' not null comment '原始类型',
    amount         decimal(10, 2)         not null comment '变更金额',
    after_amount   decimal(10, 2) null comment '交易后余额',
    trans_time     datetime               not null comment '交易时间',
    transaction_id varchar(64) default '' not null comment '交易编号',
    create_time    datetime               not null comment '创建时间'
) comment '卡片余额流水';

create table biz_card_transaction
(
    id                bigint                     not null comment 'ID'
        primary key,
    platform          tinyint                    not null comment '平台',
    card_number       varchar(64)                not null comment '卡号',
    transaction_id    varchar(64)  default ''    not null comment '交易编号',
    trans_type        int                        not null comment '交易类型（1=授权交易，2=授权回退， 99=其他）',
    origin_trans_type varchar(64)  default ''    not null comment '原始交易类型',
    trans_status      tinyint                    not null comment '交易状态(1=处理中，2=成功，3=撤销，4=失败）',
    trans_time        datetime null comment '交易时间',
    trans_amount      decimal(10, 2)             not null comment '交易金额',
    trans_currency    varchar(20)  default 'USD' not null comment '交易币种',
    trans_detail      varchar(255) default ''    not null comment '交易详情',
    create_time       datetime                   not null comment '创建时间',
    update_time       datetime null comment '更新时间'
) comment '卡片交易流水';

create table biz_clear_order
(
    id               bigint                  not null comment 'ID'
        primary key,
    order_no         varchar(64)             not null comment '订单号',
    customer_id      bigint                  not null comment '关联客户',
    ad_account_id    bigint                  not null comment '关联广告户',
    clear_amount     decimal(10, 2) null comment '清零金额',
    card_balance     decimal(10, 2) null comment '卡片余额',
    status           int          default 1  not null comment '状态',
    apply_message_id int                     not null comment '关联消息ID',
    handle_user      bigint null comment '处理人',
    handle_time      datetime null comment '处理时间',
    finish_time      datetime null comment '完成时间',
    remark           varchar(255) default '' not null comment '备注',
    certificate      varchar(255) default '' not null comment '凭证',
    create_time      datetime                not null comment '创建时间',
    update_time      datetime null comment '更新时间',
    update_user      bigint null comment '更新人'
) comment '清零订单';

create table biz_customer
(
    id                bigint                      not null comment 'ID'
        primary key,
    name              varchar(64)                 not null comment '名称',
    business_user_id  bigint                      not null comment '关联商务',
    fee_rate_percent  decimal(10, 2) default 0.00 not null comment '手续费百分比',
    fee_handle_method int                         not null comment '手续费扣款方式',
    balance           decimal(10, 2) default 0.00 not null comment '余额',
    telegram_chat_id  varchar(64)    default ''   not null comment 'TG群ID',
    remark            varchar(255)   default ''   not null comment '备注',
    create_time       datetime                    not null comment '创建时间',
    create_user       bigint                      not null comment '创建人',
    update_time       datetime null comment '更新时间',
    update_user       bigint null comment '更新人'
) comment '客户';

create table biz_customer_balance_record
(
    id          bigint                  not null comment 'ID'
        primary key,
    customer_id bigint                  not null comment '关联客户',
    action      int                     not null comment '交易动作',
    type        int                     not null comment '交易类型',
    amount      decimal(10, 2)          not null comment '交易金额',
    trans_time  datetime                not null comment '交易时间',
    remark      varchar(255) default '' not null comment '备注',
    create_user bigint                  not null comment '创建人',
    create_time datetime                not null comment '创建时间'
) comment '客户余额变更记录';

create table biz_recharge_order
(
    id               bigint                  not null comment 'ID'
        primary key,
    order_no         varchar(64)             not null comment '订单编号',
    customer_id      bigint                  not null comment '关联客户',
    ad_account_id    bigint                  not null comment '关联广告户',
    amount           decimal(10, 2)          not null comment '充值金额',
    status           int          default 1  not null comment '状态',
    apply_message_id int null comment '关联消息ID',
    handle_user      bigint null comment '处理人',
    handle_time      datetime null comment '接收时间',
    finish_time      datetime null comment '完成时间',
    certificate      varchar(255) default '' not null comment '凭证',
    remark           varchar(255) default '' not null comment '备注',
    create_time      datetime                not null comment '创建时间',
    update_time      datetime null comment '更新时间',
    update_user      bigint null comment '更新人'
) comment '充值订单';


-- changeset hans:2
-- comment 新增菜单数据
-- 新增菜单数据
insert into `sys_menu` (id, title, parent_id, type, path, name, component, redirect, icon, is_external, is_cache,
                        is_hidden, permission, sort, status, create_user, create_time)
values (664565031316242435, '卡片管理', 0, 1, '/card', 'Card', 'Layout', null, 'idcard', false, false, false, null, 200,
        1, 1, NOW()),
       (664567576520572934, '广告户管理', 0, 1, '/adAccount', 'AdAccount', 'Layout', null, 'apps', false, false, false,
        null, 100, 1, 1, NOW()),
       (664568395240325129, 'BM5管理', 0, 1, '/bm5', 'Bm5', 'Layout', null, 'branch', false, false, false, null, 90, 1,
        1, NOW()),
       (664568968329052175, '客户管理', 0, 1, '/customer', 'Customer', 'Layout', null, 'user-add', false, false, false,
        null, 300, 1, 1, NOW()),
       (664569109744205842, '订单管理', 0, 1, '/order', 'Order', 'Layout', null, 'ordered-list', false, false, false,
        null, 400, 1, 1, NOW()),
       (664569372961947669, '财务管理', 0, 1, '/finance', 'Finance', 'Layout', null, 'line-chart', false, false, false,
        null, 500, 1, 1, NOW()),
       (664863367952909209, '导出', 1874364040264835072, 3, null, null, null, null, null, false, false, false,
        'biz:adAccountBalanceRecord:export', 2, 1, 1, NOW()),
       (1874077184444895232, '卡片管理', 664565031316242435, 2, '/card/list', 'CardList', 'biz/card/index', null, null,
        false, false, false, null, 1, 1, 1, NOW()),
       (1874077184444895233, '列表', 1874077184444895232, 3, null, null, null, null, null, false, false, false,
        'biz:card:list', 1, 1, 1, NOW()),
       (1874077184444895234, '同步', 1874077184444895232, 3, null, null, null, null, null, false, false, false,
        'biz:card:sync', 2, 1, 1, NOW()),
       (1874077184444895235, '导出', 1874077184444895232, 3, null, null, null, null, null, false, false, false,
        'biz:card:export', 3, 1, 1, NOW()),
       (1874077560934010880, '余额流水', 664565031316242435, 2, '/card/balance', 'CardBalance', 'biz/cardBalance/index',
        null, null, false, false, false, null, 1, 1, 1, NOW()),
       (1874077560934010881, '列表', 1874077560934010880, 3, null, null, null, null, null, false, false, false,
        'biz:cardBalance:list', 1, 1, 1, NOW()),
       (1874077560934010882, '同步', 1874077560934010880, 3, null, null, null, null, null, false, false, false,
        'biz:cardBalance:detail', 2, 1, 1, NOW()),
       (1874077560934010883, '导出', 1874077560934010880, 3, null, null, null, null, null, false, false, false,
        'biz:cardBalance:export', 3, 1, 1, NOW()),
       (1874092543965171712, '交易流水', 664565031316242435, 2, '/card/transaction', 'CardTransaction',
        'biz/cardTransaction/index', null, null, false, false, false, null, 1, 1, 1, NOW()),
       (1874092543965171713, '列表', 1874092543965171712, 3, null, null, null, null, null, false, false, false,
        'biz:cardTransaction:list', 1, 1, 1, NOW()),
       (1874092543965171714, '同步', 1874092543965171712, 3, null, null, null, null, null, false, false, false,
        'biz:cardTransaction:sync', 2, 1, 1, NOW()),
       (1874092543965171715, '导出', 1874092543965171712, 3, null, null, null, null, null, false, false, false,
        'biz:cardTransaction:export', 3, 1, 1, NOW()),
       (1874362961431126016, '渠道管理', 664568395240325129, 2, '/bm5/channel', 'BusinessManagerChannel',
        'biz/businessManagerChannel/index', null, null, false, false, false, null, 1, 1, 1, NOW()),
       (1874362961435320320, '列表', 1874362961431126016, 3, null, null, null, null, null, false, false, false,
        'biz:businessManagerChannel:list', 1, 1, 1, NOW()),
       (1874362961435320321, '详情', 1874362961431126016, 3, null, null, null, null, null, false, false, false,
        'biz:businessManagerChannel:detail', 2, 1, 1, NOW()),
       (1874362961435320322, '新增', 1874362961431126016, 3, null, null, null, null, null, false, false, false,
        'biz:businessManagerChannel:add', 3, 1, 1, NOW()),
       (1874362961435320323, '修改', 1874362961431126016, 3, null, null, null, null, null, false, false, false,
        'biz:businessManagerChannel:update', 4, 1, 1, NOW()),
       (1874362961435320324, '删除', 1874362961431126016, 3, null, null, null, null, null, false, false, false,
        'biz:businessManagerChannel:delete', 5, 1, 1, NOW()),
       (1874362961435320325, '导出', 1874362961431126016, 3, null, null, null, null, null, false, false, false,
        'biz:businessManagerChannel:export', 6, 1, 1, NOW()),
       (1874362961544372224, '账号管理', 664568395240325129, 2, '/bm5/list', 'BusinessManager',
        'biz/businessManager/index', null, null, false, false, false, null, 1, 1, 1, NOW()),
       (1874362961544372225, '列表', 1874362961544372224, 3, null, null, null, null, null, false, false, false,
        'biz:businessManager:list', 1, 1, 1, NOW()),
       (1874362961544372226, '详情', 1874362961544372224, 3, null, null, null, null, null, false, false, false,
        'biz:businessManager:detail', 2, 1, 1, NOW()),
       (1874362961544372227, '新增', 1874362961544372224, 3, null, null, null, null, null, false, false, false,
        'biz:businessManager:add', 3, 1, 1, NOW()),
       (1874362961544372228, '修改', 1874362961544372224, 3, null, null, null, null, null, false, false, false,
        'biz:businessManager:update', 4, 1, 1, NOW()),
       (1874362961544372229, '删除', 1874362961544372224, 3, null, null, null, null, null, false, false, false,
        'biz:businessManager:delete', 5, 1, 1, NOW()),
       (1874362961544372230, '导出', 1874362961544372224, 3, null, null, null, null, null, false, false, false,
        'biz:businessManager:export', 6, 1, 1, NOW()),
       (1874364040264835072, '流水记录', 664567576520572934, 2, '/adAccount/balance', 'AdAccountBalance',
        'biz/adAccountBalanceRecord/index', null, null, false, false, false, null, 10, 1, 1, NOW()),
       (1874364040264835073, '列表', 1874364040264835072, 3, null, null, null, null, null, false, false, false,
        'biz:adAccountBalanceRecord:list', 1, 1, 1, NOW()),
       (1874364040365498368, '账号管理', 664567576520572934, 2, '/adAccount/list', 'AdAccount', 'biz/adAccount/index',
        null, null, false, false, false, null, 1, 1, 1, NOW()),
       (1874364040365498369, '列表', 1874364040365498368, 3, null, null, null, null, null, false, false, false,
        'biz:adAccount:list', 1, 1, 1, NOW()),
       (1874364040365498370, '详情', 1874364040365498368, 3, null, null, null, null, null, false, false, false,
        'biz:adAccount:detail', 2, 1, 1, NOW()),
       (1874364040365498371, '新增', 1874364040365498368, 3, null, null, null, null, null, false, false, false,
        'biz:adAccount:add', 3, 1, 1, NOW()),
       (1874364040365498372, '修改', 1874364040365498368, 3, null, null, null, null, null, false, false, false,
        'biz:adAccount:update', 4, 1, 1, NOW()),
       (1874364040365498373, '删除', 1874364040365498368, 3, null, null, null, null, null, false, false, false,
        'biz:adAccount:delete', 5, 1, 1, NOW()),
       (1874364040365498374, '导出', 1874364040365498368, 3, null, null, null, null, null, false, false, false,
        'biz:adAccount:export', 6, 1, 1, NOW()),
       (1874365715771842560, '余额流水', 664568968329052175, 2, '/customer/balance', 'CustomerBalance',
        'biz/customerBalanceRecord/index', null, null, false, false, false, null, 10, 1, 1, NOW()),
       (1874365715771842561, '列表', 1874365715771842560, 3, null, null, null, null, null, false, false, false,
        'biz:customerBalanceRecord:list', 1, 1, 1, NOW()),
       (1874365715771842566, '导出', 1874365715771842560, 3, null, null, null, null, null, false, false, false,
        'biz:customerBalanceRecord:export', 6, 1, 1, NOW()),
       (1874365715868311552, '客户列表', 664568968329052175, 2, '/customer/list', 'CustomerList', 'biz/customer/index',
        null, null, false, false, false, null, 1, 1, 1, NOW()),
       (1874365715868311553, '列表', 1874365715868311552, 3, null, null, null, null, null, false, false, false,
        'biz:customer:list', 1, 1, 1, NOW()),
       (1874365715868311554, '详情', 1874365715868311552, 3, null, null, null, null, null, false, false, false,
        'biz:customer:detail', 2, 1, 1, NOW()),
       (1874365715868311555, '新增', 1874365715868311552, 3, null, null, null, null, null, false, false, false,
        'biz:customer:add', 3, 1, 1, NOW()),
       (1874365715868311556, '修改', 1874365715868311552, 3, null, null, null, null, null, false, false, false,
        'biz:customer:update', 4, 1, 1, NOW()),
       (1874365715868311557, '删除', 1874365715868311552, 3, null, null, null, null, null, false, false, false,
        'biz:customer:delete', 5, 1, 1, NOW()),
       (1874365715868311558, '导出', 1874365715868311552, 3, null, null, null, null, null, false, false, false,
        'biz:customer:export', 6, 1, 1, NOW()),
       (1874365715868311559, '调整余额', 1874365715868311552, 3, null, null, null, null, null, false, false, false,
        'biz:customer:balance', 7, 1, 1, NOW()),
       (1874368123604328448, '下户订单', 664569109744205842, 2, '/order/adAccount', 'AdAccountOrder',
        'biz/adAccountOrder/index', null, null, false, false, false, null, 1, 1, 1, NOW()),
       (1874368123604328449, '列表', 1874368123604328448, 3, null, null, null, null, null, false, false, false,
        'biz:adAccountOrder:list', 1, 1, 1, NOW()),
       (1874368123604328450, '详情', 1874368123604328448, 3, null, null, null, null, null, false, false, false,
        'biz:adAccountOrder:detail', 2, 1, 1, NOW()),
       (1874368123604328451, '新增', 1874368123604328448, 3, null, null, null, null, null, false, false, false,
        'biz:adAccountOrder:add', 3, 1, 1, NOW()),
       (1874368123604328452, '修改', 1874368123604328448, 3, null, null, null, null, null, false, false, false,
        'biz:adAccountOrder:update', 4, 1, 1, NOW()),
       (1874368123604328453, '删除', 1874368123604328448, 3, null, null, null, null, null, false, false, false,
        'biz:adAccountOrder:delete', 5, 1, 1, NOW()),
       (1874368123604328454, '导出', 1874368123604328448, 3, null, null, null, null, null, false, false, false,
        'biz:adAccountOrder:export', 6, 1, 1, NOW()),
       (1874368457160548352, '充值订单', 664569109744205842, 2, '/order/recharge', 'RechargeOrder',
        'biz/rechargeOrder/index', null, null, false, false, false, null, 2, 1, 1, NOW()),
       (1874368457160548353, '列表', 1874368457160548352, 3, null, null, null, null, null, false, false, false,
        'biz:rechargeOrder:list', 1, 1, 1, NOW()),
       (1874368457160548354, '处理', 1874368457160548352, 3, null, null, null, null, null, false, false, false,
        'biz:rechargeOrder:handle', 2, 1, 1, NOW()),
       (1874368457160548358, '导出', 1874368457160548352, 3, null, null, null, null, null, false, false, false,
        'biz:rechargeOrder:export', 6, 1, 1, NOW()),
       (1874368839991451648, '清零订单', 664569109744205842, 2, '/order/clear', 'ClearOrder', 'biz/clearOrder/index',
        null, null, false, false, false, null, 3, 1, 1, NOW()),
       (1874368839991451649, '列表', 1874368839991451648, 3, null, null, null, null, null, false, false, false,
        'biz:clearOrder:list', 1, 1, 1, NOW()),
       (1874368839991451650, '处理', 1874368839991451648, 3, null, null, null, null, null, false, false, false,
        'biz:clearOrder:handle', 2, 1, 1, NOW()),
       (1874368839991451654, '导出', 1874368839991451648, 3, null, null, null, null, null, false, false, false,
        'biz:clearOrder:export', 6, 1, 1, NOW());

-- changeset hans:3
-- comment 新增字典数据
-- 新增字典数据
insert into sys_dict (id, name, code, description, is_system, create_user, create_time)
values (4, '卡片平台', 'card_platform', null, false, 1, NOW()),
       (5, '卡片状态', 'card_status', '', false, 1, NOW()),
       (6, '卡片余额流水类型', 'card_balance_type', null, false, 1, NOW()),
       (664056643461320736, '广告户状态', 'ad_account_status', null, false, 1, NOW()),
       (664057579076325431, '广告户养号状态', 'ad_account_keep_status', '', false, 1, NOW()),
       (664074465612664926, '广告户出售状态', 'ad_account_sale_status', null, false, 1, NOW()),
       (664074713491837036, '广告户清零状态', 'ad_account_clear_status', null, false, 1, NOW()),
       (664075127603859581, '交易动作', 'transaction_action', null, false, 1, NOW()),
       (664075493615603851, '广告户余额变更类型', 'ad_account_balance_type', null, false, 1, NOW()),
       (664103124704694426, '下户订单状态', 'ad_account_order_status', null, false, 1, NOW()),
       (664103446747549861, '清零订单状态', 'clear_order_status', null, false, 1, NOW()),
       (664104022067646645, '客户余额变更类型', 'customer_balance_type', null, false, 1, NOW()),
       (664104946030874836, '充值手续费扣款方式', 'recharge_fee_handle_method', null, false, 1, NOW()),
       (664106980238954723, '充值订单状态', 'recharge_order_status', null, false, 1, NOW()),
       (664624349432555178, '卡片交易记录状态', 'card_transaction_status', null, false, 1, NOW()),
       (664625166554277579, '卡片交易记录类型', 'card_transaction_type', null, false, 1, NOW());

insert into sys_dict_item (id, label, value, color, sort, description, status, dict_id, create_user, create_time)
values (8, '汇通', '1', 'gray', 999, null, 1, 4, 1, NOW()),
       (9, 'cardvp', '2', 'gray', 999, null, 1, 4, 1, NOW()),
       (10, '正常', '1', 'green', 999, null, 1, 5, 1, NOW()),
       (11, '锁定', '2', 'red', 999, null, 1, 5, 1, NOW()),
       (12, '冻结', '3', 'orangered', 999, null, 1, 5, 1, NOW()),
       (13, '充值', '1', 'gray', 999, null, 1, 6, 1, NOW()),
       (14, '提现', '2', 'gray', 999, null, 1, 6, 1, NOW()),
       (664056859707052069, '正常', '1', 'green', 1, null, 1, 664056643461320736, 1, NOW()),
       (664056901956276264, '停用', '2', 'red', 2, null, 1, 664056643461320736, 1, NOW()),
       (664057191065456683, '申诉中', '3', 'blue', 3, null, 1, 664056643461320736, 1, NOW()),
       (664057314092781614, '申诉成功', '4', 'cyan', 4, null, 1, 664056643461320736, 1, NOW()),
       (664057422393905204, '申诉失败', '5', 'orangered', 5, null, 1, 664056643461320736, 1, NOW()),
       (664057803136045116, '待操作', '1', 'gray', 1, null, 1, 664057579076325431, 1, NOW()),
       (664057852016463935, '进行中', '2', 'blue', 2, null, 1, 664057579076325431, 1, NOW()),
       (664058065665921090, '完成', '3', 'cyan', 3, null, 1, 664057579076325431, 1, NOW()),
       (664058215977193541, '成功', '4', 'green', 4, null, 1, 664057579076325431, 1, NOW()),
       (664058343072993361, '失败', '5', 'red', 5, null, 1, 664057579076325431, 1, NOW()),
       (664074528791466083, '待出售', '1', 'gray', 1, null, 1, 664074465612664926, 1, NOW()),
       (664074578108092518, '已出售', '2', 'red', 2, null, 1, 664074465612664926, 1, NOW()),
       (664074636081762409, '已回收', '3', 'orangered', 3, null, 1, 664074465612664926, 1, NOW()),
       (664074827459465329, '未清零', '1', 'gray', 1, null, 1, 664074713491837036, 1, NOW()),
       (664074870623047796, '清零中', '2', 'blue', 2, null, 1, 664074713491837036, 1, NOW()),
       (664074921088913527, '已清零', '3', 'red', 3, null, 1, 664074713491837036, 1, NOW()),
       (664075173988667522, '收入', '1', 'green', 1, null, 1, 664075127603859581, 1, NOW()),
       (664075203369767045, '支出', '2', 'red', 2, null, 1, 664075127603859581, 1, NOW()),
       (664102723611791508, '充值', '1', 'gray', 1, null, 1, 664075493615603851, 1, NOW()),
       (664102806654816407, '清零', '2', 'gray', 2, null, 1, 664075493615603851, 1, NOW()),
       (664103205612818591, '进行中', '1', 'blue', 1, null, 1, 664103124704694426, 1, NOW()),
       (664103242837266594, '已终止', '2', 'red', 2, null, 1, 664103124704694426, 1, NOW()),
       (664103540653822123, '待处理', '1', 'gray', 1, null, 1, 664103446747549861, 1, NOW()),
       (664103595066527918, '处理中', '2', 'blue', 2, null, 1, 664103446747549861, 1, NOW()),
       (664103787509584049, '已完成', '3', 'green', 3, null, 1, 664103446747549861, 1, NOW()),
       (664104121594286266, '后台操作', '1', 'gray', 1, null, 1, 664104022067646645, 1, NOW()),
       (664104379137134789, '充值手续费', '2', 'gray', 2, null, 1, 664104022067646645, 1, NOW()),
       (664104444153041096, '广告户充值', '3', 'gray', 3, null, 1, 664104022067646645, 1, NOW()),
       (664104624667496657, '广告户清零', '4', 'gray', 4, null, 1, 664104022067646645, 1, NOW()),
       (664106060927209689, '打款时从本金中扣除', '1', 'gray', 1, null, 1, 664104946030874836, 1, NOW()),
       (664106155894640860, '打款时包含手续费', '2', 'gray', 2, null, 1, 664104946030874836, 1, NOW()),
       (664106521906385119, '充值广告户时扣除', '3', 'gray', 3, null, 1, 664104946030874836, 1, NOW()),
       (664107025872982248, '待处理', '1', 'gray', 1, null, 1, 664106980238954723, 1, NOW()),
       (664107065567875307, '处理中', '2', 'blue', 2, null, 1, 664106980238954723, 1, NOW()),
       (664107094638596334, '已完成', '3', 'green', 3, null, 1, 664106980238954723, 1, NOW()),
       (664624399420270254, '处理中', '1', 'gray', 1, null, 1, 664624349432555178, 1, NOW()),
       (664624432148424369, '成功', '2', 'green', 2, null, 1, 664624349432555178, 1, NOW()),
       (664624518563669684, '撤销', '3', 'orangered', 3, null, 1, 664624349432555178, 1, NOW()),
       (664624566273877687, '失败', '4', 'red', 4, null, 1, 664624349432555178, 1, NOW()),
       (664624608682485434, '清算', '5', 'arcoblue', 5, null, 1, 664624349432555178, 1, NOW()),
       (664624702009943741, '过期', '6', 'orange', 6, null, 1, 664624349432555178, 1, NOW()),
       (664624824919828163, '未知', '99', 'gray', 999, null, 1, 664624349432555178, 1, NOW()),
       (664625315837945551, '授权交易', '1', 'gray', 1, null, 1, 664625166554277579, 1, NOW()),
       (664625358212998866, '授权回退', '2', 'gray', 2, null, 1, 664625166554277579, 1, NOW()),
       (664625398352488149, '退款', '3', 'gray', 3, null, 1, 664625166554277579, 1, NOW()),
       (664625431256803032, '其他', '99', 'gray', 999, null, 1, 664625166554277579, 1, NOW());


-- changeset yqx:1
-- comment 添加广告户表字段
ALTER TABLE `biz_ad_account`
    ADD COLUMN `spend_cap` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '花费限额' AFTER `clear_status`,
    ADD COLUMN `amount_spent` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '消耗' AFTER `spend_cap`,
    ADD COLUMN `balance`      decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '剩余应付' AFTER `amount_spent`;

-- changeset yqx:2
-- comment 添加时区字典
INSERT INTO `sys_dict` (`id`, `name`, `code`, `description`, `is_system`, `create_user`, `create_time`, `update_user`,
                        `update_time`)
VALUES (665137431120446619, '时区类型', 'timezone_type', NULL, b'0', 1, '2025-01-02 10:18:41', NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665137691154711713, 'GMT-08:00', 'GMT-08:00', '', 2, NULL, 1, 665137431120446619, 1, '2025-01-02 10:19:43', 1,
        '2025-01-02 10:22:06');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665137800554742948, 'GMT+03:00', 'GMT+03:00', '', 1, NULL, 1, 665137431120446619, 1, '2025-01-02 10:20:09', 1,
        '2025-01-02 10:22:01');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665137904435070125, 'GMT+05:30', 'GMT+05:30', '', 3, NULL, 1, 665137431120446619, 1, '2025-01-02 10:20:34', 1,
        '2025-01-02 10:21:54');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665137938127914160, 'GMT+07:00', 'GMT+07:00', '', 4, NULL, 1, 665137431120446619, 1, '2025-01-02 10:20:42', 1,
        '2025-01-02 10:22:10');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665137973360067763, 'GMT+08:00', 'GMT+08:00', '', 5, NULL, 1, 665137431120446619, 1, '2025-01-02 10:20:51', 1,
        '2025-01-02 10:22:13');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665138009150063798, 'GMT+05:00', 'GMT+05:00', '', 6, NULL, 1, 665137431120446619, 1, '2025-01-02 10:20:59', 1,
        '2025-01-02 10:22:16');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665138039982392505, 'GMT+04:00', 'GMT+04:00', '', 7, NULL, 1, 665137431120446619, 1, '2025-01-02 10:21:06', 1,
        '2025-01-02 10:22:19');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665138084366517436, 'GMT-07:00', 'GMT-07:00', '', 8, NULL, 1, 665137431120446619, 1, '2025-01-02 10:21:17', 1,
        '2025-01-02 10:22:23');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665138119741277375, 'GMT-03:00', 'GMT-03:00', '', 9, NULL, 1, 665137431120446619, 1, '2025-01-02 10:21:25', 1,
        '2025-01-02 10:22:26');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665138151823508674, 'GMT-05:00', 'GMT-05:00', '', 10, NULL, 1, 665137431120446619, 1, '2025-01-02 10:21:33', 1,
        '2025-01-02 10:22:31');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665138182714557637, 'GMT+00:00', 'GMT+00:00', '', 11, NULL, 1, 665137431120446619, 1, '2025-01-02 10:21:40', 1,
        '2025-01-02 10:22:35');


-- changeset hans:4
-- comment 新增广告户卡关联表
CREATE TABLE IF NOT EXISTS biz_ad_account_card
(
    id
    bigint
    not
    null
    comment
    'ID',
    ad_account_id
    bigint
    not
    null
    comment
    '关联广告户',
    full_card_number
    varchar
(
    64
) default '' not null comment '完整卡号',
    fuzzy_card_number varchar
(
    64
) default '' not null comment '模糊卡号',
    is_default bit default b'0' not null comment '是否默认',
    is_remove bit default b'0' not null comment '是否已移除',
    remove_time datetime null comment '移除时间',
    create_time datetime not null comment '创建时间',
    constraint biz_ad_account_card_pk
    primary key
(
    id
)
    )
    comment '广告户关联卡';

-- changeset yqx:3
-- comment 修改上报日志表字段
ALTER TABLE `biz_ad_account_browser_log`
    MODIFY COLUMN `env` longtext NOT NULL COMMENT '操作环境' AFTER `active_code`;


-- changeset hans:5
-- comment 新增广告户系列消耗表
CREATE TABLE IF NOT EXISTS biz_campaign_insight
(
    id
    bigint
    not
    null
    comment
    'ID',
    platform_campaign_id
    varchar
(
    64
) not null comment '平台活动ID',
    platform_ad_id bigint not null comment '平台广告ID',
    stat_date date not null comment '统计时间',
    spend decimal
(
    10,
    2
) not null comment '消耗',
    create_time datetime null comment '创建时间',
    constraint biz_campaign_insight_pk
    primary key
(
    id
)
    );

-- changeset hans:6
-- comment 修改充值清零订单表字段
CREATE TABLE IF NOT EXISTS biz_fb_account
(
    id
    bigint
    not
    null
    comment
    'ID'
    primary
    key,
    platform_account_id
    varchar
(
    64
) not null comment '关联账号ID',
    browser_serial_no varchar
(
    64
) not null comment '浏览器编号',
    status int not null comment '状态（1=正常，2=封禁）',
    create_user bigint not null comment '创建人',
    create_time datetime not null comment '创建时间',
    update_time datetime null comment '更新时间',
    constraint biz_fb_account_platform_account_id_uindex
    unique
(
    platform_account_id
)
    )
    comment 'fb账号';



-- changeset yqx:4
-- comment 创建唯一约束
ALTER TABLE `biz_campaign_insight`
    ADD UNIQUE KEY `unique_campaign_ad_stat` (`platform_campaign_id`, `stat_date`);


-- changeset hans:7
-- comment 修改充值清零订单表字段
alter table `biz_recharge_order`
    change `ad_account_id` `platform_ad_id` varchar (64) not null comment '平台广告ID';

alter table `biz_clear_order`
    change `ad_account_id` `platform_ad_id` varchar (64) not null comment '平台广告户ID';

alter table `biz_ad_account_balance_record`
    change `ad_account_id` `platform_ad_id` varchar (64) not null comment '广告户ID';



-- changeset yqx:5
-- comment fb账号管理菜单
SET
@parentId = 1875097825335775232;
INSERT INTO `sys_menu`
(`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`,
 `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES (@parentId, 'fb账号管理', 664567576520572934, 2, '/biz/fbAccount', 'FbAccount', 'biz/fbAccount/index', NULL,
        NULL, b'0', b'0',
        b'0', NULL, 3, 1, 1, NOW());
INSERT INTO `sys_menu`
(`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES (1875097825344163840, '列表', @parentId, 3, 'biz:fbAccount:list', 1, 1, 1, NOW()),
       (1875097825344163841, '详情', @parentId, 3, 'biz:fbAccount:detail', 2, 1, 1, NOW()),
       (1875097825344163842, '新增', @parentId, 3, 'biz:fbAccount:add', 3, 1, 1, NOW()),
       (1875097825344163843, '修改', @parentId, 3, 'biz:fbAccount:update', 4, 1, 1, NOW()),
       (1875097825344163844, '删除', @parentId, 3, 'biz:fbAccount:delete', 5, 1, 1, NOW()),
       (1875097825344163845, '导出', @parentId, 3, 'biz:fbAccount:export', 6, 1, 1, NOW());
INSERT INTO `sys_dict` (`id`, `name`, `code`, `description`, `is_system`, `create_user`, `create_time`, `update_user`,
                        `update_time`)
VALUES (665602361695141892, 'FB账号状态', 'fb_account_status', NULL, b'0', 1, '2025-01-03 17:06:09', NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665602497733197832, '正常', '1', 'green', 1, NULL, 1, 665602361695141892, 1, '2025-01-03 17:06:42', 1,
        '2025-01-03 17:07:00');
INSERT INTO `sys_dict_item` (`id`, `label`, `value`, `color`, `sort`, `description`, `status`, `dict_id`, `create_user`,
                             `create_time`, `update_user`, `update_time`)
VALUES (665602624959021078, '封禁', '2', 'red', 2, NULL, 1, 665602361695141892, 1, '2025-01-03 17:07:12', NULL, NULL);

-- changeset yqx:6
-- comment 修改FB账号表字段
ALTER TABLE `biz_fb_account`
    ADD COLUMN `name` varchar(255) NULL AFTER `status`,
    ADD COLUMN `tab`          varchar(255) NULL AFTER `name`,
    ADD COLUMN `platform`     varchar(255) NULL AFTER `tab`,
    ADD COLUMN `username`     varchar(255) NULL AFTER `platform`,
    ADD COLUMN `password`     varchar(255) NULL AFTER `username`,
    ADD COLUMN `fakey`        varchar(255) NULL AFTER `password`,
    ADD COLUMN `cookie`       longtext     NULL AFTER `fakey`,
    ADD COLUMN `proxy_type`   varchar(255) NULL AFTER `cookie`,
    ADD COLUMN `ip_checker`   varchar(255) NULL AFTER `proxy_type`,
    ADD COLUMN `proxy`        varchar(255) NULL AFTER `ip_checker`,
    ADD COLUMN `proxy_url`    varchar(255) NULL AFTER `proxy`,
    ADD COLUMN `proxy_id`     int          NULL AFTER `proxy_url`,
    ADD COLUMN `ip`           varchar(255) NULL AFTER `proxy_id`,
    ADD COLUMN `country_code` varchar(255) NULL AFTER `ip`,
    ADD COLUMN `region_code`  varchar(255) NULL AFTER `country_code`,
    ADD COLUMN `city_code`    varchar(255) NULL AFTER `region_code`,
    ADD COLUMN `ua`           varchar(255) NULL AFTER `city_code`,
    ADD COLUMN `resolution`   varchar(255) NULL AFTER `ua`;
ALTER TABLE `biz_fb_account`
    MODIFY COLUMN `browser_serial_no` varchar (64) NULL COMMENT '浏览器编号' AFTER `platform_account_id`;


-- changeset hans:8
-- comment 新增广告户关联卡表平台字段
alter table `biz_ad_account_card`
    add platform int null comment '平台' after ad_account_id;

-- changeset hans:9
-- comment 新增字段
alter table biz_clear_order
    add card_clear_result text null comment '卡台清零结果';

-- changeset hans:10
-- comment 新增字段
alter table biz_ad_account_card
    change ad_account_id platform_ad_id varchar (64) not null comment '关联广告户';

-- changeset hans:11
-- comment 新增字段
alter table biz_recharge_order
    add card_status int default 1 not null comment '卡片充值状态' after status;

-- changeset yqx:7
-- comment 修改字段
ALTER TABLE `biz_campaign_insight`
    MODIFY COLUMN `platform_ad_id` varbinary(64) NOT NULL COMMENT '平台广告ID' AFTER `platform_campaign_id`;

-- changeset yqx:8
-- comment 新增fb账号渠道
CREATE TABLE `biz_fb_channel`
(
    `id`          BIGINT      NOT NULL COMMENT 'ID',
    `name`        VARCHAR(64) NOT NULL COMMENT '渠道名称',
    `create_time` datetime    NOT NULL COMMENT '创建时间',
    `create_user` BIGINT      NOT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `update_user` BIGINT   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = INNODB COMMENT = 'FB账号渠道';
SET
@parentId = 1876110828936077312;
INSERT INTO `sys_menu`
(`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`,
 `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES (@parentId, 'FB账号渠道', 664567576520572934, 2, '/biz/fbChannel', 'FbChannel', 'biz/fbChannel/index', NULL,
        NULL, b'0',
        b'0', b'0', NULL, 1, 1, 1, NOW());

INSERT INTO `sys_menu`
(`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES (1876110828936077313, '列表', @parentId, 3, 'biz:fbChannel:list', 1, 1, 1, NOW()),
       (1876110828936077314, '详情', @parentId, 3, 'biz:fbChannel:detail', 2, 1, 1, NOW()),
       (1876110828936077315, '新增', @parentId, 3, 'biz:fbChannel:add', 3, 1, 1, NOW()),
       (1876110828936077316, '修改', @parentId, 3, 'biz:fbChannel:update', 4, 1, 1, NOW()),
       (1876110828936077317, '删除', @parentId, 3, 'biz:fbChannel:delete', 5, 1, 1, NOW()),
       (1876110828936077318, '导出', @parentId, 3, 'biz:fbChannel:export', 6, 1, 1, NOW());
ALTER TABLE `biz_fb_account`
    ADD COLUMN `channel_id` bigint NOT NULL COMMENT '渠道id' AFTER `status`,
    ADD COLUMN `tag`        varchar(255) NULL COMMENT '标签' AFTER `channel_id`;


-- changeset hans:12
-- comment 新增字段
alter table biz_recharge_order
    add fb_check_status int default 1 not null comment 'fb检测状态' after status;

-- changeset yqx:9
-- comment 修改字段
ALTER TABLE `biz_fb_account`
    MODIFY COLUMN `channel_id` bigint NULL COMMENT '渠道id' AFTER `status`;
ALTER TABLE `biz_ad_account`
    MODIFY COLUMN `timezone` varchar (64) NULL COMMENT '时区' AFTER `fuzzy_card_number`;

-- changeset hans:13
-- comment 新增字段
alter table biz_ad_account
    add parent_browser_no varchar(64) default '' not null comment '观察户浏览器' after remark;

-- changeset yqx:10
-- comment 修改字段
ALTER TABLE `biz_ad_account_order`
    MODIFY COLUMN `customer_bm_id` varchar (64) NOT NULL DEFAULT '' COMMENT '客户BM ID' AFTER `ad_account_id`;

-- changeset yqx:11
-- comment 修改字段
ALTER TABLE `biz_fb_account`
    ADD COLUMN `remark` varchar(255) NULL COMMENT '备注' AFTER `update_time`;
ALTER TABLE `biz_ad_account_order`
    MODIFY COLUMN `update_user` bigint NULL COMMENT '更新人' AFTER `update_time`;

-- changeset yqx:12
-- comment 修改字段
ALTER TABLE `biz_ad_account_order`
    MODIFY COLUMN `ad_account_id` varchar (64) NOT NULL COMMENT '关联广告户' AFTER `customer_id`,
    ADD COLUMN `ad_account_name` varchar (64) NOT NULL DEFAULT '' COMMENT '广告户名字' AFTER `ad_account_id`;

-- changeset yqx:13
-- comment 修改下户订单字段、新增字典数据
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `handle_user` BIGINT NULL COMMENT '处理人' AFTER `status`,
    ADD COLUMN `handle_time`    datetime NULL COMMENT '处理时间' AFTER `handle_user`,
    ADD COLUMN `authorize_time` datetime NULL COMMENT '授权时间' AFTER `handle_time`;


-- changeset hans:14
-- comment 修改字段
alter table biz_customer
    modify telegram_chat_id bigint null comment 'TG群ID';

-- changeset hans:15
-- comment 新增字段
alter table biz_customer_balance_record
    add certificate varchar(255) default '' not null comment '水单凭证';

-- changeset hans:16
-- comment 新增字段
alter table biz_ad_account
    add bm5_id varchar(64) default '' not null comment '关联bm5 id' after remark;

-- changeset hans:17
-- comment 修改字段
alter table biz_ad_account
    change bm5_id bm_id varchar (64) default '' not null comment '关联bm5 id';

-- changeset hans:18
-- comment 新增字段
alter table biz_recharge_order
    add fb_ops_id varchar(64) default '' not null comment 'fb操作id';

alter table biz_clear_order
    add fb_ops_id varchar(64) default '' not null comment 'fb操作id';

-- changeset yqx:14
-- comment 新增字段
ALTER TABLE `biz_ad_account_card`
    ADD COLUMN `remark` varchar(64) NULL COMMENT '备注' AFTER `is_remove`;

-- changeset yqx:15
-- comment 新增字段
ALTER TABLE `biz_customer_balance_record`
    ADD COLUMN `after_amount` decimal(10, 2) NULL COMMENT '交易后余额' AFTER `trans_time`;

-- changeset hans:19
-- comment 新增退款订单表
create table biz_refund_order
(
    id               bigint                  not null comment 'ID'
        primary key,
    order_no         varchar(64)             not null comment '订单编号',
    customer_id      bigint                  not null comment '客户',
    platform_ad_id   varchar(64)             not null comment '广告户ID',
    card_status      int          default 1  not null comment '卡台状态',
    fb_check_status  int          default 1  not null comment 'fb检测状态',
    amount           decimal(10, 2)          not null comment '退款金额',
    status           int          default 1  not null comment '状态',
    apply_message_id int                     not null comment '飞机消息',
    handle_user      bigint null comment '处理人',
    handle_time      datetime null comment '处理时间',
    finish_time      datetime null comment '完成时间',
    certificate      varchar(255) default '' not null comment '凭证',
    remark           varchar(255) default '' not null comment '备注',
    fb_ops_id        varchar(64)  default '' not null comment 'fb操作id',
    create_time      datetime                not null comment '创建时间',
    update_user      bigint null comment '更新人',
    update_time      datetime null comment '更新时间'
) comment '退款订单';

-- changeset hans:20
-- comment 新增字段
alter table biz_customer_balance_record
    add platform_ad_id varchar(64) default '' not null comment '广告户ID' after customer_id;

-- changeset hans:21
-- comment 新增字段
alter table biz_clear_order
    add fb_check_status int default 1 not null comment 'fb检测状态' after status;


-- changeset yqx:16
-- comment 新增字段
ALTER TABLE `biz_ad_account`
    ADD COLUMN `tag` varchar(64) NULL COMMENT '标签' AFTER `remark`;


-- changeset hans:22
-- comment 新增表
CREATE TABLE IF NOT EXISTS `biz_appeal_order`
(
    id
    bigint
    not
    null
    comment
    'ID'
    primary
    key,
    order_no
    varchar
(
    64
) not null comment '订单编号',
    customer_id bigint not null comment '客户',
    platform_ad_id varchar
(
    64
) not null comment '广告户ID',
    status int default 1 not null comment '申诉状态',
    card_status int default 1 not null comment '卡台状态',
    apply_message_id int null comment '关联消息id',
    handle_user bigint null comment '处理人',
    handle_time datetime null comment '处理时间',
    finish_time datetime null comment '完成时间',
    remark varchar
(
    255
) default '' not null comment '备注',
    create_time datetime null comment '创建时间'
    )
    comment '申诉订单';

-- changeset yqx:17
-- comment 新增表
CREATE TABLE `biz_ad_account_insight`
(
    `id`            bigint         NOT NULL COMMENT 'ID',
    `ad_account_id` varchar(64)    NOT NULL COMMENT '广告户',
    `stat_date`     date           NOT NULL COMMENT '统计时间',
    `spend`         decimal(10, 2) NOT NULL COMMENT '消耗',
    `create_time`   datetime       NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_ad_account_stat` (`ad_account_id`, `stat_date`)
) ENGINE = InnoDB COMMENT ='广告户每日消耗'


-- changeset hans:23
-- comment 新增字段
alter table biz_card
    add expire_date varchar(20) default '' not null comment '过期时间' after card_number;

alter table biz_card
    add association varchar(64) default '' not null comment '卡组织' after card_number;


-- changeset hans:24
-- comment 新增字段
alter table biz_ad_account_card
    add expire_date varchar(20) default '' not null comment '过期时间' after remark;

alter table biz_ad_account_card
    add association varchar(20) default '' not null comment '卡组织' after expire_date;

-- changeset hans:25
-- comment 新增字段
alter table biz_clear_order
    add card_status int default 1 not null comment '卡台状态' after status;

-- changeset hans:26
-- comment 新增字段
alter table biz_ad_account
    add total_spent decimal(10, 2) null comment '总花费' after parent_browser_no;

alter table biz_ad_account
    add name varchar(255) default '' not null comment '广告户名称' after platform_ad_id;


-- changeset hans:27
-- comment 新增字段
alter table biz_card_transaction
    add remark varchar(64) default '' not null comment '备注' after trans_detail;

-- changeset yqx:18
-- comment 新增表
CREATE TABLE `biz_ip`
(
    `id`            bigint      NOT NULL COMMENT 'ID',
    `host`          varchar(64) NOT NULL COMMENT 'ip地址',
    `port`          varchar(64) NOT NULL COMMENT '端口',
    `username`      varchar(64) NOT NULL COMMENT '用户名',
    `password`      varchar(64) NOT NULL COMMENT '密码',
    `country`       varchar(64) NOT NULL COMMENT '国家',
    `use_times`     int         NOT NULL DEFAULT '0' COMMENT '使用次数',
    `success_times` int         NOT NULL DEFAULT '0' COMMENT '成功次数',
    `create_time`   datetime    NOT NULL COMMENT '创建时间',
    `create_user`   bigint      NOT NULL COMMENT '创建人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
    COMMENT ='IP库';
ALTER TABLE `biz_fb_account`
    MODIFY COLUMN `status` int NULL COMMENT '状态（1=正常，2=封禁）' AFTER `browser_serial_no`;
ALTER TABLE `biz_fb_account`
    ADD COLUMN `ip_id` bigint NULL COMMENT '对应代理id' AFTER `remark`;

-- changeset hans:28
-- comment 新增字段
alter table biz_appeal_order
    add card_balance decimal(10, 2) null comment '卡片余额' after remark;

alter table biz_appeal_order
    add card_clear_result text null comment '卡台提现结果' after card_balance;

-- changeset hans:29
-- comment 新增表
CREATE TABLE IF NOT EXISTS biz_material
(
    id
    bigint
    not
    null
    comment
    'ID',
    pay_date
    datetime
    not
    null
    comment
    '支付时间',
    type
    int
    not
    null
    comment
    '类型',
    num
    int
    not
    null
    comment
    '数量',
    channel
    varchar
(
    64
) default '' not null comment '渠道',
    pay_price decimal
(
    10,
    2
) not null comment '支付金额',
    remark varchar
(
    255
) default '' not null comment '备注',
    create_time datetime not null comment '创建时间',
    create_user bigint not null comment '创建人',
    constraint biz_material_pk
    primary key
(
    id
)
    )
    comment '物料';

-- changeset yyy:01
-- comment 新增客户余额提现表
CREATE TABLE biz_customer_withdraw_order
(
    id                   BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键ID',
    order_no             VARCHAR(64) NOT NULL COMMENT '订单编号',
    status               TINYINT     NOT NULL COMMENT '订单状态（1:审核中, 2:审核通过, 3:审核拒绝, 4:退款完成, 5:已取消）',
    customer_id          BIGINT UNSIGNED NOT NULL COMMENT '关联客户ID',
    expected_refund_time DATETIME    NOT NULL COMMENT '预计退款时间',
    remark               VARCHAR(500) COMMENT '备注',
    audit_remark         VARCHAR(500) COMMENT '审核备注',
    auditor              BIGINT UNSIGNED COMMENT '审核人',
    audit_time           DATETIME COMMENT '审核时间',
    actual_refund_time   DATETIME COMMENT '实际退款时间',
    actual_refund_amount DECIMAL(10, 2)       DEFAULT 0.00 COMMENT '实际退款金额',
    create_time          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user          BIGINT UNSIGNED NOT NULL COMMENT '创建人',
    update_user          BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    update_time          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_order_no (order_no) COMMENT '订单编号唯一约束',
    INDEX                idx_customer_id (customer_id) COMMENT '客户ID索引',
    INDEX                idx_status (status) COMMENT '状态索引'
) COMMENT='客户余额提现订单表';

-- changeset yyy:02
-- comment 新增相关客户统计索引
CREATE INDEX idx_biz_ad_account_order_customer_status ON biz_ad_account_order (customer_id, status);
CREATE INDEX idx_biz_ad_account_order_ad_account_id ON biz_ad_account_order (ad_account_id);
CREATE INDEX idx_biz_ad_account_platform_status ON biz_ad_account (platform_ad_id, account_status);
CREATE INDEX idx_biz_customer_balance_record_customer_type ON biz_customer_balance_record (`type`, customer_id);
CREATE INDEX idx_biz_ad_account_balance_platform_type_trans_time
    ON biz_ad_account_balance_record (platform_ad_id, type, trans_time);
CREATE INDEX idx_biz_ad_account_balance_platform_type
    ON biz_ad_account_balance_record (platform_ad_id, type);
CREATE INDEX idx_biz_ad_account_insight_ad_account_id_stat_date
    ON biz_ad_account_insight (ad_account_id, stat_date);
CREATE INDEX idx_biz_ad_account_insight_ad_account_id
    ON biz_ad_account_insight (ad_account_id);
CREATE INDEX idx_biz_ad_account_card_platform_ad_id ON biz_ad_account_card (platform_ad_id);
CREATE INDEX idx_biz_card_full_card_number ON biz_card (card_number);

-- changeset hans:30
-- comment 新增表
CREATE TABLE IF NOT EXISTS biz_card_withdraw_record
(
    id
    bigint
    not
    null
    comment
    'ID',
    platform_ad_id
    varchar
(
    64
) not null comment '广告户',
    card_platform int not null comment '卡片平台',
    card_number varchar
(
    64
) not null comment '卡号',
    amount decimal
(
    10,
    2
) not null comment '金额',
    is_notify bit default b'0' not null comment '是否已提醒',
    status int default 1 not null comment '状态',
    create_time datetime not null comment '创建时间',
    constraint biz_card_withdraw_record_pk
    primary key
(
    id
)
    )
    comment '卡片提现记录';

-- changeset hans:31
-- comment 新增字段
alter table biz_card
    add remark varchar(255) default '' not null comment '备注';

-- changeset yyy:03
-- comment 清零订单表增加索引
CREATE INDEX idx_customer_ad_status ON biz_recharge_order (customer_id, platform_ad_id, status);
CREATE INDEX idx_customer_ad_status ON biz_clear_order (customer_id, platform_ad_id, status);

-- changeset hans:32
-- comment 新增字段
alter table biz_card_transaction
    add clear_amount decimal(10, 2) null comment '清零金额' after trans_time;


-- changeset yqx:19
-- comment 新增字段
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `recycle_time` datetime NULL COMMENT '回收时间' AFTER `authorize_time`;


-- changeset yyy:04
-- comment 下户订单增加总消耗
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `total_spent` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '总消耗';


-- changeset hans:33
-- comment 新增字段
alter table biz_ad_account
    add appeal_status int default 1 not null comment '申诉状态';


-- changeset hans:34
-- comment 新增字段
alter table biz_ad_account_insight
    add customer_id bigint null comment '关联客户' after id;

alter table biz_card_transaction
    add china_time datetime null comment '中国交易时间';

alter table biz_card_transaction
    add ad_account_time datetime null comment '广告户时间';

-- changeset yyy:05
-- comment 增加卡片交易表的索引
create index idx_card_transaction_status_num_time on biz_card_transaction (card_number, trans_status, ad_account_time)

-- changeset yyy:06
-- comment 增加广告户卡片表的索引
create index idx_ad_account_card_num on biz_ad_account_card (full_card_number);

-- changeset yqx:20
-- comment 新增字段
ALTER TABLE `biz_fb_account`
    ADD COLUMN `custom_remark` varchar(255) NULL COMMENT '自定义备注' AFTER `ip_id`;


-- changeset hans:35
-- comment 新增字段
alter table biz_ad_account
    add bill_country varchar(64) default '' not null comment '账单国家';

alter table biz_ad_account
    add bill_currency varchar(64) default '' not null comment '账单货币';

-- changeset hans:36
-- comment 新增字段
alter table biz_customer
    add robot_permission varchar(255) default '' not null comment '机器人权限';

alter table biz_ad_account
    add bm_auth_time datetime null comment 'bm5授权时间';


-- changeset hans:37
-- comment 新增字段
alter table biz_ad_account_browser_log
    add platform_ad_id varchar(64) default '' not null comment '广告户ID';

-- changeset hans:38
-- comment 新增字段
alter table biz_business_manager
    add status int default 1 not null comment '状态' after browser_no;

alter table biz_business_manager
    add is_use bit default b'0' not null comment '是否已使用' after status;

alter table biz_business_manager
    add remark varchar(255) default '' not null comment '备注' after is_use;

alter table biz_business_manager
    add content varchar(1024) default '' not null comment '账号信息' after remark;


-- changeset hans:39
-- comment 新增字段
alter table biz_ad_account
    add ban_time datetime null comment '封禁时间';


-- changeset hans:40
-- comment 新增字段
alter table biz_card
    add used_amount decimal(10, 2) null comment '已使用金额' after platform_card_id;

-- changeset hans:41
-- comment 新增字段
alter table biz_business_manager
    add num int null comment '坑位';

alter table biz_business_manager
    add user varchar(64) default '' not null comment '使用者';

alter table biz_business_manager
    add use_time datetime null comment '使用时间';



-- changeset yyy:11
-- comment 创建客户需求表
create table biz_customer_requirement
(
    id               bigint      not null comment 'ID'
        primary key,
    customer_id      bigint      not null comment '客户ID',
    timezone         varchar(64) not null comment '时区',
    requirement_time datetime    not null comment '需求时间',
    quantity         int         not null default 0 comment '需求数量',
    remark           varchar(255)         default '' not null comment '备注',
    create_time      datetime    not null comment '创建时间',
    create_user      bigint      not null comment '创建人',
    update_time      datetime null comment '更新时间',
    update_user      bigint null comment '更新人'
) comment '客户需求';

create index idx_customer_id on biz_customer_requirement (customer_id);
create index idx_timezone on biz_customer_requirement (timezone);

-- changeset yyy:12
-- comment 客户需求表增加处理人
alter table biz_customer_requirement
    add handle_user bigint not null default 0 comment '处理人';


-- changeset yyy:13
-- comment 客户需求表增加状态、接单时间、处理时间、完成数量、取消原因
alter table biz_customer_requirement
    add status int not null default 1 comment '状态:待接单，处理中，已完成，取消';
alter table biz_customer_requirement
    add accept_time datetime null comment '接单时间';
alter table biz_customer_requirement
    add handle_time datetime null comment '处理时间';
alter table biz_customer_requirement
    add finish_quantity int not null default 0 comment '完成数量';
alter table biz_customer_requirement
    add cancel_reason varchar(255) default '' not null comment '取消原因';


-- changeset hans:42
-- comment 新增字段
alter table biz_card
    add card_name varchar(64) default '' not null comment '卡片名称';


-- changeset hans:43
-- comment 新增字段
alter table biz_card
    add cvv varchar(20) default '' not null comment 'cvv';


-- changeset hans:44
-- comment 创建新表
CREATE TABLE IF NOT EXISTS biz_personal_account
(
    id
    bigint
    not
    null
    comment
    'ID',
    channel_name
    varchar
(
    64
) not null comment '渠道',
    content varchar
(
    1024
) not null comment '账号信息',
    access_user varchar
(
    64
) default '' not null comment '接入人',
    is_access bit default b'0' not null comment '是否接入',
    browser_no varchar
(
    20
) default '' not null comment '浏览器编号',
    account_status int default 1 not null comment '账号状态',
    unit_price decimal
(
    10,
    2
) default 0 not null comment '单价',
    remark varchar
(
    255
) default '' not null comment '备注',
    create_user bigint not null comment '创建人',
    create_time datetime not null comment '创建时间',
    constraint biz_personal_account_pk
    primary key
(
    id
)
    )
    comment '个号';

-- changeset hans:45
-- comment 新增字段
alter table biz_card
    add platform_ad_id varchar(20) default '' not null comment '广告户ID';

-- changeset hans:46
-- comment 新增字段
alter table biz_customer
    add buy_account_fee decimal(10, 2) null comment '开户费';

alter table biz_customer
    add is_refund bit default b'0' not null comment '是否退款';

-- changeset hans:47
-- comment 新增字段
alter table biz_business_manager
    add use_num int default 0 not null comment '已使用坑位' after num;

alter table biz_business_manager
    add unit_price decimal(10, 2) default 0 not null comment '单价';

alter table biz_business_manager
    add ban_time datetime null comment '封禁时间';


-- changeset hans:48
-- comment 新增字段
alter table biz_business_manager
    add ops_browser varchar(20) default '' not null comment '操作号';

alter table biz_business_manager
    add reserve_browser varchar(20) default '' not null comment '备用号';

alter table biz_business_manager
    add observe_browser varchar(20) default '' not null comment '观察号';


-- changeset hans:49
-- comment 创建新表
create table biz_ad_account_transaction
(
    id               bigint                 not null comment 'ID',
    platform_ad_id   varchar(64)            not null comment '广告户ID',
    trans_time       datetime               not null comment '交易时间',
    transaction_id   varchar(64) default '' not null comment '交易ID',
    transaction_type varchar(64) default '' not null comment '交易类型',
    amount           decimal(10, 2)         not null comment '交易金额',
    status           varchar(64)            not null comment '交易状态',
    create_time      datetime               not null comment '创建时间',
    constraint biz_ad_account_transaction_pk
        primary key (id)
) comment '广告户交易记录';


-- changeset hans:50
-- comment 新增字段
alter table biz_personal_account
    add type int not null comment '类型';

alter table biz_personal_account
    add is_after_sale bit default b'0' not null comment '是否售后号';

alter table biz_personal_account
    add after_sale_status int default 0 not null comment '售后状态';

alter table biz_personal_account
    add after_sale_reason varchar(64) default '' not null comment '售后原因';

alter table biz_personal_account
    add appeal_status int default 1 null comment '申诉状态';

alter table biz_personal_account
    add is_change_pwd bit default b'0' not null comment '是否改密码';

-- changeset hans:51
-- comment 删除字段
alter table biz_business_manager
drop
column use_num;

-- changeset hans:52
-- comment 创建新表
CREATE TABLE IF NOT EXISTS biz_business_manager_item
(
    id
    bigint
    not
    null
    comment
    'ID',
    business_manager_id
    bigint
    not
    null
    comment
    'bm 主键ID',
    name
    varchar
(
    64
) not null comment '名称（坑位1）',
    unit_price decimal
(
    10,
    2
) not null comment '单价',
    status int default 1 not null comment '状态（1=正常，2=封禁）',
    platform_ad_id varchar
(
    64
) default '' not null comment '广告户ID',
    is_use bit default b'0' not null comment '是否使用',
    use_time datetime null comment '使用时间',
    ban_time datetime null comment '封禁时间',
    create_time datetime null comment '创建时间',
    constraint biz_business_manager_item_pk
    primary key
(
    id
)
    )
    comment 'bm坑位';


-- changeset hans:53
-- comment 新增字段
alter table biz_ad_account
    add last_open_time datetime null comment '最后打开时间';



-- changeset hans:54
-- comment 删除字段
alter table biz_ad_account
drop
column last_open_time;

-- changeset yqx:21
-- comment 修改字段
ALTER TABLE `biz_personal_account`
    MODIFY COLUMN `type` int NULL COMMENT '类型' AFTER `create_time`,
    ADD COLUMN `purchase_time` datetime NULL COMMENT '购买时间' AFTER `is_change_pwd`;

-- changeset yyy:20
-- comment biz_card_balance增加平台卡片ID
alter table biz_card_balance
    add platform_card_id varchar(64) default '' not null comment '平台卡片ID';

-- changeset yqx:22
-- comment 新增字段
ALTER TABLE `biz_business_manager`
    ADD COLUMN `after_sale_status` int NOT NULL DEFAULT 0 COMMENT '售后状态' AFTER `observe_browser`,
    ADD COLUMN `after_sale_reason` varchar(64) NOT NULL DEFAULT '' COMMENT '售后原因' AFTER `after_sale_status`;

-- changeset yyy:21
-- comment biz_card_transaction增加平台卡片ID
alter table biz_card_transaction
    add platform_card_id varchar(64) default '' not null comment '平台卡片ID';


-- changeset yyy:22
-- comment 增加索引
create index idx_biz_card_transaction_platform_card on biz_card_transaction (card_number, platform);
create index idx_biz_card_balance_platform_card on biz_card_balance (card_number, platform);

-- changeset hans:55
-- comment 新增字段
alter table biz_ad_account
    add sale_time datetime null comment '出售时间';

-- changeset hans:56
-- comment 修改字段
alter table biz_ad_account_order
    change authorize_time finish_time datetime null comment '授权时间';

-- changeset hans:57
-- comment 新增字段
alter table biz_business_manager
    add type int default 1 not null comment '类型';

alter table biz_ad_account
    add cost decimal(10, 2) null comment '成本';

-- changeset yyy:23
-- comment 创建BM成本分析统计表
CREATE TABLE `biz_business_manager_statistics`
(
    `id`                         bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `statistics_date`            date           NOT NULL COMMENT '统计日期',
    `remaining_normal_inventory` int            NOT NULL DEFAULT '0' COMMENT '剩余正常库存',
    `daily_recycle_count`        int            NOT NULL DEFAULT '0' COMMENT '当日回收数量',
    `daily_received_count`       int            NOT NULL DEFAULT '0' COMMENT '当天接入坑位',
    `total_cost`                 decimal(10, 2) NOT NULL DEFAULT '0' COMMENT '总成本',
    `used_normal`                int            NOT NULL DEFAULT '0' COMMENT '已使用正常',
    `unused_normal`              int            NOT NULL DEFAULT '0' COMMENT '未使用正常',
    `used_banned`                int            NOT NULL DEFAULT '0' COMMENT '已使用封禁',
    `unused_banned`              int            NOT NULL DEFAULT '0' COMMENT '未使用封禁',
    `remaining_normal_count`     int            NOT NULL DEFAULT '0' COMMENT '剩余正常坑位',
    `daily_remaining_count`      int            NOT NULL DEFAULT '0' COMMENT '剩余正常坑位',
    `survival_rate`              decimal(5, 2)  NOT NULL DEFAULT '0' COMMENT '存活率',
    `prepare_survival_rate`      decimal(5, 2)  NOT NULL DEFAULT '0' COMMENT '备户存活率',
    `average_prepare_cost`       decimal(10, 2) NOT NULL DEFAULT '0' COMMENT '平均备户成本',
    `daily_sales`                int            NOT NULL DEFAULT '0' COMMENT '当日出售',
    `daily_banned`               int            NOT NULL DEFAULT '0' COMMENT '当日封禁',
    `order_cost`                 decimal(10, 2) NOT NULL DEFAULT 0 NULL COMMENT '下户成本',
    create_time                  datetime       not null comment '创建时间',
    create_user                  bigint         not null comment '创建人',
    update_user                  bigint comment '更新人',
    update_time                  datetime null comment '更新时间',
    PRIMARY KEY (`id`),
    KEY                          `idx_statistics_date` (`statistics_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='BM成本分析统计表';

-- changeset yyy:24
-- comment 下户订单增加cost
alter table biz_ad_account_order
    add cost decimal(10, 2) not null default 0 comment '下户成本';


-- changeset yqx:23
-- comment 新增字段
ALTER TABLE `biz_personal_account`
    ADD COLUMN `platform_account_id` varchar(64) NOT NULL COMMENT '广告户ID' AFTER `purchase_time`;

-- changeset hans:58
-- comment 新增字段
alter table biz_card_transaction
    add ad_account_id varchar(64) default '' not null comment '广告户ID' after ad_account_time;

create index biz_card_transaction_ad_account_id_index
    on biz_card_transaction (ad_account_id);


-- changeset yyy:25
-- comment sys_user表增加光子易用卡人
alter table sys_user
    add gzy_card_holder_id varchar(100) default '' not null comment '光子易用卡人';

-- changeset yyy:26
-- comment biz_card表增加用卡人
alter table biz_card
    add platform_card_holder_id varchar(100) default '' not null comment '第三方平台用卡人';

-- changeset hans:59
-- comment 新增结算订单相关表
CREATE TABLE IF NOT EXISTS biz_settle_order
(
    id
    bigint
    not
    null
    comment
    'ID'
    primary
    key,
    customer_id
    bigint
    not
    null
    comment
    '客户',
    total_spent
    decimal
(
    10,
    2
) not null comment '总消耗',
    settle_spent decimal
(
    10,
    2
) not null comment '结算消耗',
    settle_time datetime not null comment '结算时间',
    create_time datetime not null comment '创建时间',
    create_user bigint not null comment '创建人'
    )
    comment '结算订单';

CREATE TABLE IF NOT EXISTS biz_settle_order_item
(
    id
    bigint
    not
    null
    comment
    'ID'
    primary
    key,
    order_id
    bigint
    not
    null
    comment
    '关联订单',
    platform_ad_id
    varchar
(
    64
) not null comment '广告户ID',
    spent decimal
(
    10,
    2
) not null comment '消耗',
    create_time datetime not null comment '创建时间'
    )
    comment '结算订单详情';


alter table biz_customer
    add settle_type int not null default 1 comment '结算模式';

alter table biz_customer
    add settle_limit_amount decimal(10, 2) default 0 not null comment '结算限额';

alter table biz_customer
    add warn_limit_amount decimal(10, 2) default 0 not null comment '预警限额';

alter table biz_customer
    add last_settle_spent decimal(10, 2) default 0 not null comment '上一次结算消耗';


-- changeset hans:60
-- comment 新增字段
alter table biz_business_manager_statistics
    add daily_sales_for_bm int default 0 not null comment 'bm户出售数量' after daily_sales;

-- changeset hans:61
-- comment 新增字段
alter table biz_business_manager_statistics
    add inventory_detail varchar(255) default '' not null comment '库存详情' after remaining_normal_inventory;


-- changeset hans:62
-- comment 新增字段
alter table biz_ad_account
    add bm1_browser varchar(64) default '' not null comment 'bm1浏览器';

-- changeset yyy:27
-- comment 添加退款状态字段
ALTER TABLE biz_ad_account_order
    ADD COLUMN refunded tinyint(1) DEFAULT 0 NOT NULL COMMENT '是否已退款';

-- changeset hans:63
-- comment 新增字段
alter table biz_ad_account_order
    add start_campaign_time datetime null comment '上系列时间';

-- changeset yyy:28
-- comment 添加客户状态字段
ALTER TABLE biz_customer
    ADD COLUMN status tinyint(1) DEFAULT 1 NOT NULL COMMENT '客户状态：1-正常，2-终止';


-- changeset yyy:29
-- comment 添加客户终止时间字段
ALTER TABLE biz_customer
    ADD COLUMN terminate_time datetime NULL COMMENT '终止时间';

-- changeset yyy:30
-- comment 客户需求表增加广告户相关字段
ALTER TABLE biz_customer_requirement
    ADD COLUMN bm_type int NULL COMMENT 'bm类型',
    ADD COLUMN customer_bm_id varchar(64) NOT NULL DEFAULT '' COMMENT '客户BM ID',
    ADD COLUMN pay_amount decimal(10,2) NULL COMMENT '开户费';

-- changeset yyy:31
-- comment 下户订单表增加客户需求ID
alter table biz_ad_account_order
    add customer_requirement_id bigint not null default 0 comment '客户需求ID';

-- changeset yyy:32
-- comment 客户需求表增加广告户名称字段
ALTER TABLE biz_customer_requirement
    ADD COLUMN ad_account_name varchar(200) COMMENT '广告户名称';

-- changeset hans:64
-- comment 新增字段
alter table biz_ad_account
    add bm1_id bigint null comment '关联bm1 id';

alter table biz_business_manager
    add is_external bit default b'0' not null comment '是否是外部号';


alter table biz_business_manager
    add is_enterprise_auth bit default b'0' not null comment '是否是企业认证';

-- changeset yqx:24
-- comment 新增字段
ALTER TABLE `biz_ip`
    ADD COLUMN `expire_time` datetime NULL COMMENT '过期时间' AFTER `success_times`;

-- changeset hans:65
-- comment 新增字段
alter table biz_personal_account
    add unique_key varchar(20) default '' not null comment '唯一key'

-- changeset hans:66
-- comment 新增表
CREATE TABLE IF NOT EXISTS biz_business_manager_user
(
    id
    bigint
    not
    null
    comment
    'ID',
    bm_id
    varchar
(
    64
) not null comment 'BM ID',
    user_id varchar
(
    64
) not null comment 'USER ID',
    username varchar
(
    255
) default '' not null comment '用户名',
    user_email varchar
(
    64
) default '' not null comment '邮箱',
    user_role varchar
(
    20
) default '' not null comment '用户权限',
    is_remove bit default b'0' not null comment '是否移除',
    constraint biz_business_manager_user_pk
    primary key
(
    id
)
    )
    comment 'bm管理员';


-- changeset hans:67
-- comment 新增字段
alter table biz_personal_account
    add email varchar(255) default '' not null comment '邮箱';

-- changeset hans:68
-- comment 新增字段
alter table biz_ad_account
    add is_remove_admin bit default b'0' not null comment '是否剔除管理';

alter table biz_business_manager
    add is_remove_admin bit default b'0' not null comment '是否剔除管理';

-- changeset hans:69
-- comment 新增字段
alter table biz_ad_account
    add is_low_limit bit default b'0' not null comment '是否低限';

alter table biz_business_manager
    add reserve_browser_bak varchar(20) default '' not null comment '备用号2';


-- changeset hans:70
-- comment 新增表
CREATE TABLE IF NOT EXISTS biz_ads_power
(
    id
    bigint
    not
    null
    comment
    'ID',
    user_id
    varchar
(
    20
) not null comment 'user_id',
    serial_number varchar
(
    20
) not null comment '编号',
    remark varchar
(
    255
) default '' not null comment '备注',
    constraint biz_ads_power_pk
    primary key
(
    id
)
    )
    comment '指纹浏览器';

-- changeset hans:71
-- comment 新增字段
alter table biz_customer
    add is_self_account bit default b'0' not null comment '是否是自用号';

-- changeset hans:72
-- comment 新增字段
alter table biz_ad_account
    add real_adtrust_dsl decimal(10, 2) null comment '真实限额';


-- changeset hans:73
-- comment 新增字段
alter table biz_fb_account
    add price decimal(10, 2) default 0 not null comment '价格';

alter table biz_business_manager_statistics
    add purchase_cost decimal(10, 2) default 0 not null comment '采购成本';

-- changeset hans:74
-- comment 新增表
create table biz_customer_question
(
    id          bigint                  not null comment 'ID',
    customer_id bigint                  not null comment '客户ID',
    type        int                     not null comment '类型',
    num         int                     not null comment '问题数量',
    accounts    varchar(255) default '' not null comment '问题账号',
    create_time datetime                not null comment '创建时间',
    create_user bigint                  not null comment '创建人',
    constraint biz_customer_question_pk
        primary key (id)
) comment '客户问题';

-- changeset yqx:25
-- comment 新增字段
ALTER TABLE `biz_customer_question`
    ADD COLUMN `order_time` datetime NULL COMMENT '下户日期' AFTER `accounts`;

-- changeset hans:75
-- comment 新增字段
alter table biz_card_transaction
    add customer_id bigint null comment '关联客户';

-- changeset yqx:27
-- comment 新增表
CREATE TABLE `biz_tag`
(
    `id`          bigint       NOT NULL COMMENT 'ID',
    `name`        varchar(255) NOT NULL COMMENT '名称',
    `create_time` datetime     NOT NULL COMMENT '创建日期',
    `create_user` bigint       NOT NULL COMMENT '创建人',
    PRIMARY KEY (`id`)
) COMMENT = '标签';

CREATE TABLE `biz_tag_relation`
(
    `id`          bigint   NOT NULL COMMENT 'ID',
    `tag_id`      bigint   NOT NULL COMMENT '标签ID',
    `type`        int      NOT NULL COMMENT '类型(1=广告户 2=下户订单)',
    `relation_id` bigint   NOT NULL COMMENT '关联ID',
    `create_time` datetime NOT NULL COMMENT '创建日期',
    PRIMARY KEY (`id`)
) COMMENT = '标签关联';

-- changeset yqx:26
-- comment 新增字段
ALTER TABLE `biz_business_manager_item`
    ADD COLUMN `type` int NOT NULL COMMENT 'BM类型' AFTER `create_time`,
    ADD COLUMN `channel_id` bigint NOT NULL COMMENT '渠道ID' AFTER `type`,
    ADD COLUMN `own_method` int    NOT NULL COMMENT '权限（1=认领，2=授权）' AFTER `channel_id`;
ALTER TABLE `biz_business_manager`
    MODIFY COLUMN `num` int NOT NULL DEFAULT 0 COMMENT '坑位' AFTER `update_user`;
ALTER TABLE `biz_ad_account`
    ADD COLUMN `bm_item_type` int NULL COMMENT 'bm类型' AFTER `real_adtrust_dsl`,
    ADD COLUMN `bm_item_channel_id` bigint NULL COMMENT '坑位渠道ID' AFTER `bm_item_type`;


-- changeset yqx:28
-- comment 新增字段
ALTER TABLE `biz_business_manager_item`
    ADD COLUMN `remark` varchar(255) NULL COMMENT '备注' AFTER `own_method`;


-- changeset hans:76
-- comment 新增字段
alter table biz_ad_account
    add vo_status int default 1 not null comment 'vo状态';

-- changeset yyy:33
-- comment 增加索引
create index idx_ad_account_bm_id on biz_ad_account (business_manager_id);

-- changeset yyy:34
-- comment 增加封禁原因
ALTER TABLE biz_business_manager
    ADD COLUMN banned_reason tinyint NOT NULL DEFAULT 0 COMMENT '封禁原因';

-- changeset hans:77
-- comment 新增表
CREATE TABLE IF NOT EXISTS biz_test_order
(
    id
    bigint
    not
    null
    comment
    'ID',
    title
    varchar
(
    100
) not null comment '测试标题',
    trello_url varchar
(
    100
) not null default '' comment 'trello链接',
    status int default 1 not null comment '状态(1=待进行，2=进行中，3=已完成）',
    create_time datetime not null comment '创建时间',
    create_user bigint not null comment '创建人',
    constraint biz_test_order_pk
    primary key
(
    id
)
    )
    comment '测试任务';

CREATE TABLE IF NOT EXISTS biz_test_order_item
(
    id
    bigint
    not
    null
    comment
    'ID',
    order_id
    bigint
    not
    null
    comment
    '关联测试任务',
    platform_ad_id
    varchar
(
    64
) not null comment '广告户ID',
    status int default 1 not null comment '状态（1=待进行，2=进行中，3=测试通过，4=测试失败）',
    remark varchar
(
    255
) default '' not null comment '备注',
    create_time datetime null comment '创建时间',
    constraint biz_test_order_item_pk
    primary key
(
    id
)
    )
    comment '测试任务详情';

-- changeset yqx:29
-- comment 新增字段
ALTER TABLE `biz_business_manager`
    ADD COLUMN `is_bu` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否补号' AFTER `banned_reason`;


-- changeset hans:78
-- comment 新增字段
alter table biz_ad_account
    add headers text null comment '请求头';

-- changeset hans:79
-- comment 新增表
CREATE TABLE IF NOT EXISTS biz_purchase_order
(
    id
    bigint
    not
    null
    comment
    'ID',
    channel_id
    bigint
    not
    null
    comment
    '渠道ID',
    type
    int
    not
    null
    comment
    '物料类型',
    expect_num
    int
    not
    null
    comment
    '预计采购数量',
    total_price
    decimal
(
    10,
    2
) default 0 not null comment '预计采购金额',
    is_pay bit default b'0' not null comment '是否付款',
    pay_time datetime null comment '付款时间',
    pay_price decimal
(
    10,
    2
) null comment '付款金额',
    status int default 1 not null comment '状态（1=进行中，2=已完成）',
    finish_time datetime null comment '完成时间',
    remark varchar
(
    255
) default '' not null comment '备注',
    create_user bigint not null comment '创建人',
    create_time datetime not null comment '创建时间',
    constraint biz_purchase_order_pk
    primary key
(
    id
)
    )
    comment '采购订单';

CREATE TABLE IF NOT EXISTS biz_purchase_receive_order
(
    id
    bigint
    not
    null
    comment
    'ID',
    purchase_order_id
    bigint
    not
    null
    comment
    '关联采购单',
    receive_num
    int
    not
    null
    comment
    '验收数量',
    create_user
    bigint
    not
    null
    comment
    '验收人',
    create_time
    datetime
    not
    null
    comment
    '验收时间',
    constraint
    biz_purchase_receive_order_pk
    primary
    key
(
    id
)
    )
    comment '采购验收单';

-- changeset hans:80
-- comment 新增字段
alter table biz_purchase_receive_order
    add receive_price decimal(10, 2) not null comment '验收金额' after receive_num;

-- changeset hans:81
-- comment 新增字段
alter table biz_material
    add channel_id bigint null comment '关联渠道' after channel;

-- changeset hans:82
-- comment 新增字段
alter table biz_personal_account
    add channel_id bigint null comment '关联渠道' after channel_name;

alter table biz_personal_account
    modify channel_name varchar (64) default '' null comment '渠道';

-- changeset hans:83
-- comment 新增字段
alter table biz_business_manager_item
    add is_bu bit default b'0' not null comment '是否补号';

-- changeset hans:84
-- comment 新增字段
alter table biz_personal_account
    add headers text null comment '请求头';

alter table biz_personal_account
    add proxy varchar(255) default '' not null comment '代理ip';


-- changeset hans:85
-- comment 新增字段
alter table biz_purchase_receive_order
    add receive_time datetime null comment '验收时间' after receive_price;



-- changeset hans:86
-- comment 新增字段
alter table biz_personal_account
    add is_sync bit default b'0' not null comment '是否开启同步';

-- changeset hans:87
-- comment 新增字段
alter table biz_purchase_order
    add receive_num int default 0 not null comment '验收数量';

alter table biz_purchase_order
    add receive_price decimal(10, 2) default 0 not null comment '验收金额';

alter table biz_purchase_order
    add receive_user varchar(64) default '' not null comment '验收人';



-- changeset hans:88
-- comment 修改字段
alter table biz_card_transaction
    change ad_account_time stat_time datetime null comment '统计时间';

alter table biz_card_transaction
    add origin_transaction_id varchar(64) default '' not null comment '关联交易id';

-- changeset yqx:30
-- comment 新增字段
ALTER TABLE `biz_customer_question`
    ADD COLUMN `remark` varchar(255) NULL COMMENT '备注' AFTER `create_user`;

-- changeset hans:89
-- comment 新增字段
alter table biz_ad_account
    add prepay_account_balance decimal(10, 2) default 0 not null comment '充值金余额';

alter table biz_ad_account_order
    add enable_prepay bit default b'0' not null comment '开启预充';

-- changeset hans:90
-- comment 新增字段
alter table biz_ad_account
    add browser_id varchar(64) comment '浏览器ID';


alter table biz_fb_account
    add browser_id varchar(64) comment '浏览器ID';

alter table biz_personal_account
    add browser_id varchar(64) comment '浏览器ID';

-- changeset yyy:40
-- comment 交易流水表增加优化索引
ALTER TABLE biz_card_transaction
    ADD INDEX idx_ad_account_customer_stat (ad_account_id, customer_id, stat_time);
ALTER TABLE biz_card_transaction
    ADD INDEX idx_trans_time_status (trans_status,stat_time,ad_account_id,trans_amount);
ALTER TABLE biz_ad_account_order
    ADD INDEX idx_status_finish_time (ad_account_id, status, finish_time);
ALTER TABLE biz_ad_account
    ADD INDEX idx_platform_status (platform_ad_id, account_status,business_manager_id,bm1_id);

-- changeset yyy:41
-- comment 性能优化增加索引
CREATE INDEX idx_insight_customer_account ON biz_ad_account_insight (customer_id, ad_account_id, spend);
CREATE INDEX idx_customer_status_business_user ON biz_customer (business_user_id, status);
CREATE INDEX idx_china_time_status_amount ON biz_card_transaction (china_time, platform, trans_status, trans_amount);

-- changeset hans:91
-- comment 新增字段
alter table biz_ad_account_order
    add is_one_dollar bit default b'0' not null comment '是否一刀流';

-- changeset hans:92
-- comment 新增字段
alter table biz_card
    add create_user bigint null comment '开卡人' after create_time;

-- changeset yqx:31
-- comment 新增字段
ALTER TABLE `biz_purchase_order`
    ADD COLUMN `purchase_time` datetime NULL COMMENT '购买时间' AFTER `receive_user`;


-- changeset hans:93
-- comment 新增字段
alter table biz_ad_account
    add billing_threshold_currency_amount decimal(10, 2) default 0 not null comment '付费门槛';

-- changeset hans:94
-- comment 新增字段
alter table biz_ad_account
    add is_prepay bit default b'0' not null comment '是否预充户';

-- changeset hans:95
-- comment 新增表
create table biz_white_email
(
    id    bigint      not null comment 'ID',
    email varchar(64) not null comment '邮箱',
    constraint biz_white_email_pk
        primary key (id)
) comment '白名单邮箱';

-- changeset yyy:42
-- comment 广告户表增加可用字段
ALTER TABLE biz_ad_account ADD COLUMN usable bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可用', ADD COLUMN unusable_reason INT NOT NULL DEFAULT 0 COMMENT '不可用原因';

-- changeset hans:96
-- comment 新增字段
alter table biz_business_manager
    add sync_browser_no varchar(64) default '' not null comment '同步浏览器';


-- changeset yyy:45
-- comment 增加广告系列、广告组、广告表
CREATE TABLE `biz_fb_ad_campaigns` (
   `id`            BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键ID',
   `platform_id`   VARCHAR(100)  NOT NULL COMMENT '平台ID（Facebook 返回的 campaign_id）',
   `name`          VARCHAR(500) COMMENT '系列名称',
   `ad_account_id` VARCHAR(64)  NOT NULL COMMENT '广告户ID（Facebook 返回的 act_xxx）',
   `status`        VARCHAR(100) COMMENT '状态：ACTIVE 正常投放 / PAUSED 暂停 / DELETED 已删除 / ARCHIVED 已归档',
   `daily_budget`  INT UNSIGNED COMMENT '每日预算，单位为分（cent）',
   `lifetime_budget` INT UNSIGNED COMMENT '总预算（分）；0 表示不限',
   `bid_strategy`  VARCHAR(100) COMMENT '定价策略: LOWEST_COST_WITHOUT_CAP、COST_CAP、BID_CAP 等',
   `create_time`    DATETIME     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_time`    DATETIME     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uniq_platform_id` (`platform_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Facebook 广告系列';

CREATE TABLE `biz_fb_ad_sets` (
  `id`            BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键ID',
  `platform_id`   VARCHAR(100)  NOT NULL COMMENT '平台ID（Facebook 返回的 adset_id）',
  `name`          VARCHAR(500) COMMENT '广告组名称',
  `ad_account_id` VARCHAR(64)  NOT NULL COMMENT '广告户ID',
  `status`        VARCHAR(100) COMMENT '状态：ACTIVE 正常投放 / PAUSED 暂停 / DELETED 已删除 / ARCHIVED 已归档',
  `daily_budget`  INT UNSIGNED COMMENT '每日预算，单位为分（cent）',
  `lifetime_budget` INT UNSIGNED COMMENT '总预算（分）',
  `bid_strategy`  VARCHAR(100) COMMENT '定价策略',
  `campaign_id`   VARCHAR(64)  NOT NULL COMMENT '所属系列ID',
  `create_time`    DATETIME     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time`    DATETIME     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_platform_id` (`platform_id`),
  KEY `idx_campaign_id` (`campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Facebook 广告组';

CREATE TABLE `biz_fb_ad` (
     `id`              BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键ID',
     `platform_id`     VARCHAR(64)  NOT NULL COMMENT '平台ID（Facebook 返回的 ad_id）',
     `name`            VARCHAR(500) NOT NULL COMMENT '广告名称',
     `ad_account_id`   VARCHAR(64)  NOT NULL COMMENT '广告户ID',
     `status`          VARCHAR(100) COMMENT '配置状态：ACTIVE 正常 / PAUSED 暂停 / DELETED 已删除 / ARCHIVED 已归档',
     `effective_status` VARCHAR(100) COMMENT '实际生效状态：ACTIVE、PAUSED、CAMPAIGN_PAUSED、ADSET_PAUSED、DISAPPROVED、PENDING_REVIEW、WITH_ISSUES、LEARNING等',
     `daily_budget`    INT UNSIGNED COMMENT '每日预算，单位为分（cent）（通常继承父级，留 0 即可）',
     `lifetime_budget` INT UNSIGNED COMMENT '总预算（分）',
     `bid_strategy`    VARCHAR(100) COMMENT '定价策略',
     `adset_id`        VARCHAR(64)  NOT NULL COMMENT '所属广告组ID（对应 biz_fb_ad_sets.platform_id）',
     `campaign_id`     VARCHAR(64)  NOT NULL COMMENT '所属系列ID（冗余存储，方便查询）',
     `create_time`      DATETIME     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time`      DATETIME     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uniq_platform_id` (`platform_id`),
     KEY `idx_adset_id` (`adset_id`),
     KEY `idx_campaign_id` (`campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Facebook 广告';

-- changeset yyy:46
-- comment 广告系列增加开启时间
alter table biz_fb_ad_campaigns add start_time datetime comment '广告开启时间(转为北京时间)';

-- changeset yyy:47
-- comment 增加广告系列、广告组、广告表的索引
ALTER TABLE biz_fb_ad_campaigns ADD INDEX idx_ad_account_id (ad_account_id);
ALTER TABLE biz_fb_ad_sets ADD INDEX idx_ad_account_id (ad_account_id);
ALTER TABLE biz_fb_ad ADD INDEX idx_ad_account_id (ad_account_id);

-- changeset hans:97
-- comment 新增字段
alter table biz_ad_account
    add disabled_reason varchar(20) default '' not null comment 'fb封禁原因';

-- changeset hans:98
-- comment 新增字段
create table if not exists biz_customer_daily_stat
(
    id                   bigint                      not null comment 'ID'
        primary key,
    customer_id          bigint                      not null comment '关联客户',
    stat_date            date                        not null comment '统计时间',
    total_account        int            default 0    not null comment '总户数',
    total_finish_account int            default 0    not null comment '当前户数',
    total_normal_account int            default 0    not null comment '正常户数',
    today_used_account   int            default 0    not null comment '使用户数',
    today_ban_account    int            default 0    not null comment '封禁数量',
    today_open_account   int            default 0    not null comment '下户数',
    today_card_spent     decimal(10, 2) default 0.00 not null comment '卡台消耗',
    create_time          datetime                    not null comment '创建时间'
)
    comment '客户每日统计';

-- changeset hans:99
-- comment 新增中介模块
create table if not exists biz_agent
(
    id               bigint                  not null comment 'ID'
    primary key,
    name             varchar(64)             not null comment '名称',
    business_user_id bigint                  not null comment '关联商务',
    remark           varchar(255) default '' not null comment '备注',
    rebate_rule      varchar(255) default '' not null comment '返点规则',
    create_time      datetime                not null comment '创建时间',
    create_user      bigint                  not null comment '创建人'
    )
    comment '中介';

create table biz_agent_rebate
(
    id                  bigint                      not null comment 'ID'
        primary key,
    agent_id            bigint                      not null comment '关联中介',
    customer_id         bigint                      not null comment '关联客户',
    settle_month        varchar(20)                 not null comment '结算月份',
    settle_amount       decimal(10, 2)              not null comment '结算金额',
    account_open_amount decimal(10, 2) default 0.00 not null comment '开户费',
    rebate_date         date                        not null comment '返点日期',
    rebate_amount       decimal(10, 2)              null comment '返点金额',
    rebate_rule         varchar(255)   default ''   not null comment '返点规则',
    remark              varchar(255)   default ''   not null comment '备注',
    create_time         datetime                    not null comment '创建时间',
    create_user         bigint                      not null comment '创建人'
)
    comment '中介返点';



alter table biz_customer
    add agent_id bigint not null comment '关联中介' after business_user_id;

alter table biz_ad_account_order
    add cost_party int default 2 not null comment '成本责任方(1=自己，2=客户）';

-- changeset hans:100
-- comment 新增字段
alter table biz_customer
    add rebate_rule varchar(255) default '' not null comment '返点政策' after agent_id;

-- changeset hans:101
-- comment 新增字段
alter table biz_fb_ad_campaigns
    add effective_status varchar(100) default '' not null comment '生效状态';

alter table biz_fb_ad_campaigns
    add delivery_status varchar(100) default '' not null comment '投放状态';

-- changeset hans:102
-- comment 新增字段
create table if not exists biz_operator_task_record
(
    id             bigint                  not null comment 'ID',
    customer_id    bigint                  null comment '客户ID',
    platform_ad_id varchar(64)  default '' not null comment '广告户',
    type           int                     not null comment '类型',
    num            int                     not null comment '数量',
    remark         varchar(255) default '' not null comment '备注',
    create_user    bigint                  not null comment '创建人',
    create_time    datetime                not null comment '创建时间',
    constraint biz_operator_task_record_pk
        primary key (id)
)
    comment '运营人员工作记录';

-- changeset hans:103
-- comment 新增表
create table if not exists  biz_wallet_transfer
(
    id               bigint                          not null comment 'ID'
        primary key,
    transaction_id   varchar(255)                    not null comment '交易号',
    confirmed        bit                             not null comment '是否确认',
    trans_time       datetime                        not null comment '交易时间',
    contract_ret     varchar(64)    default ''       not null comment 'contractRet',
    contract_address varchar(255)   default ''       not null comment '合约地址',
    contract_type    varchar(64)    default ''       not null comment '合约类型',
    event_type       varchar(64)                     not null comment '事件类型',
    final_result     varchar(64)    default ''       not null comment 'finalResult',
    from_address     varchar(64)                     not null comment '来源地址',
    amount           decimal(20, 6) default 0.000000 not null comment '金额',
    to_address       varchar(64)                     not null comment 'to_address',
    constraint biz_wallet_transfer_transaction_id_uindex
        unique (transaction_id)
)
    comment '钱包流水';


create table if not exists  biz_customer_wallet
(
    id             bigint      not null comment 'ID',
    customer_id    bigint      not null comment '关联客户',
    wallet_address varchar(64) not null comment '钱包地址',
    create_time    datetime    null comment '创建时间',
    create_user    bigint      not null comment '创建人',
    constraint biz_customer_wallet_pk
        primary key (id)
)
    comment '客户钱包';

-- changeset hans:104
-- comment 新增字段
alter table biz_customer_balance_record
    add wallet_transaction_id varchar(255) default '' not null comment '钱包交易号';



-- changeset yyy:49
-- comment 广告组、广告增加投放状态
alter table biz_fb_ad_sets
    add delivery_status varchar(100) default '' not null comment '投放状态';
alter table biz_fb_ad
    add delivery_status varchar(100) default '' not null comment '投放状态';

-- changeset hans:105
-- comment 新增字段
alter table biz_wallet_transfer
    add token_symbol varchar(64) default '' not null comment '代币符号';


-- changeset yyy:50
-- comment 增加第一次上系列时间
alter table biz_ad_account_order add first_start_campaign_time datetime null comment '第一次上系列时间';


-- changeset hans:106
-- comment 新增字段
alter table biz_agent_rebate
    add actual_rebate_amount decimal(20, 6) default 0 not null comment '实际返点金额' after rebate_amount;

alter table biz_agent_rebate
    change rebate_date rebate_time datetime not null comment '返点日期';



