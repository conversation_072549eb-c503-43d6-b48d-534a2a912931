-- liquibase formatted sql

-- changeset yqx:10000
-- comment 客户新增用户名密码
ALTER TABLE `biz_customer`
    ADD COLUMN `username` varchar(255) NULL COMMENT '用户名' AFTER `product_name`,
    ADD COLUMN `password` varchar(255) NULL COMMENT '密码' AFTER `username`;

-- changeset yqx:10001
-- comment 新增客户邮箱
ALTER TABLE `biz_customer_requirement`
    ADD COLUMN `customer_email` varchar(255) NULL COMMENT '客户邮箱' AFTER `ad_account_name`;
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `customer_email` varchar(255) NULL COMMENT '客户邮箱' AFTER `is_one_dollar`;
-- changeset yqx:10002
-- comment 接收状态
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `take_status` bit(1) NOT NULL DEFAULT b'0' COMMENT '接收状态' AFTER `customer_email`;

-- changeset yqx:10003
-- comment 新建表
CREATE TABLE `biz_customer_order_group`
(
    `id`          bigint NOT NULL COMMENT 'ID',
    `name`        varchar(255) DEFAULT NULL COMMENT '名称',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `customer_id` bigint       DEFAULT NULL COMMENT '客户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='客户下户订单分组';
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `group_id` bigint NULL COMMENT '客户分组ID' AFTER `take_status`;


-- changeset hans:10001
-- comment 新建表
alter table biz_ad_account_order
    add clear_status int NOT NULL DEFAULT 1 comment '清零状态';

alter table biz_ad_account_order
    add clear_time datetime null comment '清零时间';

alter table biz_clear_order
    add ad_account_order_id bigint null comment '关联下户订单';


-- changeset hans:10002
-- comment 新增字段
alter table biz_purchase_order
    add receive_date date null comment '验收时间' after receive_price;


-- changeset yqx:10004
-- comment 新增字段
ALTER TABLE `sys_user`
    ADD COLUMN `job_rank` int NULL COMMENT '岗位职级',
    ADD COLUMN `telegram_id` int NULL COMMENT '飞机号';


-- changeset yqx:10005
-- comment 新增字段
ALTER TABLE `biz_customer`
    ADD COLUMN `cooperate_time` datetime NULL COMMENT '合作时间';

-- changeset yqx:10006
-- comment 新增字段
CREATE TABLE `biz_sales_daily_summary`
(
    `id`          BIGINT   NOT NULL COMMENT 'ID',
    `record_date` DATE     NOT NULL COMMENT '日报记录的日期',
    `content`     TEXT     NOT NULL COMMENT '日报具体内容，支持长文本',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `create_user` bigint   NOT NULL COMMENT '创建人',
    `update_time` datetime NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商务日报';
ALTER TABLE `sys_user`
    MODIFY COLUMN `telegram_id` varchar(100) NULL DEFAULT NULL COMMENT '飞机号' AFTER `job_rank`;

-- changeset yqx:10007
-- comment 新增表
CREATE TABLE `biz_sales_schedule`
(
    `id`            BIGINT   NOT NULL COMMENT 'ID',
    `sales_id`      BIGINT   NOT NULL COMMENT '商务人员ID',
    `schedule_date` DATE     NOT NULL COMMENT '排班/请假的具体日期',
    `schedule_type` TINYINT  NOT NULL COMMENT '类型: 1=请假, 2=排班',
    `create_time`   DATETIME NOT NULL COMMENT '创建时间',
    `update_time`   DATETIME NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商务人员排班/请假记录表';


-- changeset yyh:10001
-- comment 广告组表增加事件类型和优化目标
alter table biz_fb_ad_sets add custom_event_type varchar(200) comment '自定义事件类型';
alter table biz_fb_ad_sets add optimization_goal varchar(200) comment '优化目标';

-- changeset hans:10003
-- comment 新增字段
alter table biz_card
    add ad_platform int default 1 not null comment '广告平台';

alter table biz_ad_account_card
    add ad_platform int default 1 not null comment '广告平台';

alter table biz_card_transaction
    add ad_platform int default 1 not null comment '广告平台';

-- changeset yqx:10008
-- comment 新增表
CREATE TABLE `biz_customer_business_user`
(
    `id`          BIGINT AUTO_INCREMENT COMMENT 'ID',
    `customer_id` BIGINT   NOT NULL COMMENT '客户ID，用于关联客户信息表',
    `user_id`     BIGINT   NOT NULL COMMENT '关联的商务用户ID',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)

) ENGINE = InnoDB COMMENT ='客户商务对接表';
ALTER TABLE biz_ad_account_insight ADD COLUMN business_user_id BIGINT COMMENT '关联商务';
ALTER TABLE biz_ad_account_order ADD COLUMN business_user_id BIGINT COMMENT '关联商务';
ALTER TABLE biz_customer_balance_record ADD COLUMN business_user_id BIGINT COMMENT '关联商务';
ALTER TABLE biz_card_transaction ADD COLUMN business_user_id BIGINT COMMENT '关联商务';
ALTER TABLE biz_customer_withdraw_order ADD COLUMN business_user_id BIGINT COMMENT '关联商务';


