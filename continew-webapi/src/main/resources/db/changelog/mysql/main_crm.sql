-- liquibase formatted sql


-- changeset yyy:640
-- comment 创建社交账号表
create table biz_social_account
(
    id           bigint       not null comment 'ID'
        primary key,
    account_type tinyint  not null comment '账号类型：telegram、wechat、line、phone',
    account      varchar(200)  not null comment '账号',
    assignee_id  bigint      not null default 0 comment '分配人',
    status       int         not null default 1 comment '账号状态：1-启用、2-禁用',
    create_time  datetime    not null comment '创建时间',
    create_user  bigint      not null comment '创建人',
    update_time  datetime    null comment '更新时间',
    update_user  bigint      null comment '更新人'
) comment '社交账号';

-- changeset yyy:641
-- comment 创建社交账号表索引
create index idx_social_account_assignee on biz_social_account (assignee_id);
create index idx_social_account_status on biz_social_account (status);

-- changeset yyy:642
-- comment 创建来源分组表
create table biz_source_group
(
    id          bigint       not null comment 'ID'
        primary key,
    name        varchar(100)  not null comment '分组名称',
    create_time datetime     not null comment '创建时间',
    create_user bigint       not null comment '创建人',
    update_time datetime     null comment '更新时间',
    update_user bigint       null comment '更新人'
) comment '来源分组';

-- changeset yyy:643
-- comment 创建来源表
create table biz_source
(
    id          bigint       not null comment 'ID' primary key,
    group_id    bigint       not null comment '来源分组ID',
    name        varchar(200)  not null comment '来源名称',
    create_time datetime     not null comment '创建时间',
    create_user bigint       not null comment '创建人',
    update_time datetime     null comment '更新时间',
    update_user bigint       null comment '更新人'
) comment '来源';

-- changeset yyy:644
-- comment 创建来源表索引
create index idx_source_group on biz_source (group_id);

-- changeset yyy:645
-- comment 创建线索表
create table biz_lead
(
    id                bigint       not null comment 'ID' primary key,
    source_id         bigint       not null default 0 comment '来源ID',
    status           tinyint          not null default 1 comment '状态：1-待跟进、2-跟进中、3-长期跟进、4-创建商机、5-无效',
    customer_name    varchar(100)  not null comment '客户名称',
    customer_industry int  not null default 0 comment '客户行业',
    requirement      varchar(5000) not null default '' comment '需求内容',
    remind_time      datetime        null comment '提醒时间',
    invalid_reason   int  not null default 0 comment '无效原因',
    opportunity_id   bigint       not null default 0 comment '关联商机ID',
    last_follow_time datetime     null comment '最近跟进时间',
    handler_user_id  bigint       not null default 0 comment '对接人',
    remark          varchar(1000)  comment '备注',
    create_time     datetime     not null comment '创建时间',
    create_user     bigint       not null comment '创建人',
    update_time     datetime     null comment '更新时间',
    update_user     bigint       null comment '更新人'
) comment '线索';

-- changeset yyy:646
-- comment 创建线索表索引
create index idx_lead_source on biz_lead (source_id);
create index idx_lead_opportunity on biz_lead (opportunity_id);
create index idx_lead_handler on biz_lead (status,handler_user_id);

-- changeset yyy:647
-- comment 创建线索跟进记录表
create table biz_lead_follow
(
    id          bigint       not null comment 'ID' primary key,
    lead_id     bigint       not null comment '线索ID',
    content     varchar(5000)         not null comment '跟进内容',
    attachment  varchar(2000)  comment '附件地址',
    follow_time datetime     not null comment '跟进时间',
    follow_user_id bigint       not null comment '跟进人',
    create_time datetime     not null comment '创建时间',
    create_user bigint       not null comment '创建人'
) comment '线索跟进记录';

-- changeset yyy:648
-- comment 创建线索跟进记录表索引
create index idx_lead_follow_lead on biz_lead_follow (lead_id);

-- changeset yyy:649
-- comment 创建商机表
create table biz_opportunity
(
    id                bigint          not null comment 'ID'
        primary key,
    customer_id      bigint          not null default 0 comment '关联客户ID',
    source_id        bigint          not null default 0 comment '来源ID',
    status           tinyint             not null default 0 comment '状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失',
    requirement     varchar(5000)    not null comment '需求内容',
    remind_time      datetime        null comment '提醒时间',
    lost_reason      int     not null default 0 comment '流失原因',
    last_follow_time datetime        null comment '最近跟进时间',
    handler_user_id       bigint          not null comment '对接人',
    remark           varchar(2000)    comment '备注',
    create_time      datetime        not null comment '创建时间',
    create_user      bigint          not null comment '创建人',
    update_time      datetime        null comment '更新时间',
    update_user      bigint          null comment '更新人'
) comment '商机';

-- changeset yyy:650
-- comment 创建商机表索引
create index idx_opportunity_customer on biz_opportunity (customer_id);
create index idx_opportunity_source on biz_opportunity (source_id);
create index idx_opportunity_handler on biz_opportunity (status, handler_user_id);

-- changeset yyy:651
-- comment 创建商机跟进记录表
create table biz_opportunity_follow
(
    id          bigint       not null comment 'ID'
        primary key,
    opportunity_id bigint    not null comment '商机ID',
    content     varchar(5000)         not null comment '跟进内容',
    attachment  varchar(255) default '' not null comment '附件地址',
    follow_time datetime     not null comment '跟进时间',
    follow_user_id bigint       not null comment '跟进人',
    create_time datetime     not null comment '创建时间',
    create_user bigint       not null comment '创建人'
) comment '商机跟进记录表';

-- changeset yyy:652
-- comment 创建商机跟进记录表索引
create index idx_opportunity_follow_opportunity on biz_opportunity_follow (opportunity_id);

-- changeset yyy:653
-- comment 创建客户标签表
create table biz_customer_tag (
  id          bigint       not null comment 'ID' primary key,
  customer_id bigint       not null comment '客户ID',
  tag_id      bigint       not null comment '标签ID',
  create_time datetime     not null comment '创建时间',
  create_user bigint       not null comment '创建人',
  update_time datetime     null comment '更新时间',
  update_user bigint       null comment '更新人'
) comment '客户标签表';

-- changeset yyy:654
-- comment 创建客户账号信息表
create table biz_customer_account (
  id          bigint       not null comment 'ID' primary key,
  account_type tinyint     not null comment '账号类型：telegram、wechat、line、phone',
  account     varchar(200) not null comment '账号',
  social_account_id bigint not null default 0 comment '关联的社交账号ID',
  remark      varchar(500) comment '备注',
  create_time datetime    not null comment '创建时间',
  create_user bigint      not null comment '创建人',
  update_time datetime    null comment '更新时间',
  update_user bigint      null comment '更新人'
) comment '客户账号信息表';

-- changeset yyy:655
-- comment 创建客户账号信息表索引
create index idx_account_social on biz_customer_account (social_account_id);

-- changeset yyy:656
-- comment 创建客户账号信息关联表
create table biz_customer_account_rel (
  id          bigint      not null comment 'ID' primary key,
  customer_account_id  bigint      not null comment '账号ID',
  entity_type tinyint     not null comment '实体类型：1-线索、2-商机、3-客户',
  entity_id   bigint      not null comment '实体ID',
  is_primary  tinyint     not null default 0 comment '是否主要联系方式：0-否、1-是',
  create_time datetime    not null comment '创建时间',
  create_user bigint      not null comment '创建人'
) comment '客户账号信息关联表';

-- changeset yyy:657
-- comment 创建客户账号信息关联表索引
create index idx_account_rel on biz_customer_account_rel (entity_type, entity_id);

-- changeset yyy:658
-- comment 修改客户表添加类型和来源字段
alter table biz_customer add column `type` tinyint not null default 1 comment '客户类型：1-正式客户、2-潜在客户';
alter table biz_customer add column `source_id` bigint not null default 0 comment '客户来源';

-- changeset yyy:659
-- comment 为线索跟进表添加更新人和更新时间
alter table biz_lead_follow add column `update_time` datetime comment '更新时间';
alter table biz_lead_follow add column `update_user` bigint comment '更新人';

-- changeset yyy:660
-- comment 为商机跟进表添加更新人和更新时间
alter table biz_opportunity_follow add column `update_time` datetime comment '更新时间';
alter table biz_opportunity_follow add column `update_user` bigint comment '更新人';

-- changeset yyy:661
-- comment 修改客户表添加客户行业字段
alter table biz_customer add column `industry` tinyint not null default 0 comment '客户行业';

-- changeset yyy:662
-- comment 客户标签表增加编码字段
alter table biz_customer_tag add column `code` varchar(30) not null default '' comment '编码';

-- changeset yyy:663
-- comment 为客户账号信息关联表添加更新人和更新时间
alter table biz_customer_account_rel add column `update_time` datetime comment '更新时间';
alter table biz_customer_account_rel add column `update_user` bigint comment '更新人';

-- changeset yyy:664
-- comment 增加客户回访策略表
create table biz_customer_visit_strategy (
     id                    bigint       not null comment 'ID' primary key,
     strategy_name         varchar(100) not null comment '策略名称',
     strategy_desc         varchar(500) null comment '策略描述',
     customer_type         tinyint      not null comment '适用客户类型：1-潜在客户，2-正式客户',
     strategy_status       tinyint      not null default 0 comment '策略状态：0-禁用，1-启用',
     strategy_conditions   json         not null comment '策略条件配置(JSON格式)',
     last_scan_time        datetime     null comment '上次扫描时间',
     last_scan_match_count int          null default 0 comment '上次扫描符合条件的客户数',
     create_time           datetime     not null comment '创建时间',
     create_user           bigint       not null comment '创建人',
     update_time           datetime     null comment '更新时间',
     update_user           bigint       null comment '更新人'
) comment '客户回访策略表';

-- changeset yyy:665
-- comment 增加客户回访任务表
create table biz_customer_visit_task (
     id                   bigint       not null comment 'ID' primary key,
     customer_id          bigint       not null comment '客户ID',
     strategy_id          bigint       not null comment '触发策略ID',
     strategy_name        varchar(100) not null comment '触发策略名称',
     task_status          tinyint      not null default 1 comment '任务状态：1-待处理，2-处理中，3-已完成，4-已关闭',
     assignee_id          bigint       not null comment '负责人ID',
     required_finish_time datetime     null comment '要求完成时间',
     visit_method         varchar(20)  null comment '回访方式：电话、微信、邮件、上门拜访、会议等',
     visit_time           datetime     null comment '回访时间',
     visit_summary        varchar(2000) null comment '回访纪要',
     close_reason         varchar(200) null comment '关闭原因',
     attachment_urls      varchar(2000) null comment '附件URL，多个用逗号分隔',
     trigger_info         varchar(1000) null comment '触发条件信息，记录客户满足的具体条件',
     create_time          datetime     not null comment '创建时间',
     create_user          bigint       not null comment '创建人',
     update_time          datetime     null comment '更新时间',
     update_user          bigint       null comment '更新人'
) comment '客户回访任务表';

-- changeset yyy:666
-- comment 增加CRM的参数配置
INSERT INTO sys_option (category, name, code, value, default_value, description, update_user, update_time) VALUES ('CRM', '线索待跟进超时时长', 'CRM_LEAD_WAIT_OVER_HOUR', '6', '8', '取值换算成多少小时', 1, '2025-06-03 14:30:46');
INSERT INTO sys_option (category, name, code, value, default_value, description, update_user, update_time) VALUES ('CRM', '线索跟进中超时时长', 'CRM_LEAD_PENDING_OVER_HOUR', '24', '24', '取值换算成多少小时', 1, '2025-06-03 14:30:46');
INSERT INTO sys_option (category, name, code, value, default_value, description, update_user, update_time) VALUES ('CRM', '商机待跟进超时时长', 'CRM_OPPORTUNITY_WAIT_OVER_HOUR', '8', '8', '取值换算成多少小时', 1, '2025-06-03 14:30:46');
INSERT INTO sys_option (category, name, code, value, default_value, description, update_user, update_time) VALUES ('CRM', '商机跟进中超时时长', 'CRM_OPPORTUNITY_PENDING_OVER_HOUR', '24', '24', '取值换算成多少小时', 1, '2025-06-03 14:30:46');

-- changeset yyy:667
-- comment 增加回访任务去重天数参数
INSERT INTO sys_option (category, name, code, value, default_value, description, update_user, update_time) VALUES ('CRM', '回访任务去重天数', 'CRM_VISIT_TASK_DEDUP_DAYS', '30', '30', '取值为天数', null, null);

-- changeset yqx:100
-- comment 新增字段
ALTER TABLE `biz_lead`
    ADD COLUMN `company_name`        VARCHAR(255)   NULL COMMENT '公司名称',
    ADD COLUMN `customer_position`   VARCHAR(100)   NULL COMMENT '客户职位',
    ADD COLUMN `city`                VARCHAR(100)   NULL COMMENT '城市',
    ADD COLUMN `team_size`           INT            NULL COMMENT '团队规模',
    ADD COLUMN `daily_team_spending` DECIMAL(10, 2) NULL COMMENT '团队单日消耗(美元)',
    ADD COLUMN `product_name`        VARCHAR(255)   NULL COMMENT '产品名称';
ALTER TABLE `biz_customer`
    ADD COLUMN `company_name`        VARCHAR(255)   NULL COMMENT '公司名称',
    ADD COLUMN `customer_position`   VARCHAR(100)   NULL COMMENT '客户职位',
    ADD COLUMN `city`                VARCHAR(100)   NULL COMMENT '城市',
    ADD COLUMN `team_size`           INT            NULL COMMENT '团队规模',
    ADD COLUMN `daily_team_spending` DECIMAL(10, 2) NULL COMMENT '团队单日消耗(美元)',
    ADD COLUMN `product_name`        VARCHAR(255)   NULL COMMENT '产品名称';
-- changeset yqx:101
-- comment 修改字段
ALTER TABLE `biz_customer`
    MODIFY COLUMN `team_size` varchar(100) NULL DEFAULT NULL COMMENT '团队规模' AFTER `city`,
    MODIFY COLUMN `daily_team_spending` varchar(100) NULL DEFAULT NULL COMMENT '团队单日消耗(美元)' AFTER `team_size`;
ALTER TABLE `biz_lead`
    MODIFY COLUMN `team_size` varchar(100) NULL DEFAULT NULL COMMENT '团队规模' AFTER `city`,
    MODIFY COLUMN `daily_team_spending` varchar(100) NULL DEFAULT NULL COMMENT '团队单日消耗(美元)' AFTER `team_size`;


-- changeset yyy:668
-- comment 修改回访任务表，增加新字段
ALTER TABLE `biz_customer_visit_task`
    ADD COLUMN `reminder_time` datetime DEFAULT NULL COMMENT '回访提醒时间' AFTER `required_finish_time`,
    ADD COLUMN `visit_goal` varchar(2000) DEFAULT NULL COMMENT '回访目标' AFTER `reminder_time`,
    ADD COLUMN `visit_result` tinyint DEFAULT NULL COMMENT '回访结果：1-达成目标，2-无效沟通，3-客户拒绝，4-未达成目标' AFTER `visit_goal`;

-- changeset yyy:669
-- 删除原有的回访相关字段（如果存在）
ALTER TABLE `biz_customer_visit_task`
    DROP COLUMN `visit_method`,
    DROP COLUMN `visit_time`,
    DROP COLUMN `visit_summary`,
    DROP COLUMN `attachment_urls`;

-- changeset yyy:670
-- comment 创建客户回访任务明细表
CREATE TABLE `biz_customer_visit_task_detail` (
      `id` bigint NOT NULL COMMENT 'ID',
      `task_id` bigint NOT NULL COMMENT '回访任务ID',
      `visit_method` varchar(50) NOT NULL COMMENT '回访方式：电话、微信、邮件、上门拜访、会议等',
      `visit_time` datetime NOT NULL COMMENT '回访时间',
      `visit_summary` varchar(2000) COMMENT '回访纪要',
      `attachment_urls` varchar(3000) COMMENT '附件URL，多个用逗号分隔',
      `create_user` bigint DEFAULT NULL COMMENT '创建人',
      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
      `update_user` bigint DEFAULT NULL COMMENT '更新人',
      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
      PRIMARY KEY (`id`),
      KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户回访任务明细表';



-- changeset yqx:102
-- comment 新增表
CREATE TABLE `biz_sales_daily_data`
(
    `id`                  bigint         NOT NULL COMMENT '主键ID，自增',
    `lead_id`             bigint         NOT NULL DEFAULT 0 COMMENT '线索ID',
    `record_date`         date           NOT NULL COMMENT '记录添加日期，默认为当前日期',
    `add_method`          int            NOT NULL COMMENT '添加方式，1：被动 2：主动',
    `account_type`        tinyint        NOT NULL COMMENT '账号类型：telegram、wechat、line、phone',
    `customer_account`    varchar(200)   NOT NULL COMMENT '客户的账号',
    `customer_name`    varchar(200)   NOT NULL default '' COMMENT '客户名称',
    `social_account_id`   bigint not null default 0 comment '商务的社交账号ID',
    `customer_business`   varchar(255)            DEFAULT NULL COMMENT '客户的大致业务范围或描述',
    `customer_city`       varchar(50)             DEFAULT NULL COMMENT '客户所在的城市',
    `customer_overview`   varchar(5000) NULL COMMENT '对客户的初步情况进行描述',
    `first_contact_notes` varchar(5000) COMMENT '记录与客户首次沟通的内容和结果',
    `create_time`         datetime       NOT NULL COMMENT '创建时间',
    `create_user`         bigint         NOT NULL COMMENT '创建人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT = '商务每日数据';