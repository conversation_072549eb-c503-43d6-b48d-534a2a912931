--- ### 代码生成器配置
generator:
  # 排除数据表
  excludeTables:
    - DATABASECHANGELOG
    - DATABASECHANGELOGLOCK
    - gen_config
    - gen_field_config
  ## 类型映射
  typeMappings:
    MYSQL:
      String:
        - varchar
        - char
        - text
        - mediumtext
        - longtext
        - tinytext
        - json
      Integer:
        - int
        - tinyint
        - smallint
        - mediumint
        - integer
      Long:
        - bigint
      Float:
        - float
      Double:
        - double
      Boolean:
        - bit
      BigDecimal:
        - decimal
      LocalDate:
        - date
      LocalDateTime:
        - datetime
        - timestamp
    POSTGRE_SQL:
      String:
        - varchar
        - char
        - text
        - json
      Integer:
        - int2
        - int4
      Long:
        - int8
      Float:
        - float4
      Double:
        - float8
      Boolean:
        - bool
      BigDecimal:
        - decimal
      LocalDate:
        - date
      LocalDateTime:
        - timestamp
  ## 模板配置
  templateConfigs:
    DO:
      # 模板路径
      templatePath: backend/Entity.ftl
      # 包名称
      packageName: model.entity
      # 排除字段
      excludeFields:
        - id
        - createUser
        - createTime
        - updateUser
        - updateTime
    Query:
      templatePath: backend/Query.ftl
      packageName: model.query
    Req:
      templatePath: backend/Req.ftl
      packageName: model.req
    Resp:
      templatePath: backend/Resp.ftl
      packageName: model.resp
      excludeFields:
        - id
        - createUser
        - createTime
    DetailResp:
      templatePath: backend/DetailResp.ftl
      packageName: model.resp
      excludeFields:
        - id
        - createUser
        - createTime
        - updateUser
        - updateTime
    Mapper:
      templatePath: backend/Mapper.ftl
      packageName: mapper
    MapperXml:
      templatePath: backend/MapperXml.ftl
      packageName: mapper
      extension: .xml
      suffix: Mapper
    Service:
      templatePath: backend/Service.ftl
      packageName: service
    ServiceImpl:
      templatePath: backend/ServiceImpl.ftl
      packageName: service.impl
    Controller:
      templatePath: backend/Controller.ftl
      packageName: controller
    api:
      templatePath: frontend/api.ftl
      packageName: src/apis
      extension: .ts
      backend: false
    index:
      templatePath: frontend/index.ftl
      packageName: src/views
      extension: .vue
      backend: false
    AddModal:
      templatePath: frontend/AddModal.ftl
      packageName: src/views
      extension: .vue
      backend: false
    DetailDrawer:
      templatePath: frontend/DetailDrawer.ftl
      packageName: src/views
      extension: .vue
      backend: false
    Menu:
      template-path: backend/Menu.ftl
      packageName: sql
      extension: .sql
      backend: true