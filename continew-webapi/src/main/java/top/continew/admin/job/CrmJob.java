package top.continew.admin.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.service.crm.CrmCommonService;
import top.continew.admin.biz.service.crm.LeadService;
import top.continew.admin.biz.service.crm.OpportunityService;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/6/9 10:53
 */
@Component
@RequiredArgsConstructor
public class CrmJob {
    private final LeadService leadService;
    private final OpportunityService opportunityService;

    /**
     * 系统更新长期跟进
     */
    @JobExecutor(name = "updateLongTermFollowUp")
    public void updateLongTermFollowUp() {
        leadService.updateLongTermFollowUp();
        opportunityService.updateLongTermFollowUp();
    }

}
