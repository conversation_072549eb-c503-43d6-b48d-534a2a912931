/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.model.entity.PersonalAccountDO;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountSyncService;
import top.continew.admin.biz.service.CustomerDailyStatService;
import top.continew.admin.biz.service.PersonalAccountService;
import top.continew.admin.biz.utils.ThreadPoolHelper;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

@Component
@RequiredArgsConstructor
@Slf4j
public class AdAccountOrderJob {
    private final AdAccountOrderService adAccountOrderService;

    private final PersonalAccountService personalAccountService;

    private final ThreadPoolExecutor threadPoolExecutor = ThreadPoolHelper.getOrderInstance();

    private final AdAccountSyncService adAccountSyncService;

    private final CustomerDailyStatService customerDailyStatService;

    /**
     * 计算下户订单的总消耗
     */
    @JobExecutor(name = "calAccountOrderTotalSpent")
    public void calAccountOrderTotalSpent() {
        //更新授权完成的下户订单的总消耗，时间在支付时间-昨天之间
        //更新回收的下户订单的总消耗，时间在支付时间-回收时间之间
        Integer count = adAccountOrderService.calAccountOrderTotalSpent(AdAccountOrderStatusEnum.AUTH_COMPLETED);
        log.info("calAccountOrderTotalSpent-授权完成的变更数据：{}", count);

        count = adAccountOrderService.calAccountOrderTotalSpent(AdAccountOrderStatusEnum.RECYCLE);
        log.info("calAccountOrderTotalSpent-回收的变更数据：{}", count);

    }

    @JobExecutor(name = "checkPrepayBalanceJob")
    public void checkPrepayBalance() {
        adAccountOrderService.checkPrepayBalance();

    }

    @JobExecutor(name = "checkOneDollarAdJob")
    public void checkOneDollarAd() {
        List<PersonalAccountDO> observeAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
            .eq(PersonalAccountDO::getIsSync, true));
        for (PersonalAccountDO personalAccountDO : observeAccountList) {
            threadPoolExecutor.execute(() -> adAccountSyncService.getAllAdSets(null, personalAccountDO.getBrowserNo(), personalAccountDO.getProxy(), personalAccountDO.getHeaders(), true));
        }

    }

    @JobExecutor(name = "checkAdJob")
    public void checkAd() {
        adAccountOrderService.checkAdConfig();
    }

    @JobExecutor(name = "customerDailyStatJob")
    public void customerDailyStat(JobArgs jobArgs) {
        LocalDate now = LocalDate.now().minusDays(1);
        if (jobArgs.getJobParams() != null) {
            String str = (String)jobArgs.getJobParams();
            if (StringUtils.isNotBlank(str)) {
                if (str.contains("~")) {
                    LocalDate startDate = LocalDate.parse(str.substring(0, str.indexOf("~")));
                    LocalDate endDate = LocalDate.parse(str.substring(str.indexOf("~") + 1));
                    while (!startDate.isAfter(endDate)) {
                        customerDailyStatService.statData(startDate);
                        startDate = startDate.plusDays(1);
                    }
                } else {
                    now = LocalDate.parse(str);
                    customerDailyStatService.statData(now);
                }
            } else {
                customerDailyStatService.statData(now);
            }
        } else {
            customerDailyStatService.statData(now);
        }
    }
}
