package top.continew.admin.job;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.model.entity.PersonalAccountDO;
import top.continew.admin.biz.service.BusinessManagerService;
import top.continew.admin.biz.service.BusinessManagerStatisticsService;
import top.continew.admin.biz.service.PersonalAccountService;
import top.continew.admin.biz.utils.ThreadPoolHelper;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/17 17:22
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessManageJob {
    private final BusinessManagerStatisticsService businessManagerStatisticsService;

    private final BusinessManagerService businessManagerService;

    private final PersonalAccountService personalAccountService;

    private final ThreadPoolExecutor threadPoolExecutor = ThreadPoolHelper.getOrderInstance();

    @JobExecutor(name = "statBusinessManageData")
    public void statBusinessManageData(JobArgs jobArgs) {
        LocalDate date = LocalDate.now().minusDays(1);
        if (jobArgs.getJobParams() != null) {
            String params = (String)jobArgs.getJobParams();
            if (StringUtils.isNotBlank(params)) {
                date = LocalDate.parse(params);
            }
        }

        log.info("成本分析统计开始，统计日期：{}", date);
        // 统计账户备户成本
        businessManagerService.calcAdAccountCost(date);
        //统计昨天的数据
        businessManagerStatisticsService.stat(date);
    }

    @JobExecutor(name = "syncBmStatusJob")
    public void syncBmStatus() {
        List<PersonalAccountDO> observeAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
            .eq(PersonalAccountDO::getIsSync, true));
        for (PersonalAccountDO personalAccountDO : observeAccountList) {
            threadPoolExecutor.execute(() -> businessManagerService.syncData(null, personalAccountDO.getBrowserNo(), personalAccountDO.getProxy(), personalAccountDO.getHeaders()));
        }
    }
}
