package top.continew.admin.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.service.WalletTransferService;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
public class WalletJob {

    private final WalletTransferService walletTransferService;

    @JobExecutor(name = "syncWalletTransDataJob")
    public void syncWalletTransData() {
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = LocalDateTime.now().minusHours(6);
        walletTransferService.syncData(LocalDateTimeUtil.toEpochMilli(start), LocalDateTimeUtil.toEpochMilli(end));
    }
}
