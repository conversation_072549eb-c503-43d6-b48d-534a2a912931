/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.RefundOrderQuery;
import top.continew.admin.biz.model.req.RefundOrderFinishReq;
import top.continew.admin.biz.model.req.RefundOrderReq;
import top.continew.admin.biz.model.resp.RefundOrderDetailResp;
import top.continew.admin.biz.model.resp.RefundOrderResp;
import top.continew.admin.biz.service.RefundOrderService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * 退款订单管理 API
 *
 * <AUTHOR>
 * @since 2025/01/10 15:39
 */
@Tag(name = "退款订单管理 API")
@RestController
@CrudRequestMapping(value = "/biz/refundOrder", api = {Api.PAGE, Api.EXPORT})
public class RefundOrderController extends BaseController<RefundOrderService, RefundOrderResp, RefundOrderDetailResp, RefundOrderQuery, RefundOrderReq> {

    @Operation(summary = "处理订单", description = "处理订单")
    @PutMapping("/{id}/handle")
    @SaCheckPermission("biz:refundOrder:handle")
    public void handle(@PathVariable("id") Long id) {
        this.baseService.handleOrder(id);
    }

    @Operation(summary = "完成订单", description = "完成订单")
    @PutMapping("/{id}/finish")
    @SaCheckPermission("biz:refundOrder:handle")
    public void finish(@PathVariable("id") Long id, @RequestBody @Validated RefundOrderFinishReq req) {
        req.setId(id);
        this.baseService.finishOrder(req);
    }

    @Operation(summary = "取消订单", description = "取消订单")
    @PutMapping("/{id}/cancel")
    @SaCheckPermission("biz:refundOrder:cancel")
    public void cancel(@PathVariable("id") Long id) {
        this.baseService.cancelOrder(id);
    }
}