/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.hutool.core.util.ArrayUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.CardBalanceQuery;
import top.continew.admin.biz.model.req.CardBalanceReq;
import top.continew.admin.biz.model.req.CardSyncDataReq;
import top.continew.admin.biz.model.resp.CardBalanceDetailResp;
import top.continew.admin.biz.model.resp.CardBalanceResp;
import top.continew.admin.biz.service.CardBalanceService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

import java.time.LocalDateTime;

/**
 * 卡片余额流水管理 API
 *
 * <AUTHOR>
 * @since 2024/12/29 13:45
 */
@Tag(name = "卡片余额流水管理 API")
@RestController
@CrudRequestMapping(value = "/biz/cardBalance", api = {Api.PAGE, Api.EXPORT})
public class CardBalanceController extends BaseController<CardBalanceService, CardBalanceResp, CardBalanceDetailResp, CardBalanceQuery, CardBalanceReq> {

    @Operation(summary = "同步数据", description = "同步数据")
    @PostMapping("sync")
    public void syncData(@RequestBody @Validated CardSyncDataReq req) {
        this.baseService.syncData(req.getPlatform(), ArrayUtil.get(req.getTransTime(), 0), ArrayUtil.get(req.getTransTime(), 1));
    }
}