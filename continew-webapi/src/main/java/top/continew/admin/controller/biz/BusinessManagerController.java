/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.hutool.core.util.ArrayUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.query.BusinessManagerQuery;
import top.continew.admin.biz.model.req.BmItemReq;
import top.continew.admin.biz.model.req.BusinessManagerBannedReasonReq;
import top.continew.admin.biz.model.req.BusinessManagerReq;
import top.continew.admin.biz.model.resp.BusinessManagerDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerResp;
import top.continew.admin.biz.service.BusinessManagerService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * BM5账号管理 API
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
@Tag(name = "BM5账号管理 API")
@RestController
@CrudRequestMapping(value = "/biz/businessManager", api = {Api.PAGE, Api.DETAIL, Api.LIST, Api.ADD, Api.UPDATE,
        Api.DELETE, Api.EXPORT})
@RequiredArgsConstructor
public class BusinessManagerController extends BaseController<BusinessManagerService, BusinessManagerResp, BusinessManagerDetailResp, BusinessManagerQuery, BusinessManagerReq> {

    @Operation(summary = "导入数据", description = "导入数据")
    @PostMapping("/excel")
    public void exportExcel(@RequestParam("file") MultipartFile file) {

        baseService.importExcel(file);

    }

    @Operation(summary = "使用账号", description = "使用账号")
    @PutMapping("/{id}/use")
    public void used(@PathVariable("id") Long id) {
        baseService.used(id);
    }

    @Operation(summary = "封禁账号", description = "封禁账号")
    @PutMapping("/{id}/ban")
    public void ban(@PathVariable("id") Long id, @RequestBody BusinessManagerBannedReasonReq req) {
        baseService.ban(id, req.getReason());
    }

    @Operation(summary = "恢复账号", description = "恢复账号")
    @PutMapping("/{id}/normal")
    public void normal(@PathVariable("id") Long id) {
        baseService.normal(id);
    }

    @Operation(summary = "数据修复", description = "数据修复")
    @PutMapping("/test")
    public void test() {
        List<BusinessManagerDO> list = baseService.list();
        List<BusinessManagerDO> updateList = new ArrayList<>();
        for (BusinessManagerDO businessManagerDO : list) {
            String[] browsers = businessManagerDO.getBrowserNo().replace("-", ",").replace("，", ",").split(",");
            if (browsers.length > 0) {
                BusinessManagerDO update = new BusinessManagerDO();
                update.setId(businessManagerDO.getId());
                update.setOpsBrowser(browsers[0]);
                update.setReserveBrowser(ArrayUtil.get(browsers, 1));
                update.setObserveBrowser(ArrayUtil.get(browsers, 2));
                updateList.add(update);
            }
        }
        baseService.updateBatchById(updateList);
    }

}