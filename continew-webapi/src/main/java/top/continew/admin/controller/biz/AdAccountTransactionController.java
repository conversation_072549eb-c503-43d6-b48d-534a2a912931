package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.AdAccountTransactionQuery;
import top.continew.admin.biz.model.req.AdAccountTransactionReq;
import top.continew.admin.biz.model.resp.AdAccountTransactionDetailResp;
import top.continew.admin.biz.model.resp.AdAccountTransactionResp;
import top.continew.admin.biz.service.AdAccountTransactionService;

/**
 * 广告户交易记录管理 API
 *
 * <AUTHOR>
 * @since 2025/03/10 14:15
 */
@Tag(name = "广告户交易记录管理 API")
@RestController
@CrudRequestMapping(value = "/biz/adAccountTransaction", api = {Api.PAGE, Api.EXPORT})
public class AdAccountTransactionController extends BaseController<AdAccountTransactionService, AdAccountTransactionResp, AdAccountTransactionDetailResp, AdAccountTransactionQuery, AdAccountTransactionReq> {}