package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.req.AdAccountCheckAmountReq;
import top.continew.admin.biz.model.req.AdAccountDoctorCheckReq;
import top.continew.admin.biz.model.resp.AdAccountCheckAmountResp;
import top.continew.admin.biz.model.resp.AdAccountDoctorCheckResp;
import top.continew.admin.biz.service.AdAccountDoctorService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.log.annotation.Log;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/7/8 16:19
 */
@RestController
@RequestMapping("/biz/adAccountDoctor")
@RequiredArgsConstructor
public class AdAccountDoctorController {
    private final AdAccountDoctorService adAccountDoctorService;

    @Operation(summary = "广告户诊断", description = "广告户诊断")
    @Log(ignore = true)
    @PostMapping("/check")
    public List<AdAccountDoctorCheckResp> check(@Validated @RequestBody AdAccountDoctorCheckReq req) {
        return adAccountDoctorService.check(req);
    }

}
