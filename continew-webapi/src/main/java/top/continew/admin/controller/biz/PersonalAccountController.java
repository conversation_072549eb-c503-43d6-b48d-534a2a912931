package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;
import top.continew.admin.biz.model.query.PersonalAccountQuery;
import top.continew.admin.biz.model.req.PersonAccountBatchUpdateReq;
import top.continew.admin.biz.model.req.PersonalAccountReq;
import top.continew.admin.biz.model.resp.PersonalAccountDetailResp;
import top.continew.admin.biz.model.resp.PersonalAccountResp;
import top.continew.admin.biz.service.PersonalAccountService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.log.annotation.Log;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 个号管理 API
 *
 * <AUTHOR>
 * @since 2025/02/27 14:48
 */
@Tag(name = "个号管理 API")
@RestController
@CrudRequestMapping(value = "/biz/personalAccount", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class PersonalAccountController extends BaseController<PersonalAccountService, PersonalAccountResp, PersonalAccountDetailResp, PersonalAccountQuery, PersonalAccountReq> {

    @Log(ignore = true)
    @GetMapping("/channels")
    public List<LabelValueResp<String>> getChannels() {
        return baseService.getChannelList();
    }

    @Log(ignore = true)
    @Operation(summary = "导入数据", description = "导入数据")
    @PostMapping("/import")
    public void importData(@RequestParam("file") MultipartFile file,
                           @RequestParam("channelId") Long channelId,
                           @RequestParam("unitPrice") BigDecimal unitPrice,
                           @RequestParam("purchaseTime") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate purchaseTime,
                           @RequestParam("isAfterSale") Boolean isAfterSale,
                           @RequestParam("type") PersonalAccoutTypeEnum type) {
        baseService.importExcel(file, channelId, unitPrice, purchaseTime,isAfterSale,type);

    }

    @Operation(summary = "接入账号", description = "接入账号")
    @PutMapping("/{id}/access")
    public void access(@PathVariable Long id) {
        baseService.access(id);

    }
    @Operation(summary = "批量修改", description = "批量修改")
    @PostMapping("/batchUpdate")
    public void batchUpdate(@RequestBody @Validated PersonAccountBatchUpdateReq req) {
        baseService.batchUpdate(req);
    }
}