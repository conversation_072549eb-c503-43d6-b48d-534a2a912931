package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.CustomerBusinessUserQuery;
import top.continew.admin.biz.model.req.CustomerBusinessUserReq;
import top.continew.admin.biz.model.resp.CustomerBusinessUserDetailResp;
import top.continew.admin.biz.model.resp.CustomerBusinessUserResp;
import top.continew.admin.biz.service.CustomerBusinessUserService;

/**
 * 客户商务对接管理 API
 *
 * <AUTHOR>
 * @since 2025/08/05 15:35
 */
@Tag(name = "客户商务对接管理 API")
@RestController
@CrudRequestMapping(value = "/biz/customerBusinessUser", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CustomerBusinessUserController extends BaseController<CustomerBusinessUserService, CustomerBusinessUserResp, CustomerBusinessUserDetailResp, CustomerBusinessUserQuery, CustomerBusinessUserReq> {}