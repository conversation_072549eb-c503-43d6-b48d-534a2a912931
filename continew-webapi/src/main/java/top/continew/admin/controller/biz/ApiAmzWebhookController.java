package top.continew.admin.controller.biz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.katai.strategy.impl.AmzOpsStrategyImpl;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.biz.service.CardTransactionService;
import top.continew.katai.GzyClient;
import top.continew.katai.amz.model.req.AmzWebhookEvent;
import top.continew.starter.log.annotation.Log;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Map;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 16:47
 */
@Log(ignore = true)
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class ApiAmzWebhookController {
    private final CardTransactionService cardTransactionService;

    @PostMapping("/amz/webhook")
    public Map<String, String>  amzWebhook(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String payload = getBody(request);

        log.info("amz-payload:{}", payload);
        if(StrUtil.isBlank(payload)) {
            log.warn("amz-webhook-not handle, payload is null");
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return Map.of("code", "40001", "msg", "fail");
        }


        AmzWebhookEvent eventReq = JSON.parseObject(payload, AmzWebhookEvent.class);
        if(null != eventReq) {
            if(!"10000".equals(eventReq.getCode()) && !AmzOpsStrategyImpl.APP_ID.equals(eventReq.getAppId())) {
                log.warn("amz-webhook-code is fail,{}", payload);
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return Map.of("code", "20000", "msg", "fail");
            }

            cardTransactionService.handleAmzTransaction(JSON.parseArray(eventReq.getData()));
            response.setStatus(HttpServletResponse.SC_OK);
            return Map.of("code", "10000", "msg", "ok");

        }else {
            log.warn("amz-webhook-not handle, payload is null");
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return Map.of("code", "40001", "msg", "fail");
        }
    }


    private String getBody(HttpServletRequest request) {
        // 读取请求体
        String payload = "";
        try {
            StringBuilder sb = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            payload = sb.toString();
            if (StrUtil.isBlank(payload)) {
                log.warn("AMZ webhook-请求体为空");
            }
        } catch (IOException e) {
            log.error("AMZ webhook-读取请求体失败", e);
        }
        return payload;
    }
}
