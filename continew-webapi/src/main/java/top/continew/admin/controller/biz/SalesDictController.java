package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.system.model.query.DictItemQuery;
import top.continew.admin.system.model.req.DictItemReq;
import top.continew.admin.system.model.resp.DictResp;
import top.continew.admin.system.model.resp.DictItemResp;
import top.continew.admin.system.service.DictService;
import top.continew.admin.system.service.DictItemService;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.log.annotation.Log;

import java.util.Collections;
import java.util.List;
import java.util.Arrays;

/**
 * 销售字典管理 API
 */
@Log(module = "销售字典管理")
@Tag(name = "销售字典管理 API")
@RestController
@RequestMapping("/biz/sales-dict")
@RequiredArgsConstructor
public class SalesDictController {
    
    private final DictService dictService;
    private final DictItemService dictItemService;
    
    // 销售相关的字典编码列表
    private static final List<String> SALES_DICT_CODES = Arrays.asList(
        "social_account_type", "customer_industry", "lead_invalid_reason", "opportunity_lost_reason", "customer_sales_tag", "customer_visit_task_result"
    );
    
    /**
     * 获取销售相关的字典列表
     */
    @GetMapping("/list")
    public List<DictResp> listSalesDict() {
        return dictService.listByDictCodes(SALES_DICT_CODES);
    }
    
    /**
     * 分页查询字典项
     */
    @GetMapping("/item")
    public BasePageResp<DictItemResp> pageDictItem(DictItemQuery query, PageQuery pageQuery) {
        // 验证dictId是否在允许的范围内
        if (query.getDictId() != null) {
            validateDictId(query.getDictId());
        }
        return dictItemService.page(query, pageQuery);
    }
    
    /**
     * 获取字典项详情
     */
    @GetMapping("/item/{id}")
    public DictItemResp getDictItem(@PathVariable Long id) {
        DictItemResp item = dictItemService.get(id);
        // 验证是否是销售相关的字典项
        validateDictId(item.getDictId());
        return item;
    }
    
    /**
     * 新增字典项
     */
    @PostMapping("/item")
    public void addDictItem(@RequestBody DictItemReq req) {
        // 验证是否是销售相关的字典
        validateDictId(req.getDictId());
        dictItemService.add(req);
    }
    
    /**
     * 修改字典项
     */
    @PutMapping("/item/{id}")
    public void updateDictItem(@RequestBody DictItemReq req, @PathVariable Long id) {
        // 验证是否是销售相关的字典项
        DictItemResp item = dictItemService.get(id);
        validateDictId(item.getDictId());
        dictItemService.update(req, id);
    }
    
    /**
     * 删除字典项
     */
    @DeleteMapping("/item/{id}")
    public void deleteDictItem(@PathVariable Long id) {
        // 验证是否是销售相关的字典项
        DictItemResp item = dictItemService.get(id);
        validateDictId(item.getDictId());
        dictItemService.delete(Collections.singletonList(id));
    }
    
    /**
     * 验证字典ID是否在销售相关的字典范围内
     */
    private void validateDictId(Long dictId) {
        DictResp dict = dictService.get(dictId);
        if (dict == null || !SALES_DICT_CODES.contains(dict.getCode())) {
            throw new IllegalArgumentException("无权操作该字典");
        }
    }
}