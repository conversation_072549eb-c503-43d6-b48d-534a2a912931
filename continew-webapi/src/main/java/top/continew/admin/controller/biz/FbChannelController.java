/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.FbChannelQuery;
import top.continew.admin.biz.model.req.FbChannelReq;
import top.continew.admin.biz.model.resp.FbChannelDetailResp;
import top.continew.admin.biz.model.resp.FbChannelResp;
import top.continew.admin.biz.service.FbChannelService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * FB账号渠道管理 API
 *
 * <AUTHOR>
 * @since 2025/01/06 11:37
 */
@Tag(name = "FB账号渠道管理 API")
@RestController
@CrudRequestMapping(value = "/biz/fbChannel", api = {Api.PAGE, Api.DETAIL, Api.LIST, Api.ADD, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
public class FbChannelController extends BaseController<FbChannelService, FbChannelResp, FbChannelDetailResp, FbChannelQuery, FbChannelReq> {

}