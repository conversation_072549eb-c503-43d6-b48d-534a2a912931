/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.RechargeOrderQuery;
import top.continew.admin.biz.model.req.RechargeOrderFinishReq;
import top.continew.admin.biz.model.req.RechargeOrderRechargeReq;
import top.continew.admin.biz.model.req.RechargeOrderReq;
import top.continew.admin.biz.model.resp.RechargeOrderDetailResp;
import top.continew.admin.biz.model.resp.RechargeOrderResp;
import top.continew.admin.biz.service.RechargeOrderService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * 充值订单管理 API
 *
 * <AUTHOR>
 * @since 2024/12/30 17:09
 */
@Tag(name = "充值订单管理 API")
@RestController
@CrudRequestMapping(value = "/biz/rechargeOrder", api = {Api.PAGE, Api.EXPORT})
public class RechargeOrderController extends BaseController<RechargeOrderService, RechargeOrderResp, RechargeOrderDetailResp, RechargeOrderQuery, RechargeOrderReq> {

    @Operation(summary = "处理订单", description = "处理订单")
    @PutMapping("/{id}/handle")
    @SaCheckPermission("biz:rechargeOrder:handle")
    public void handle(@PathVariable("id") Long id) {
        this.baseService.handleOrder(id);
    }

    @Operation(summary = "完成订单", description = "完成订单")
    @PutMapping("/{id}/finish")
    @SaCheckPermission("biz:rechargeOrder:handle")
    public void finish(@PathVariable("id") Long id, @RequestBody @Validated RechargeOrderFinishReq req) {
        req.setId(id);
        this.baseService.finishOrder(req);
    }

    @Operation(summary = "取消订单", description = "取消订单")
    @PutMapping("/{id}/cancel")
    @SaCheckPermission("biz:rechargeOrder:cancel")
    public void cancel(@PathVariable("id") Long id) {
        this.baseService.cancelOrder(id);
    }

    @Operation(summary = "卡台充值", description = "卡台充值")
    @PutMapping("/{id}/recharge")
    @SaCheckPermission("biz:rechargeOrder:handle")
    public void recharge(@PathVariable("id") Long id, @RequestBody @Validated RechargeOrderRechargeReq req) {
        req.setId(id);
        this.baseService.recharge(req);
    }

    @Operation(summary = "生成凭证", description = "生成凭证")
    @PutMapping("/{id}/certificate")
    @SaCheckPermission("biz:rechargeOrder:handle")
    public void generate(@PathVariable("id") Long id) {
        this.baseService.generateCertificate(id);
    }
}