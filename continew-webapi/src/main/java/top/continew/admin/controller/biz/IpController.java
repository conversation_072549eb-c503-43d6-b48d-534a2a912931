/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.multipart.MultipartFile;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.IpQuery;
import top.continew.admin.biz.model.req.IpReq;
import top.continew.admin.biz.model.resp.IpDetailResp;
import top.continew.admin.biz.model.resp.IpResp;
import top.continew.admin.biz.service.IpService;

/**
 * IP库管理 API
 *
 * <AUTHOR>
 * @since 2025/01/20 13:46
 */
@Tag(name = "IP库管理 API")
@RestController
@CrudRequestMapping(value = "/biz/ip", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class IpController extends BaseController<IpService, IpResp, IpDetailResp, IpQuery, IpReq> {

    @Operation(summary = "导入数据", description = "导入数据")
    @PostMapping("/importIp")
    public void importIp(@RequestParam("file") MultipartFile file, @RequestParam("country") String country,@RequestParam("expireDay") Integer expireDay) {

        baseService.importIp(file, country,expireDay);
    }
}