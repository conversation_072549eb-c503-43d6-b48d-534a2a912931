/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.starter.cache.redisson.util.RedisUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.log.annotation.Log;

import static top.continew.admin.common.constant.CacheConstants.BROWSER_ACTIVE_KEY_PREFIX;

/**
 * 浏览器管理 API
 *
 * <AUTHOR>
 * @since 2025/01/20 13:46
 */
@Log(ignore = true)
@Slf4j
@Tag(name = "浏览器管理 API")
@RestController
@RequestMapping(value = "/biz/browser")
@RequiredArgsConstructor
public class BrowserController {

    @GetMapping("/open")
    public void open(String serialNumber, Long id) {
        if (StringUtils.isBlank(serialNumber)) {
            throw new BusinessException("浏览器编号不能为空");
        }
        serialNumber = serialNumber.trim();
        String username = UserContextHolder.getUsername();
        log.info("用户{}正在尝试打开浏览器：{}...", username, serialNumber);
        String value = RedisUtils.get(BROWSER_ACTIVE_KEY_PREFIX + serialNumber);
        if (StringUtils.isBlank(value)) {
            return;
        }
        log.info("浏览器{}已被{}使用", serialNumber, value);
        if (value.equals(username)) {
            return;
        }
        throw new BusinessException("浏览器%s已被%s使用".formatted(serialNumber, value));
    }
}