package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.PurchaseReceiveOrderQuery;
import top.continew.admin.biz.model.req.PurchaseReceiveOrderReq;
import top.continew.admin.biz.model.resp.PurchaseReceiveOrderDetailResp;
import top.continew.admin.biz.model.resp.PurchaseReceiveOrderResp;
import top.continew.admin.biz.service.PurchaseReceiveOrderService;

/**
 * 采购验收单管理 API
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Tag(name = "采购验收单管理 API")
@RestController
@CrudRequestMapping(value = "/biz/purchaseReceiveOrder", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class PurchaseReceiveOrderController extends BaseController<PurchaseReceiveOrderService, PurchaseReceiveOrderResp, PurchaseReceiveOrderDetailResp, PurchaseReceiveOrderQuery, PurchaseReceiveOrderReq> {}