package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.CustomerDailyStatQuery;
import top.continew.admin.biz.model.req.CustomerDailyStatReq;
import top.continew.admin.biz.model.resp.CustomerDailyStatByDateResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatDetailResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatResp;
import top.continew.admin.biz.service.CustomerDailyStatService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 客户每日统计管理 API
 *
 * <AUTHOR>
 * @since 2025/07/17 10:11
 */
@Tag(name = "客户每日统计管理 API")
@RestController
@CrudRequestMapping(value = "/biz/customerDailyStat", api = {Api.PAGE, Api.EXPORT})
public class CustomerDailyStatController extends BaseController<CustomerDailyStatService, CustomerDailyStatResp, CustomerDailyStatDetailResp, CustomerDailyStatQuery, CustomerDailyStatReq> {

    @Log(ignore = true)
    @GetMapping("/date/page")
    public PageResp<CustomerDailyStatByDateResp> selectDailyStatByDatePage(CustomerDailyStatQuery query,
                                                                           PageQuery pageQuery) {
        return this.baseService.selectDailyPage(query, pageQuery);
    }

    @Log(ignore = true)
    @GetMapping("/date/export")
    public void exportDailyStatByDate(CustomerDailyStatQuery query, HttpServletResponse response) {
        List<CustomerDailyStatByDateResp> resp = this.baseService.selectDailyList(query);
        ExcelUtils.export(resp, "每日下户分析", CustomerDailyStatByDateResp.class, response);
    }
}