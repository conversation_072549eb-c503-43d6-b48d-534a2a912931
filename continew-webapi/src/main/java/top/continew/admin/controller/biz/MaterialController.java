package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.MaterialQuery;
import top.continew.admin.biz.model.query.MaterialStatQuery;
import top.continew.admin.biz.model.req.MaterialReq;
import top.continew.admin.biz.model.resp.MaterialDetailResp;
import top.continew.admin.biz.model.resp.MaterialResp;
import top.continew.admin.biz.model.resp.MaterialStatByDateResp;
import top.continew.admin.biz.model.resp.MaterialStatByTypeResp;
import top.continew.admin.biz.service.MaterialService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 物料管理 API
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
@Tag(name = "物料管理 API")
@RestController
@CrudRequestMapping(value = "/biz/material", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class MaterialController extends BaseController<MaterialService, MaterialResp, MaterialDetailResp, MaterialQuery, MaterialReq> {

    @Log(ignore = true)
    @GetMapping("stat/date")
    public PageResp<MaterialStatByDateResp> statByDate(MaterialStatQuery query, PageQuery pageQuery) {
        return this.baseService.selectStatByDatePage(query, pageQuery);
    }

    @Log(ignore = true)
    @GetMapping("stat/date/export")
    public void statByDateExport(MaterialStatQuery query, HttpServletResponse response) {
        List<MaterialStatByDateResp> list = this.baseService.selectStatByDateList(query);
        ExcelUtils.export(list, "导出数据", MaterialStatByDateResp.class, response);
    }

    @Log(ignore = true)
    @GetMapping("stat/type")
    public List<MaterialStatByTypeResp> statByType(MaterialStatQuery query, String[] sort) {
        return this.baseService.selectStatByTypeList(query, sort);
    }

    @Log(ignore = true)
    @GetMapping("stat/type/export")
    public void statByTypeExport(MaterialStatQuery query, String[] sort, HttpServletResponse response) {
        List<MaterialStatByTypeResp> list = this.baseService.selectStatByTypeList(query, sort);
        ExcelUtils.export(list, "导出数据", MaterialStatByTypeResp.class, response);
    }
}