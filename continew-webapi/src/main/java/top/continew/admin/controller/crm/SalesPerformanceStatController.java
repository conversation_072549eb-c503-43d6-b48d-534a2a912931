package top.continew.admin.controller.crm;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.query.PerformanceStatisticsQuery;
import top.continew.admin.biz.model.query.crm.SalesPerformanceStatQuery;
import top.continew.admin.biz.model.resp.BusinessPerformanceStatisticsResp;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;
import top.continew.admin.biz.service.crm.SalesPerformanceStatService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 商务业绩统计 API
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "商务业绩统计 API")
@RestController
@RequestMapping(value = "/biz/salesPerformanceStat")
@RequiredArgsConstructor
public class SalesPerformanceStatController {

    private final SalesPerformanceStatService salesPerformanceStatService;

    @PostMapping("/list")
    @Operation(summary = "查询商务业绩统计", description = "根据条件查询商务业绩统计数据")
    @Log(ignore = true)
    public List<SalesPerformanceStatResp> list(@RequestBody @Valid SalesPerformanceStatQuery query) {
        return salesPerformanceStatService.listSalesPerformanceStat(query);
    }


    @GetMapping("/list")
    @SaIgnore
    @Operation(summary = "查询商务业绩统计", description = "根据条件查询商务业绩统计数据")
    public List<BusinessPerformanceStatisticsResp> list(@ParameterObject @Validated PerformanceStatisticsQuery query) {

        return salesPerformanceStatService.listBusinessPerformanceStat(query);
    }

}