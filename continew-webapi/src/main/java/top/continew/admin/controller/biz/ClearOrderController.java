/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.query.ClearAccountAnalyzeQuery;
import top.continew.admin.biz.model.query.ClearOrderQuery;
import top.continew.admin.biz.model.req.ClearOrderFinishReq;
import top.continew.admin.biz.model.req.ClearOrderReq;
import top.continew.admin.biz.model.resp.ClearAccountAnalyzeResp;
import top.continew.admin.biz.model.resp.ClearOrderDetailResp;
import top.continew.admin.biz.model.resp.ClearOrderResp;
import top.continew.admin.biz.service.ClearOrderService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.resp.PageResp;

/**
 * 清零订单管理 API
 *
 * <AUTHOR>
 * @since 2024/12/31 11:17
 */
@Tag(name = "清零订单管理 API")
@RestController
@CrudRequestMapping(value = "/biz/clearOrder", api = {Api.PAGE, Api.ADD, Api.EXPORT})
public class ClearOrderController extends BaseController<ClearOrderService, ClearOrderResp, ClearOrderDetailResp, ClearOrderQuery, ClearOrderReq> {

    @Operation(summary = "处理订单", description = "处理订单")
    @PutMapping("/{id}/handle")
    @SaCheckPermission("biz:clearOrder:handle")
    public void handle(@PathVariable("id") Long id) {
        this.baseService.handleOrder(id);
    }

    @Operation(summary = "完成订单", description = "完成订单")
    @PutMapping("/{id}/finish")
    @SaCheckPermission("biz:clearOrder:handle")
    public void finish(@PathVariable("id") Long id, @RequestBody @Validated ClearOrderFinishReq req) {
        req.setId(id);
        this.baseService.finishOrder(req);
    }

    @Operation(summary = "取消订单", description = "取消订单")
    @PutMapping("/{id}/cancel")
    @SaCheckPermission("biz:clearOrder:cancel")
    public void cancel(@PathVariable("id") Long id) {
        this.baseService.cancelOrder(id);
    }

    @Operation(summary = "生成凭证", description = "生成凭证")
    @PutMapping("/{id}/certificate")
    @SaCheckPermission("biz:clearOrder:handle")
    public void generate(@PathVariable("id") Long id) {
        this.baseService.generateCertificate(id);
    }

    @Operation(summary = "清零账户分析", description = "清零账户分析")
    @PostMapping("/clearAccountAnalyze")
    @SaCheckPermission("biz:clearOrder:accountAnalyze")
    public PageResp<ClearAccountAnalyzeResp> clearAccountAnalyze(@RequestBody @Validated ClearAccountAnalyzeQuery query) {
        return this.baseService.getClearAccountAnalyze(query);
    }
}