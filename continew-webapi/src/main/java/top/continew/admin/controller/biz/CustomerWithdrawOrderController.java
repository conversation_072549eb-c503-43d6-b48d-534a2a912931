package top.continew.admin.controller.biz;

import jakarta.ws.rs.Path;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import top.continew.admin.biz.model.resp.CustomerDetailResp;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.CustomerWithdrawOrderQuery;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderReq;
import top.continew.admin.biz.model.resp.CustomerWithdrawOrderDetailResp;
import top.continew.admin.biz.model.resp.CustomerWithdrawOrderResp;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderAuditReq;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderRefundReq;
import top.continew.admin.biz.service.CustomerWithdrawOrderService;

import java.math.BigDecimal;

/**
 * 客户余额提现订单管理 API
 *
 * <AUTHOR>
 * @since 2025/01/22 14:23
 */
@Tag(name = "客户余额提现订单管理 API")
@RestController
@RequiredArgsConstructor
@CrudRequestMapping(value = "/biz/customerWithdrawOrder", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CustomerWithdrawOrderController extends BaseController<CustomerWithdrawOrderService, CustomerWithdrawOrderResp, CustomerWithdrawOrderDetailResp, CustomerWithdrawOrderQuery, CustomerWithdrawOrderReq> {
    private final CustomerService customerService;

    @PostMapping("/audit")
    public void audit(@Validated @RequestBody CustomerWithdrawOrderAuditReq req) {
        this.baseService.audit(req);
    }

    @PostMapping("/refund")
    public void refund(@Validated @RequestBody CustomerWithdrawOrderRefundReq req) {
        this.baseService.refund(req);
    }

    @PostMapping("/pre_refund")
    public BigDecimal preRefund(@RequestParam Long customerId) {
        CustomerDetailResp customer = customerService.get(customerId);
        if (customer == null) {
            throw new BusinessException("客户不存在");
        }
        return customer.getBalance();
    }
}