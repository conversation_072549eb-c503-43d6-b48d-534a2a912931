/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.ExcelTemplateEnum;
import top.continew.admin.biz.model.query.FbAccountQuery;
import top.continew.admin.biz.model.req.FbAccountBatchUpdateRemarkReq;
import top.continew.admin.biz.model.req.FbAccountBatchUpdateStatusReq;
import top.continew.admin.biz.model.req.FbAccountBatchVerifyReq;
import top.continew.admin.biz.model.req.FbAccountReq;
import top.continew.admin.biz.model.resp.FbAccountDetailResp;
import top.continew.admin.biz.model.resp.FbAccountResp;
import top.continew.admin.biz.service.FbAccountService;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.system.enums.MessageTemplateEnum;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

import java.math.BigDecimal;
import java.util.List;

/**
 * fb账号管理 API
 *
 * <AUTHOR>
 * @since 2025/01/03 16:32
 */
@Tag(name = "fb账号管理 API")
@RestController
@CrudRequestMapping(value = "/biz/fbAccount", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
@RequiredArgsConstructor
public class FbAccountController extends BaseController<FbAccountService, FbAccountResp, FbAccountDetailResp, FbAccountQuery, FbAccountReq> {

    private final FbAccountService fbAccountService;


    @Operation(summary = "导入数据", description = "导入数据")
    @PostMapping("/excel")
    public List<String> exportExcel(@RequestParam("file") MultipartFile file,
                                    @RequestParam("channelId") Long channelId,
                                    @RequestParam("tag") String tag,
                                    @RequestParam("systemIp") Integer systemIp,
                                    @RequestParam("price") BigDecimal price,
                                    @RequestParam("type") ExcelTemplateEnum type) {

        if (systemIp != null && systemIp == 1) {
            return fbAccountService.importExcelBySystemIp(file, channelId, tag, price,type);
        } else {
            return fbAccountService.importExcelByCustomIp(file, channelId, tag, price,type);
        }
    }

    @Operation(summary = "批量修改状态", description = "批量修改状态")
    @PostMapping("/batchUpdateStatus")
    public void batchUpdateStatus(@Validated @RequestBody FbAccountBatchUpdateStatusReq req) {
        fbAccountService.batchUpdateStatus(req);
    }

    @Operation(summary = "批量验活", description = "批量验活")
    @PostMapping("/batchVerify")
    public void batchVerify(@Validated @RequestBody FbAccountBatchVerifyReq req) {
        fbAccountService.batchVerify(req);
    }


    @Operation(summary = "批量修改备注", description = "批量修改备注")
    @PostMapping("/batchUpdateRemark")
    public void batchUpdateRemark(@Validated @RequestBody FbAccountBatchUpdateRemarkReq req) {
        fbAccountService.batchUpdateRemark(req);
    }
}