/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.AdAccountBalanceRecordQuery;
import top.continew.admin.biz.model.req.AdAccountBalanceRecordReq;
import top.continew.admin.biz.model.resp.AdAccountBalanceRecordDetailResp;
import top.continew.admin.biz.model.resp.AdAccountBalanceRecordResp;
import top.continew.admin.biz.service.AdAccountBalanceRecordService;

/**
 * 广告户余额变更记录管理 API
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
@Tag(name = "广告户余额变更记录管理 API")
@RestController
@CrudRequestMapping(value = "/biz/adAccountBalanceRecord", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
public class AdAccountBalanceRecordController extends BaseController<AdAccountBalanceRecordService, AdAccountBalanceRecordResp, AdAccountBalanceRecordDetailResp, AdAccountBalanceRecordQuery, AdAccountBalanceRecordReq> {}