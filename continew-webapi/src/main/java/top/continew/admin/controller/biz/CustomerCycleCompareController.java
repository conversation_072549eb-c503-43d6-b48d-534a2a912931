package top.continew.admin.controller.biz;

import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.query.CustomerCycleComparePeriodQuery;
import top.continew.admin.biz.model.query.CustomerCycleCompareSummaryQuery;
import top.continew.admin.biz.model.query.CustomerQuery;
import top.continew.admin.biz.model.resp.CustomerCycleComparePeroidResp;
import top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp;
import top.continew.admin.biz.model.resp.CustomerResp;
import top.continew.admin.biz.service.CustomerCycleCompareService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.log.annotation.Log;

import java.util.List;
import java.util.Map;

/**
 * 客户周期对比 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/biz/customer-cycle-compare")
public class CustomerCycleCompareController {
    @Resource
    private CustomerCycleCompareService customerCycleCompareService;
    @Resource
    private CustomerService customerService;


    /**
     * 分页获取客户基础数据
     *
     * @param query 查询条件
     * @return 客户基础数据
     */
    @GetMapping("/page")
    @Log(ignore = true)
    public BasePageResp<CustomerResp> pageCustomerBaseData(CustomerQuery query, @Validated PageQuery pageQuery) {
        return customerService.page(query, pageQuery);
    }

    /**
     * 分批获取客户的汇总统计数据
     *
     * @param query 查询条件
     * @return 汇总统计数据
     */
    @GetMapping("/summary")
    @Log(ignore = true)
    public List<CustomerCycleCompareSummaryResp> summary(CustomerCycleCompareSummaryQuery query) {
        return customerCycleCompareService.listCustomerSummaryData(query);
    }
    
    /**
     * 分批获取客户的周期统计数据
     *
     * @param query 查询条件
     * @return 客户周期对比汇总数据
     */
    @PostMapping("/period")
    @Log(ignore = true)
    public Map<String, List<CustomerCycleComparePeroidResp>> getCustomerCycleSummary(@RequestBody CustomerCycleComparePeriodQuery query) {
        return customerCycleCompareService.listCustomerPeriodData(query);
    }
}