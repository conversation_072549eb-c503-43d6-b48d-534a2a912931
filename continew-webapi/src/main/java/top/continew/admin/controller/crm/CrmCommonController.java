package top.continew.admin.controller.crm;

import cn.dev33.satoken.stp.StpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.resp.crm.CrmWorkbenchResp;
import top.continew.admin.biz.service.crm.CrmCommonService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.log.annotation.Log;
import top.continew.starter.web.model.R;

/**
 * CRM工作台控制器
 *
 * @version: 1.00.00
 * @description:
 * @date: 2025/6/3 14:43
 */
@Tag(name = "CRM工作台 API")
@RestController
@CrudRequestMapping(value = "/biz/crm")
@RequiredArgsConstructor
public class CrmCommonController {

    private final CrmCommonService crmCommonService;

    @Log(ignore = true)
    @Operation(summary = "获取CRM工作台统计数据")
    @GetMapping("/workbench/stats")
    public CrmWorkbenchResp getWorkbenchStats() {
        // 提前判断权限
        boolean showLeadCount = StpUtil.hasPermission("biz:lead:add");
        boolean showOpportunityCount = StpUtil.hasPermission("biz:opportunity:add");
        boolean showVisitTaskCount = StpUtil.hasPermission("biz:customerVisitTask:add");

        // 如果都没有权限，直接返回空数据
        if (!showLeadCount && !showOpportunityCount && !showVisitTaskCount) {
            CrmWorkbenchResp resp = new CrmWorkbenchResp();
            resp.setShowLeadCount(false);
            resp.setShowOpportunityCount(false);
            resp.setShowVisitTaskCount(false);
            return resp;
        }
        
        // 根据权限获取对应的统计数据
        CrmWorkbenchResp resp = crmCommonService.getCrmWorkbenchStats(showLeadCount, showOpportunityCount, showVisitTaskCount);
        resp.setShowLeadCount(showLeadCount);
        resp.setShowVisitTaskCount(showVisitTaskCount);
        resp.setShowOpportunityCount(showOpportunityCount);
        
        return resp;
    }
}
