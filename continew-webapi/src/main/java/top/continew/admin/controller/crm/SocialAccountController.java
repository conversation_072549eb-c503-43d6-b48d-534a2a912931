package top.continew.admin.controller.crm;

import jakarta.validation.Valid;
import top.continew.admin.biz.model.req.crm.SocialAccountReq;
import top.continew.admin.biz.model.req.crm.SocialAccountUpdateStatusReq;
import top.continew.admin.biz.model.req.crm.SocialAccountUpdateAssigneeReq;
import top.continew.admin.biz.model.resp.crm.SocialAccountDetailResp;
import top.continew.admin.biz.model.resp.crm.SocialAccountResp;
import top.continew.admin.biz.service.crm.SocialAccountService;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.crm.SocialAccountQuery;


/**
 * 社交账号管理 API
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Tag(name = "社交账号管理 API")
@RestController
@CrudRequestMapping(value = "/biz/socialAccount", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class SocialAccountController extends BaseController<SocialAccountService, SocialAccountResp, SocialAccountDetailResp, SocialAccountQuery, SocialAccountReq> {

    @Operation(summary = "批量修改账号状态")
    @PutMapping("/batch/status")
    public void batchUpdateStatus(@RequestBody @Valid SocialAccountUpdateStatusReq req) {
        baseService.batchUpdateStatus(req);
    }

    @Operation(summary = "批量修改分配人")
    @PutMapping("/batch/assignee")
    public void batchUpdateAssignee(@RequestBody @Valid SocialAccountUpdateAssigneeReq req) {
        baseService.batchUpdateAssignee(req);
    }
}