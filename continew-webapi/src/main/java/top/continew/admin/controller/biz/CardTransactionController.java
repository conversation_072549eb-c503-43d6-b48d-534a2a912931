/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.query.CardTransactionQuery;
import top.continew.admin.biz.model.query.CardTransactionStatisticsQuery;
import top.continew.admin.biz.model.req.CardSyncDataReq;
import top.continew.admin.biz.model.req.CardTransactionRemarkReq;
import top.continew.admin.biz.model.req.CardTransactionReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.CardTransactionService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡片交易流水管理 API
 *
 * <AUTHOR>
 * @since 2024/12/31 21:57
 */
@Tag(name = "卡片交易流水管理 API")
@RestController
@CrudRequestMapping(value = "/biz/cardTransaction", api = {Api.PAGE, Api.EXPORT})
public class CardTransactionController extends BaseController<CardTransactionService, CardTransactionResp, CardTransactionDetailResp, CardTransactionQuery, CardTransactionReq> {

    @Operation(summary = "同步数据", description = "同步数据")
    @PostMapping("sync")
    public void syncData(@RequestBody @Validated CardSyncDataReq req) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (req.getTransTime() != null && req.getTransTime().length == 2) {
            start = req.getTransTime()[0];
            end = req.getTransTime()[1];
        }
        this.baseService.syncData(req.getPlatform(), start, end);
    }

    @Operation(summary = "备注", description = "备注")
    @PutMapping("{id}/remark")
    public void remark(@PathVariable("id") Long id, @RequestBody @Validated CardTransactionRemarkReq req) {
        this.baseService.remark(id, req);
    }

    @Log(ignore = true)
    @GetMapping("stat/summary")
    public CardTransactionSummaryResp statistics(CardTransactionStatisticsQuery query) {
        return this.baseService.statistics(query);
    }

    @Log(ignore = true)
    @GetMapping("stat/cardholder")
    public PageResp<CardTransactionStatByCardholderResp> statisticsByCardholder(CardTransactionStatisticsQuery query,
                                                                                PageQuery pageQuery) {
        return this.baseService.selectStatByCardholderPage(query, pageQuery);
    }

    @Log(ignore = true)
    @GetMapping("stat/date")
    public PageResp<CardTransactionStatByDateResp> statisticsByDate(CardTransactionStatisticsQuery query,
                                                                    PageQuery pageQuery) {
        return this.baseService.selectStatByDatePage(query, pageQuery);
    }

    @Log(ignore = true)
    @GetMapping("stat/date/export")
    public void exportStatisticsByDate(CardTransactionStatisticsQuery query, HttpServletResponse response) {
        List<CardTransactionStatByDateResp> list = this.baseService.selectStatByDateList(query);
        ExcelUtils.export(list, "导出数据", CardTransactionStatByDateResp.class, response);
    }

    @Log(ignore = true)
    @GetMapping("stat/timezone")
    public PageResp<CardTransactionStatByTimezoneResp> statisticsTimezone(CardTransactionStatisticsQuery query,
                                                                          PageQuery pageQuery) {
        return this.baseService.selectStatByTimezonePage(query, pageQuery);
    }
}