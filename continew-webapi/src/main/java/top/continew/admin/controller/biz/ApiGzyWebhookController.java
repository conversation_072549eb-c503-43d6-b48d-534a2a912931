package top.continew.admin.controller.biz;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.biz.service.CardTransactionService;
import top.continew.katai.GzyClient;
import top.continew.starter.log.annotation.Log;

import java.io.BufferedReader;
import java.io.IOException;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 16:47
 */
@Log(ignore = true)
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class ApiGzyWebhookController {
    private final CardTransactionService cardTransactionService;
    private final CardService cardService;



    @PostMapping("/gzy/webhook")
    @ResponseBody
    public void gzyWebhook(HttpServletRequest request, HttpServletResponse response) throws IOException {
        //通知种类
        String category = request.getHeader("X-PD-NOTIFICATION-CATAGORY");
        //通知子类
        //不是交易流水、卡信息通知，直接返回
        if(!"issuing".equals(category) && !"issuing_card".equals(category)) {
            response.setStatus(HttpServletResponse.SC_OK);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"roger\":true}");
            return;
        }

        String sign = request.getHeader("X-PD-SIGN");
        String payload = getBody(request);

        //使用 RSA 公钥进行验签
        boolean verified = GzyClient.checkSign(sign, payload);
        if (!verified) {
            log.error("光子易webhook-签名验证失败，sign:{}", sign);
            response.setStatus(HttpServletResponse.SC_OK);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":403,\"msg\":\"sign error\"}");
            return;
        }

        //异常处理业务
        if("issuing".equals(category)) {
            asyncHandleTransaction(payload);
        }else if("issuing_card".equals(category)) {
            asyncHandleCard(payload);
        }else {
            log.info("光子易webhook-非发卡交易通知，忽略，category:{}", category);
        }

        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"roger\":true}");
    }


    private void asyncHandleTransaction(String payload) {
        try {
            JSONArray arr = parseTransaction(payload);
            if(!arr.isEmpty()) {
                cardTransactionService.handleGzyTransaction(arr);
            }

        } catch (Exception e) {
            log.error("交易通知处理失败", e);
        }
    }

    private void asyncHandleCard(String payload) {
        try {
            //TODO
        } catch (Exception e) {
            log.error("交易通知处理失败", e);
        }
    }


    private JSONArray parseTransaction(String payload) {
        JSONArray jsonArray = new JSONArray();
        if (payload != null && !payload.trim().isEmpty()) {
            try {
                // 去除 BOM 和特殊字符
                payload = payload.trim().replaceAll("[\t\r]", "");
                jsonArray.add(JSON.parseObject(payload));

            } catch (JSONException e) {
                log.error("交易通知-JSON 解析失败: " + e.getMessage());
            }
        } else {
            log.error("交易通知-payload 为空，无法解析！");
        }

        return jsonArray;
    }

    private String getBody(HttpServletRequest request) {
        // 读取请求体
        String payload = "";
        try {
            StringBuilder sb = new StringBuilder();
            String line;
            BufferedReader reader = request.getReader();
            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\n"); // 添加换行符，保持原始格式
            }
            // 移除最后一个多余的换行符
            if (sb.length() > 0) {
                sb.setLength(sb.length() - 1);
            }
            payload = sb.toString();
        } catch (IOException e) {
            log.error("光子易webhook-读取请求体失败", e);
        }

        return payload;
    }

}
