/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.AppealOrderQuery;
import top.continew.admin.biz.model.req.AppealOrderFinishReq;
import top.continew.admin.biz.model.req.AppealOrderReq;
import top.continew.admin.biz.model.req.AppealOrderUpdateRemarkReq;
import top.continew.admin.biz.model.resp.AppealOrderDetailResp;
import top.continew.admin.biz.model.resp.AppealOrderResp;
import top.continew.admin.biz.service.AppealOrderService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * 申诉订单管理 API
 *
 * <AUTHOR>
 * @since 2025/01/16 11:24
 */
@Tag(name = "申诉订单管理 API")
@RestController
@CrudRequestMapping(value = "/biz/appealOrder", api = {Api.PAGE, Api.ADD, Api.EXPORT})
public class AppealOrderController extends BaseController<AppealOrderService, AppealOrderResp, AppealOrderDetailResp, AppealOrderQuery, AppealOrderReq> {

    @Operation(summary = "处理订单", description = "处理订单")
    @PutMapping("/{id}/handle")
    @SaCheckPermission("biz:appealOrder:handle")
    public void handle(@PathVariable("id") Long id) {
        this.baseService.handleOrder(id);
    }

    @Operation(summary = "完成订单", description = "完成订单")
    @PutMapping("/{id}/finish")
    @SaCheckPermission("biz:appealOrder:handle")
    public void finish(@PathVariable("id") Long id, @RequestBody @Validated AppealOrderFinishReq req) {
        this.baseService.finishOrder(id, req);
    }

    @Operation(summary = "取消订单", description = "取消订单")
    @PutMapping("/{id}/cancel")
    @SaCheckPermission("biz:appealOrder:handle")
    public void cancel(@PathVariable("id") Long id) {
        this.baseService.cancelOrder(id);
    }

    @Operation(summary = "提交清零", description = "提交清零")
    @PutMapping("/{id}/clear")
    @SaCheckPermission("biz:appealOrder:handle")
    public void applyClear(@PathVariable("id") Long id) {
        this.baseService.applyClear(id);
    }

    @Operation(summary = "修改备注", description = "修改备注")
    @PutMapping("/remark")
    @SaCheckPermission("biz:appealOrder:handle")
    public void updateRemark(@RequestBody @Validated AppealOrderUpdateRemarkReq req) {
        this.baseService.updateRemark(req);
    }
}