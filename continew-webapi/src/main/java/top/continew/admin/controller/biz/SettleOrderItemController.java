package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.SettleOrderItemQuery;
import top.continew.admin.biz.model.req.SettleOrderItemReq;
import top.continew.admin.biz.model.resp.SettleOrderItemDetailResp;
import top.continew.admin.biz.model.resp.SettleOrderItemResp;
import top.continew.admin.biz.service.SettleOrderItemService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * 结算订单详情管理 API
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Tag(name = "结算订单详情管理 API")
@RestController
@CrudRequestMapping(value = "/biz/settleOrderItem", api = {Api.PAGE, Api.EXPORT})
public class SettleOrderItemController extends BaseController<SettleOrderItemService, SettleOrderItemResp, SettleOrderItemDetailResp, SettleOrderItemQuery, SettleOrderItemReq> {}