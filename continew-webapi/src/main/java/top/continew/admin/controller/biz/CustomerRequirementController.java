package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import top.continew.admin.biz.model.query.CustomerQuestionQuery;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.CustomerRequirementQuery;
import top.continew.admin.biz.model.req.CustomerRequirementReq;
import top.continew.admin.biz.model.resp.CustomerRequirementDetailResp;
import top.continew.admin.biz.model.resp.CustomerRequirementResp;
import top.continew.admin.biz.service.CustomerRequirementService;
import top.continew.starter.log.annotation.Log;
import top.continew.starter.web.model.R;

import java.util.List;
import java.util.Map;

/**
 * 客户需求管理 API
 *
 * <AUTHOR>
 * @since 2025/02/20 14:03
 */
@Tag(name = "客户需求管理 API")
@RestController
@CrudRequestMapping(value = "/biz/customerRequirement", api = {Api.PAGE, Api.ADD, Api.DETAIL, Api.UPDATE, Api.DELETE})
public class CustomerRequirementController extends BaseController<CustomerRequirementService, CustomerRequirementResp, CustomerRequirementDetailResp, CustomerRequirementQuery, CustomerRequirementReq> {
    @Log(ignore = true)
    @GetMapping("/summary")
    public R<List<Map<String, Object>>> getSummary(CustomerRequirementQuery query) {
        return R.ok(baseService.getSummary(query));
    }

    @PostMapping("/accept")
    @Operation(summary = "批量接单客户需求")
    public R<Void> accept(@RequestBody List<Long> ids) {
        baseService.accept(ids);
        return R.ok();
    }

    @PostMapping("/finish/{id}")
    @Operation(summary = "完成客户需求")
    public R<Void> finish(@PathVariable Long id, @RequestParam Integer finishQuantity) {
        baseService.finish(id, finishQuantity);
        return R.ok();
    }

    @PostMapping("/cancel/{id}")
    @Operation(summary = "取消客户需求")
    public R<Void> cancel(@PathVariable Long id, @RequestParam(required = false) String cancelReason) {
        baseService.cancel(id, cancelReason);
        return R.ok();
    }

    @PostMapping("/wait/{id}")
    @Operation(summary = "客户需求变更为待处理")
    public R<Void> wait(@PathVariable Long id) {
        baseService.wait(id);
        return R.ok();
    }

    @PostMapping("/invited/{id}")
    @Operation(summary = "客户需求变更为已邀请")
    public R<Void> invited(@PathVariable Long id) {
        baseService.invited(id);
        return R.ok();
    }

    @GetMapping("/getRequiredNum")
    public Integer getRequiredNum(CustomerRequirementQuery query) {
        return baseService.getRequiredNum(query);
    }


}