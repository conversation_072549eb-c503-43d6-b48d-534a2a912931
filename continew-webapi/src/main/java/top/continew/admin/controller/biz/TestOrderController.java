package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.validation.annotation.Validated;
import top.continew.admin.biz.model.entity.TestOrderDO;
import top.continew.admin.biz.model.req.AdAccountTestOrderReq;
import top.continew.admin.biz.model.req.RechargeOrderFinishReq;
import top.continew.admin.biz.model.req.TestOrderStatusReq;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.TestOrderQuery;
import top.continew.admin.biz.model.req.TestOrderReq;
import top.continew.admin.biz.model.resp.TestOrderDetailResp;
import top.continew.admin.biz.model.resp.TestOrderResp;
import top.continew.admin.biz.service.TestOrderService;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 测试任务管理 API
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@SaIgnore
@Tag(name = "测试任务管理 API")
@RestController
@CrudRequestMapping(value = "/biz/testOrder", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT,Api.LIST})
public class TestOrderController extends BaseController<TestOrderService, TestOrderResp, TestOrderDetailResp, TestOrderQuery, TestOrderReq> {

    @PutMapping("/{id}/status")
    public void status(@PathVariable("id") Long id, @RequestBody @Validated TestOrderStatusReq req) {
        baseService.updateStatus(id,req);
    }

    @PostMapping("/addOrder")
    public void addOrder(@RequestBody @Validated AdAccountTestOrderReq req) {
        baseService.addOrder(req);
    }



}