/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.CustomerProfitStatQuery;
import top.continew.admin.biz.model.resp.CustomerProfitStatResp;
import top.continew.admin.biz.service.CustomerProfitService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.io.IOException;
import java.util.List;

@Log(ignore = true)
@Tag(name = "客户盈利 API")
@RestController
@RequestMapping("/biz/customer/profit")
@RequiredArgsConstructor
@Slf4j
public class CustomerProfitController {
    private final CustomerProfitService customerProfitService;

    @Operation(summary = "分页查询客户统计")
    @GetMapping("/page")
    public PageResp<CustomerProfitStatResp> page(CustomerProfitStatQuery query, PageQuery pageQuery) {
        return customerProfitService.selectCustomerProfitStatPage(query, pageQuery);
    }

    @Operation(summary = "导出客户统计")
    @GetMapping("/export")
    public void export(CustomerProfitStatQuery query, SortQuery sortQuery, HttpServletResponse response) throws IOException {
        List<CustomerProfitStatResp> list = customerProfitService.selectCustomerProfitStatList(query, sortQuery);
        ExcelUtils.export(list, "客户盈利分析", CustomerProfitStatResp.class, response);
    }
}