package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.BusinessManagerBanStatQuery;
import top.continew.admin.biz.model.query.BusinessManagerChannelStatQuery;
import top.continew.admin.biz.model.query.BusinessManagerStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerStatisticsQuery;
import top.continew.admin.biz.model.req.BusinessManagerStatisticsReq;
import top.continew.admin.biz.model.req.SpendStatisticsReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.BusinessManagerStatisticsService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 成本分析管理 API
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Log(ignore = true)
@Tag(name = "成本分析管理 API")
@RestController
@CrudRequestMapping(value = "/biz/businessManagerStatistics", api = {Api.PAGE, Api.EXPORT})
public class BusinessManagerStatisticsController extends BaseController<BusinessManagerStatisticsService, BusinessManagerStatisticsResp, BusinessManagerStatisticsDetailResp, BusinessManagerStatisticsQuery, BusinessManagerStatisticsReq> {

    @GetMapping("/spend/summary")
    @Operation(summary = "获取消耗统计汇总数据")
    public SpendStatisticsSummaryResp getSpendSummary(@Valid SpendStatisticsReq req) {
        return baseService.getSpendSummary(req);
    }

    @GetMapping("/spend/daily")
    @Operation(summary = "获取每日消耗记录")
    public List<DailySpendResp> getDailySpend(@Valid SpendStatisticsReq req) {
        return baseService.getDailySpend(req);
    }

    @GetMapping("summary")
    public BusinessManagerStatisticsSummaryResp getSummary(BusinessManagerStatisticsQuery req) {
        return baseService.getSummary(req);
    }


    @Operation(summary = "分页查询BM渠道分析")
    @GetMapping("/channelStat")
    public PageResp<BusinessManagerChannelStatDataResp> page(@Valid BusinessManagerChannelStatQuery query) {
        return baseService.statChannelData(query);
    }

    @Operation(summary = "分页查询BM封禁分析")
    @GetMapping("/banStat")
    public PageResp<BusinessManagerBanStatDataResp> banStat(@Valid BusinessManagerBanStatQuery query) {
        return baseService.statBanData(query);
    }
}