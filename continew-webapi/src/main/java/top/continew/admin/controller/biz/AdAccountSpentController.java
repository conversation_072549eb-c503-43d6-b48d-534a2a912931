package top.continew.admin.controller.biz;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.AdAccountSpentQuery;
import top.continew.admin.biz.model.query.CustomerAnalyzeQuery;
import top.continew.admin.biz.model.resp.CustomerSpentResp;
import top.continew.admin.biz.model.resp.CustomerSpentStatisticsResp;
import top.continew.admin.biz.service.AdAccountSpentService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.log.annotation.Log;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "广告户消耗分析API")
@RestController
@RequestMapping("/biz/adAccountSpent")
@RequiredArgsConstructor
@Log(ignore = true)
public class AdAccountSpentController {

    private final AdAccountSpentService adAccountSpentService;


    @GetMapping("/page")
    @Operation(summary = "分页查询列表", description = "分页查询列表")
    public BasePageResp<CustomerSpentResp> page(AdAccountSpentQuery query, PageQuery pageQuery) {
        check(query);
        if (query.getType() == 1) {
            return adAccountSpentService.pageByCustomer(query, pageQuery);
        } else if (query.getType() == 2) {
            return adAccountSpentService.pageByAdAccount(query, pageQuery);
        } else {
            return null;
        }
    }


    @GetMapping("/statistics")
    @Operation(summary = "统计数据", description = "统计数据")
    public CustomerSpentStatisticsResp statistics(AdAccountSpentQuery query) {
        check(query);
        if (query.getType() == 1) {
            return adAccountSpentService.getCustomerDashboardStats(query);
        } else if (query.getType() == 2) {
            return adAccountSpentService.getAdAccountDashboardStats(query);
        } else {
            return null;
        }
    }


    @GetMapping("/analyze")
    @Operation(summary = "分析影响因素", description = "分析影响因素")
    public List<CustomerSpentResp> analyze(CustomerAnalyzeQuery query) {
        if (query.getType() == 1) {
            return adAccountSpentService.analyzeByCustomer(query);
        } else if (query.getType() == 2) {
            return adAccountSpentService.analyzeByAdAccount(query);
        } else {
            return List.of();
        }
    }

    @GetMapping("/timeInterval")
    public LocalDateTime[] timeInterval(String adAccountIds){
        return adAccountSpentService.timeInterval(adAccountIds);
    }

    private static void check(AdAccountSpentQuery query) {
        if (query.getLowConsumptionDay() == null || query.getLowConsumptionDay() <= 0) {
            query.setLowConsumptionDay(1);
        }
        if (query.getLowConsumptionAmount() == null || query.getLowConsumptionAmount().compareTo(BigDecimal.ZERO) <= 0) {
            query.setLowConsumptionAmount(BigDecimal.valueOf(10));
        }
    }
}
