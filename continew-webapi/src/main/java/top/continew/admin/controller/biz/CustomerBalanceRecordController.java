/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.CustomerBalanceRecordQuery;
import top.continew.admin.biz.model.req.CustomerBalanceRecordCertificateUploadReq;
import top.continew.admin.biz.model.req.CustomerBalanceRecordReq;
import top.continew.admin.biz.model.req.CustomerBalanceRecordWalletTransIdUploadReq;
import top.continew.admin.biz.model.resp.CustomerBalanceRecordDetailResp;
import top.continew.admin.biz.model.resp.CustomerBalanceRecordResp;
import top.continew.admin.biz.service.CustomerBalanceRecordService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * 客户余额变更记录管理 API
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
@Tag(name = "客户余额变更记录管理 API")
@RestController
@CrudRequestMapping(value = "/biz/customerBalanceRecord", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
public class CustomerBalanceRecordController extends BaseController<CustomerBalanceRecordService, CustomerBalanceRecordResp, CustomerBalanceRecordDetailResp, CustomerBalanceRecordQuery, CustomerBalanceRecordReq> {

    @Operation(summary = "上传水单", description = "上传水单")
    @PutMapping("/{id}/certificate")
    public void uploadCertificate(@PathVariable("id") String id,
                                  @RequestBody @Validated CustomerBalanceRecordCertificateUploadReq req) {
        this.baseService.uploadCertificate(id, req.getCertificate());

    }

    @Operation(summary = "上传钱包交易号", description = "上传钱包交易号")
    @PutMapping("/{id}/walletTransId")
    public void uploadCertificate(@PathVariable("id") Long id,
                                  @RequestBody @Validated CustomerBalanceRecordWalletTransIdUploadReq req) {
        this.baseService.bindWalletTransId(id, req.getTransactionId());

    }
}