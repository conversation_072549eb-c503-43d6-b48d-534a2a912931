package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.WalletTransferQuery;
import top.continew.admin.biz.model.req.WalletTransferReq;
import top.continew.admin.biz.model.resp.WalletTransferDetailResp;
import top.continew.admin.biz.model.resp.WalletTransferResp;
import top.continew.admin.biz.service.WalletTransferService;
import top.continew.starter.log.annotation.Log;

import java.math.BigDecimal;

/**
 * 钱包流水管理 API
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
@Tag(name = "钱包流水管理 API")
@RestController
@CrudRequestMapping(value = "/biz/walletTransfer", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class WalletTransferController extends BaseController<WalletTransferService, WalletTransferResp, WalletTransferDetailResp, WalletTransferQuery, WalletTransferReq> {

    @Log(ignore = true)
    @GetMapping("balance")
    public BigDecimal getCurrentBalance() {
        return this.baseService.getCurrentBalance();
    }
}