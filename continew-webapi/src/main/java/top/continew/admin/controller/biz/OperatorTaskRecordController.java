package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.OperatorTaskRecordQuery;
import top.continew.admin.biz.model.req.OperatorTaskRecordReq;
import top.continew.admin.biz.model.resp.OperatorTaskRecordDetailResp;
import top.continew.admin.biz.model.resp.OperatorTaskRecordResp;
import top.continew.admin.biz.service.OperatorTaskRecordService;

/**
 * 运营人员工作记录管理 API
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Tag(name = "运营人员工作记录管理 API")
@RestController
@CrudRequestMapping(value = "/biz/operatorTaskRecord", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class OperatorTaskRecordController extends BaseController<OperatorTaskRecordService, OperatorTaskRecordResp, OperatorTaskRecordDetailResp, OperatorTaskRecordQuery, OperatorTaskRecordReq> {}