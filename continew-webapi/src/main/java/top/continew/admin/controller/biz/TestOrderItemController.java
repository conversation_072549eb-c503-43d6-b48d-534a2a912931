package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.query.TestOrderItemQuery;
import top.continew.admin.biz.model.req.TestOrderItemReq;
import top.continew.admin.biz.model.resp.AdAccountTestOrderItemResp;
import top.continew.admin.biz.model.resp.TestOrderItemDetailResp;
import top.continew.admin.biz.model.resp.TestOrderItemResp;
import top.continew.admin.biz.service.TestOrderItemService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

import java.util.List;

/**
 * 测试任务详情管理 API
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@SaIgnore
@Tag(name = "测试任务详情管理 API")
@RestController
@CrudRequestMapping(value = "/biz/testOrderItem", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class TestOrderItemController extends BaseController<TestOrderItemService, TestOrderItemResp, TestOrderItemDetailResp, TestOrderItemQuery, TestOrderItemReq> {

    @PostMapping("/batchUpdate")
    public void batchUpdate(@RequestBody TestOrderItemReq req) {
        baseService.batchUpdate(req);
    }

    @GetMapping("/{adAccountId}/list")
    public List<AdAccountTestOrderItemResp> list(@PathVariable String adAccountId) {

        return baseService.listByAdAccountId(adAccountId);
    }
}