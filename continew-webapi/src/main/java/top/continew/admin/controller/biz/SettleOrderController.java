package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.SettleOrderQuery;
import top.continew.admin.biz.model.req.SettleOrderReq;
import top.continew.admin.biz.model.resp.SettleOrderDetailResp;
import top.continew.admin.biz.model.resp.SettleOrderResp;
import top.continew.admin.biz.service.SettleOrderService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * 结算订单管理 API
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Tag(name = "结算订单管理 API")
@RestController
@CrudRequestMapping(value = "/biz/settleOrder", api = {Api.PAGE, Api.ADD, Api.EXPORT})
public class SettleOrderController extends BaseController<SettleOrderService, SettleOrderResp, SettleOrderDetailResp, SettleOrderQuery, SettleOrderReq> {
}