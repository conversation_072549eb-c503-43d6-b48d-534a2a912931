package top.continew.admin.controller.crm;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import top.continew.admin.biz.model.query.crm.OpportunityFollowQuery;
import top.continew.admin.biz.model.query.crm.OpportunityQuery;
import top.continew.admin.biz.model.req.crm.*;
import top.continew.admin.biz.model.resp.crm.*;
import top.continew.admin.biz.service.crm.OpportunityService;
import top.continew.admin.common.base.BaseController;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.log.annotation.Log;

/**
 * 商机管理 API
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Tag(name = "商机管理 API")
@RestController
@CrudRequestMapping(value = "/biz/opportunity")
public class OpportunityController extends BaseController<OpportunityService, OpportunityResp, OpportunityDetailResp, OpportunityQuery, OpportunityReq> {
    
    @PostMapping("/follow")
    @Operation(summary = "添加商机跟进记录")
    public void addFollow(@RequestBody @Valid OpportunityFollowReq req) {
        baseService.addFollow(req);
    }

    @PutMapping("/follow")
    @Operation(summary = "修改商机跟进记录")
    public void updateFollow(@RequestBody @Valid OpportunityFollowUpdateReq req) {
        baseService.updateFollow(req);
    }

    @DeleteMapping("/follow/{id}")
    @Operation(summary = "删除商机跟进记录")
    public void deleteFollow(@PathVariable Long id) {
        baseService.deleteFollow(id);
    }

    @GetMapping("/follow/page")
    @Operation(summary = "分页获取商机跟进记录")
    @Log(ignore = true)
    public PageResp<OpportunityFollowResp> pageFollow(@Valid OpportunityFollowQuery query) {
        return baseService.pageFollow(query);
    }

    @PostMapping("/batch-update-handler")
    @Operation(summary = "批量修改商机对接人")
    public void batchUpdateHandler(@RequestBody @Valid OpportunityUpdateHandlerReq req) {
        baseService.batchUpdateHandler(req);
    }
}