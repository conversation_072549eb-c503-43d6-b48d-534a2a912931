package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.query.PurchaseOrderQuery;
import top.continew.admin.biz.model.req.IdsReq;
import top.continew.admin.biz.model.req.PurchaseOrderPayReq;
import top.continew.admin.biz.model.req.PurchaseOrderReceiveReq;
import top.continew.admin.biz.model.req.PurchaseOrderReq;
import top.continew.admin.biz.model.resp.PurchaseOrderCheckResp;
import top.continew.admin.biz.model.resp.PurchaseOrderDetailResp;
import top.continew.admin.biz.model.resp.PurchaseOrderResp;
import top.continew.admin.biz.service.PurchaseOrderService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 采购订单管理 API
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Tag(name = "采购订单管理 API")
@RestController
@CrudRequestMapping(value = "/biz/purchaseOrder", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
public class PurchaseOrderController extends BaseController<PurchaseOrderService, PurchaseOrderResp, PurchaseOrderDetailResp, PurchaseOrderQuery, PurchaseOrderReq> {

    @Operation(summary = "采购订单付款", description = "采购订单付款")
    @PostMapping("pay")
    public void pay(@RequestBody PurchaseOrderPayReq req) {
        this.baseService.batchPay(req);
    }

    @Operation(summary = "采购订单完成", description = "采购订单完成")
    @PostMapping("finish")
    public void finish(@RequestBody IdsReq reqs) {
        this.baseService.batchFinish(reqs);
    }

    @Log(ignore = true)
    @GetMapping("check")
    public List<PurchaseOrderCheckResp> check(PurchaseOrderQuery query) {
        return this.baseService.check(query.getCreateTime());
    }

    @Operation(summary = "采购订单验收", description = "采购订单验收")
    @PostMapping("receive")
    public void receive(@RequestBody @Validated PurchaseOrderReceiveReq req) {
        this.baseService.receive(req);
    }
}