/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.model.query.AdAccountInsightQuery;
import top.continew.admin.biz.model.query.AdAccountRetentionQuery;
import top.continew.admin.biz.model.query.AdAccountSalesStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerStatReportQuery;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.FinanceStatService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.util.Arrays;
import java.util.List;

/**
 * 广告账号管理 API
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
@Log(ignore = true)
@Tag(name = "财务管理 API")
@RestController
@CrudRequestMapping(value = "/biz/finance")
@RequiredArgsConstructor
public class FinanceController {

    private final FinanceStatService financeStatService;

    private final CustomerService customerService;

    private final AdAccountOrderService adAccountOrderService;

    @Operation(summary = "导入数据", description = "导入数据")
    @PostMapping("/customer/{id}/report/import")
    public void importData(@PathVariable("id") Long id, @RequestParam("file") MultipartFile file) {
        financeStatService.importDailyReport(id, file);
    }

    @Operation(summary = "查询客户报告", description = "查询客户报告")
    @GetMapping("/customer/stat")
    public BasePageResp<CustomerStatReportResp> listCustomerStatReport(CustomerStatReportQuery query,
                                                                       PageQuery pageQuery) {
        return customerService.pageCustomerStatReport(query, pageQuery);
    }

    @Operation(summary = "导出客户报告", description = "导出客户报告")
    @GetMapping("/customer/stat/export")
    public void exportCustomerStatReport(CustomerStatReportQuery query,
                                         PageQuery sortQuery,
                                         HttpServletResponse response) {
        List<CustomerStatReportResp> list = customerService.listCustomerStatReport(query, sortQuery);
        ExcelUtils.export(list, "导出数据", CustomerStatReportResp.class, response);
    }

    @Operation(summary = "查询客户报告汇总", description = "查询客户报告汇总")
    @GetMapping("/customer/stat/summary")
    public CustomerStatSummaryResp listCustomerStatReportSummary(CustomerStatReportQuery query) {
        return customerService.getCustomerStatReportSummary(query);
    }

    @Operation(summary = "查询客户每日报告", description = "查询客户每日报告")
    @GetMapping("/customer/{id}/daily")
    public BasePageResp<CustomerDailyStatReportResp> listCustomerDailyStatReport(@PathVariable("id") Long id,
                                                                                 CustomerStatReportQuery query,
                                                                                 PageQuery pageQuery) {
        query.setCustomerId(id);
        return customerService.pageCustomerDailyStatReport(query, pageQuery);
    }

    @Operation(summary = "导出客户每日报告", description = "导出客户每日报告")
    @GetMapping("/customer/{id}/daily/export")
    public void exportCustomerDailyStatReport(@PathVariable("id") Long id,
                                              CustomerStatReportQuery query,
                                              PageQuery sortQuery,
                                              HttpServletResponse response) {
        query.setCustomerId(id);
        List<CustomerDailyStatReportResp> list = customerService.listCustomerDailyStatReport(query, sortQuery);
        ExcelUtils.export(list, "导出数据", CustomerDailyStatReportResp.class, response);
    }

    @Operation(summary = "查询广告户报告", description = "查询广告户报告")
    @GetMapping("/adAccount/stat")
    public BasePageResp<AdAccountStatResp> listAdAccountStatReport(AdAccountInsightQuery query, PageQuery pageQuery) {
        return financeStatService.pageAdAccountStatReport(query, pageQuery);
    }

    @Operation(summary = "查询广告户每日报告", description = "查询广告户每日报告")
    @GetMapping("/adAccount/{id}/daily")
    public BasePageResp<AdAccountDailyStatReportResp> listAdAccountDailyStatReport(@PathVariable("id") String id,
                                                                                   AdAccountInsightQuery query,
                                                                                   PageQuery pageQuery) {
        query.setAdAccountIds(Arrays.asList(id));
        return financeStatService.pageAdAccountDailyStatReport(query, pageQuery);
    }

    @Operation(summary = "导出广告户报告", description = "导出广告户报告")
    @GetMapping("/adAccount/stat/export")
    public void exportAdAccountStatReport(AdAccountInsightQuery query, HttpServletResponse response) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(50000);
        BasePageResp<AdAccountStatResp> page = financeStatService.pageAdAccountStatReport(query, pageQuery);
        String customerName = "";
        if (query.getCustomerId() != null) {
            customerName = customerService.getById(query.getCustomerId()).getName();
        }
        ExcelUtils.export(page.getList(), customerName + "广告户报告", AdAccountStatResp.class, response);
    }

    @Operation(summary = "导出广告户每日报告", description = "导出广告户每日报告")
    @GetMapping("/adAccount/{id}/daily/export")
    public void exportAdAccountDailyStatReport(@PathVariable("id") String id,
                                               AdAccountInsightQuery query,
                                               HttpServletResponse response) {
        query.setAdAccountIds(Arrays.asList(id));

        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(50000);
        BasePageResp<AdAccountDailyStatReportResp> page = financeStatService.pageAdAccountDailyStatReport(query, pageQuery);
        page.getList().forEach(row -> row.setAdAccountId(id));
        ExcelUtils.export(page.getList(), "广告户每日报告", AdAccountDailyStatReportResp.class, response);
    }

    @GetMapping("/adAccount/salesStatistics")
    public BasePageResp<AdAccountSalesStatisticsResp> listAdAccountSalesStatistics(AdAccountSalesStatisticsQuery query,
                                                                                   PageQuery pageQuery) {
        return adAccountOrderService.pageAdAccountSalesStatistics(query, pageQuery);
    }

    @GetMapping("/adAccount/salesStatistics/summary")
    public AdAccountSalesStatisticsResp getAdAccountSalesStatisticsSummary(AdAccountSalesStatisticsQuery query) {
        return adAccountOrderService.getAdAccountSalesStatisticsSummary(query);
    }

    @GetMapping("/adAccount/salesStatistics/export")
    public void exportAdAccountSalesStatistics(AdAccountSalesStatisticsQuery query, HttpServletResponse response) {
        List<AdAccountSalesStatisticsResp> list = adAccountOrderService.listAdAccountSalesStatistics(query);
        ExcelUtils.export(list, "导出数据", AdAccountSalesStatisticsResp.class, response);
    }

    @GetMapping("/daily")
    public PageResp<DailyStatReportResp> selectDailyStatReportPage(CustomerStatReportQuery query, PageQuery pageQuery) {
        return financeStatService.selectDailyStatReportPage(query, pageQuery);
    }

    @GetMapping("/daily/export")
    public void exportDailyStatReport(CustomerStatReportQuery query, HttpServletResponse response) {
        List<DailyStatReportResp> list = financeStatService.selectDailyStatReportList(query);
        ExcelUtils.export(list, "导出数据", DailyStatReportResp.class, response);
    }

    @Operation(summary = "查询广告户留存", description = "查询广告户留存")
    @GetMapping("/adAccount/retention")
    public BasePageResp<AdAccountRetentionResp> listAdAccountRetention(AdAccountRetentionQuery query,
                                                                       PageQuery pageQuery) {
        return financeStatService.pageAdAccountRetention(query, pageQuery);
    }

    @Operation(summary = "导出广告户留存", description = "导出广告户留存")
    @GetMapping("/adAccount/retention/export")
    public void exportAdAccountRetention(AdAccountRetentionQuery query, HttpServletResponse response) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(50000);
        BasePageResp<AdAccountRetentionResp> resp = financeStatService.pageAdAccountRetention(query, pageQuery);
        ExcelUtils.export(resp.getList(), "广告户留存分析", AdAccountRetentionResp.class, response);
    }
}