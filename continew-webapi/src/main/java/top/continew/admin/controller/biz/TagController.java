package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import org.springframework.validation.annotation.Validated;
import top.continew.admin.biz.model.req.DeleteTagReq;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.TagQuery;
import top.continew.admin.biz.model.req.TagReq;
import top.continew.admin.biz.model.resp.TagDetailResp;
import top.continew.admin.biz.model.resp.TagResp;
import top.continew.admin.biz.service.TagService;

/**
 * 标签管理 API
 *
 * <AUTHOR>
 * @since 2025/05/07 18:02
 */
@SaIgnore
@Tag(name = "标签管理 API")
@RestController
@CrudRequestMapping(value = "/biz/tag", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class TagController extends BaseController<TagService, TagResp, TagDetailResp, TagQuery, TagReq> {



    @PostMapping("delete")
    public void delete(@RequestBody @Validated DeleteTagReq req) {

        baseService.deleteTag(req);

    }


}