package top.continew.admin.controller.crm;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.admin.biz.model.query.crm.LeadQuery;
import top.continew.admin.biz.model.query.crm.LeadFollowQuery;
import top.continew.admin.biz.model.req.crm.LeadReq;
import top.continew.admin.biz.model.req.crm.LeadFollowReq;
import top.continew.admin.biz.model.req.crm.LeadFollowUpdateReq;
import top.continew.admin.biz.model.req.crm.LeadUpdateHandlerReq;
import top.continew.admin.biz.model.resp.crm.LeadDetailResp;
import top.continew.admin.biz.model.resp.crm.LeadResp;
import top.continew.admin.biz.model.resp.crm.LeadFollowResp;
import top.continew.admin.biz.service.crm.LeadService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;

import jakarta.validation.Valid;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.log.annotation.Log;

/**
 * 线索管理 API
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Tag(name = "线索管理 API")
@RestController
@CrudRequestMapping(value = "/biz/lead")
public class LeadController extends BaseController<LeadService, LeadResp, LeadDetailResp, LeadQuery, LeadReq> {
    
    @PostMapping("/follow")
    @Operation(summary = "添加线索跟进记录")
    public void addFollow(@RequestBody @Valid LeadFollowReq req) {
        baseService.addFollow(req);
    }
    
    @PutMapping("/follow")
    @Operation(summary = "修改线索跟进记录")
    public void updateFollow(@RequestBody @Valid LeadFollowUpdateReq req) {
        baseService.updateFollow(req);
    }
    
    @DeleteMapping("/follow/{id}")
    @Operation(summary = "删除线索跟进记录")
    public void deleteFollow(@PathVariable Long id) {
        baseService.deleteFollow(id);
    }
    
    @GetMapping("/follow/page")
    @Operation(summary = "分页获取线索跟进记录")
    @Log(ignore = true)
    public PageResp<LeadFollowResp> pageFollow(@Valid LeadFollowQuery query) {
        return baseService.pageFollow(query);
    }
    
    @PostMapping("/batch-update-handler")
    @Operation(summary = "批量修改线索对接人")
    public void batchUpdateHandler(@RequestBody @Valid LeadUpdateHandlerReq req) {
        baseService.batchUpdateHandler(req);
    }
}