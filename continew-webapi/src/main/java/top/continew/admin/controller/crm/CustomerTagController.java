package top.continew.admin.controller.crm;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import top.continew.admin.biz.model.req.crm.CustomerTagAddReq;
import top.continew.admin.biz.model.req.crm.CustomerTagQueryReq;
import top.continew.admin.biz.model.resp.crm.CustomerTagResp;
import top.continew.admin.biz.service.crm.CustomerTagService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.log.annotation.Log;

import java.util.List;
import java.util.Map;

/**
 * 客户标签管理 API
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Tag(name = "客户标签管理 API")
@RestController
@RequiredArgsConstructor
@CrudRequestMapping(value = "/biz/customer/tag")
public class CustomerTagController {
    
    private final CustomerTagService customerTagService;
    
    @Operation(summary = "添加客户标签", description = "添加客户标签")
    @PostMapping("/add")
    public void addCustomerTag(@RequestBody @Valid CustomerTagAddReq req) {
        customerTagService.addCustomerTag(req);
    }
    
    @Operation(summary = "删除客户标签", description = "删除客户标签")
    @DeleteMapping("/{customerId}/{tagId}")
    public void deleteCustomerTag(@PathVariable Long customerId, @PathVariable Long tagId) {
        customerTagService.deleteCustomerTag(customerId, tagId);
    }

    @Log(ignore = true)
    @Operation(summary = "获取客户标签列表", description = "获取客户标签列表")
    @GetMapping("/list/{customerId}")
    public List<CustomerTagResp> getCustomerTagList(@PathVariable Long customerId) {
        return customerTagService.getCustomerTagList(customerId);
    }
    
    @Log(ignore = true)
    @Operation(summary = "批量获取客户标签列表", description = "批量获取客户标签列表")
    @PostMapping("/batchList")
    public Map<Long, List<CustomerTagResp>> batchGetCustomerTagList(@RequestBody CustomerTagQueryReq req) {
        return customerTagService.batchGetCustomerTagList(req.getCustomerIds());
    }
}