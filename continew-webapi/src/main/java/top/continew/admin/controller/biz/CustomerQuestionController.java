package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.CustomerQuestionQuery;
import top.continew.admin.biz.model.req.CustomerQuestionReq;
import top.continew.admin.biz.model.resp.CustomerQuestionDetailResp;
import top.continew.admin.biz.model.resp.CustomerQuestionResp;
import top.continew.admin.biz.service.CustomerQuestionService;

/**
 * 客户问题管理 API
 *
 * <AUTHOR>
 * @since 2025/04/30 14:35
 */
@Tag(name = "客户问题管理 API")
@RestController
@CrudRequestMapping(value = "/biz/customerQuestion", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CustomerQuestionController extends BaseController<CustomerQuestionService, CustomerQuestionResp, CustomerQuestionDetailResp, CustomerQuestionQuery, CustomerQuestionReq> {

    @GetMapping("/getQuestionNum")
    public Integer getQuestionNum(CustomerQuestionQuery query) {
        return baseService.getQuestionNum(query);
    }
}