package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.CustomerWalletQuery;
import top.continew.admin.biz.model.req.CustomerWalletReq;
import top.continew.admin.biz.model.resp.CustomerWalletDetailResp;
import top.continew.admin.biz.model.resp.CustomerWalletResp;
import top.continew.admin.biz.service.CustomerWalletService;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 客户钱包管理 API
 *
 * <AUTHOR>
 * @since 2025/07/22 16:58
 */
@Tag(name = "客户钱包管理 API")
@RestController
@CrudRequestMapping(value = "/biz/customerWallet", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CustomerWalletController extends BaseController<CustomerWalletService, CustomerWalletResp, CustomerWalletDetailResp, CustomerWalletQuery, CustomerWalletReq> {

    @Log(ignore = true)
    @GetMapping("list")
    public List<CustomerWalletResp> list(CustomerWalletQuery query, SortQuery sortQuery) {
        return this.baseService.list(query, sortQuery);
    }
}