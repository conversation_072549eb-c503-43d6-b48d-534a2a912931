/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.query.BusinessStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerSpentTrendQuery;
import top.continew.admin.biz.model.query.CustomerStatisticsQuery;
import top.continew.admin.biz.model.resp.BusinessStatisticsResp;
import top.continew.admin.biz.model.resp.CustomerSpentTrendResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsOverviewResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsResp;
import top.continew.admin.biz.service.CustomerStatisticsService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Log(ignore = true)
@Tag(name = "客户统计 API")
@RestController
@RequestMapping("/biz/customer/statistics")
@RequiredArgsConstructor
@Slf4j
public class CustomerStatisticsController {
    private final CustomerStatisticsService customerStatisticsService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Operation(summary = "分页查询客户统计")
    @GetMapping("/page")
    @SaCheckPermission("customer:statistics:show")
    public PageResp<CustomerStatisticsResp> page(CustomerStatisticsQuery query) {
        return customerStatisticsService.page(query);
    }

    @Operation(summary = "分页查询客户统计")
    @GetMapping("/summary")
    public CustomerStatisticsOverviewResp summary(CustomerStatisticsQuery query) {
        return customerStatisticsService.selectCustomerStatSummary(query);
    }

    @Operation(summary = "分页查询商务统计")
    @PostMapping("/business/page")
    public PageResp<BusinessStatisticsResp> businessPage(@RequestBody BusinessStatisticsQuery query) {
        return customerStatisticsService.businessPage(query);
    }

    @Operation(summary = "导出客户统计")
    @GetMapping("/export")
    @SaCheckPermission("customer:statistics:export")
    public void export(CustomerStatisticsQuery query, HttpServletResponse response) throws IOException {
        List<CustomerStatisticsResp> list = customerStatisticsService.listCustomerStat(query);
        ExcelUtils.export(list, "导出数据", CustomerStatisticsResp.class, response);
    }

    @GetMapping("spent/trend")
    public PageResp<CustomerSpentTrendResp> getCustomerSpentTrend(CustomerSpentTrendQuery customerSpentTrendQuery,
                                                                  PageQuery query) {
        return customerStatisticsService.selectCustomerSpentTrendList(customerSpentTrendQuery, query);
    }
}