package top.continew.admin.controller.crm;

import top.continew.admin.biz.model.query.crm.CustomerVisitTaskQuery;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskUpdateReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskCloseReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskDetailReq;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskDetailResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskDetailsResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskResp;
import top.continew.admin.biz.service.crm.CustomerVisitTaskService;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.web.model.R;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;

import java.util.List;

/**
 * 客户回访任务管理 API
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Tag(name = "客户回访任务管理 API")
@RestController
@CrudRequestMapping(value = "/biz/customerVisitTask", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CustomerVisitTaskController extends BaseController<CustomerVisitTaskService, CustomerVisitTaskResp, CustomerVisitTaskDetailResp, CustomerVisitTaskQuery, CustomerVisitTaskReq> {
    
    @Operation(summary = "关闭客户回访任务", description = "关闭客户回访任务")
    @PutMapping("/{id}/close")
    public void closeTask(@Parameter(description = "ID", example = "1") @PathVariable Long id,
                             @Validated @RequestBody CustomerVisitTaskCloseReq req) {
        baseService.closeTask(req, id);
    }
    
    @Operation(summary = "添加回访明细", description = "添加回访明细")
    @PostMapping("/detail")
    public Long addVisitDetail(@Validated @RequestBody CustomerVisitTaskDetailReq req) {
        return baseService.addVisitDetail(req);
    }
    
    @Operation(summary = "更新回访明细", description = "更新回访明细")
    @PutMapping("/detail/{id}")
    public void updateVisitDetail(@Parameter(description = "明细ID", example = "1") @PathVariable Long id,
                                     @Validated @RequestBody CustomerVisitTaskDetailReq req) {
        baseService.updateVisitDetail(req, id);
    }
    
    @Operation(summary = "删除回访明细", description = "删除回访明细")
    @DeleteMapping("/detail/{id}")
    public void deleteVisitDetail(@Parameter(description = "明细ID", example = "1") @PathVariable Long id) {
        baseService.deleteVisitDetail(id);
    }
    
    @Operation(summary = "获取任务回访明细列表", description = "获取任务回访明细列表")
    @GetMapping("/{taskId}/details")
    public List<CustomerVisitTaskDetailsResp> getVisitDetailsByTaskId(
            @Parameter(description = "任务ID", example = "1") @PathVariable Long taskId) {
        return baseService.getVisitDetailsByTaskId(taskId);
    }
}