package top.continew.admin.controller.crm;

import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.validation.Valid;
import top.continew.admin.biz.model.query.crm.SourceQuery;
import top.continew.admin.biz.model.req.crm.SourceGroupReq;
import top.continew.admin.biz.model.req.crm.SourceReq;
import top.continew.admin.biz.model.resp.crm.SourceDetailResp;
import top.continew.admin.biz.model.resp.crm.SourceGroupResp;
import top.continew.admin.biz.model.resp.crm.SourceResp;
import top.continew.admin.biz.service.crm.SourceService;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 来源管理 API
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Tag(name = "来源管理 API")
@RestController
@CrudRequestMapping(value = "/biz/source", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE})
public class SourceController extends BaseController<SourceService, SourceResp, SourceDetailResp, SourceQuery, SourceReq> {

    @Override
    @ResponseBody
    @GetMapping({"/list"})
    public List<SourceResp> list(SourceQuery query, SortQuery sortQuery) {
        return super.list(query, sortQuery);
    }

    @GetMapping("/group/list")
    @Log(ignore = true)
    public List<SourceGroupResp> listGroup() {
        return baseService.listGroup();
    }

    @PostMapping("/group")
    public void addGroup(@RequestBody @Valid SourceGroupReq req) {
        baseService.addGroup(req);
    }

    @DeleteMapping("/group/{id}")
    public void deleteGroup(@PathVariable Long id) {
        baseService.deleteGroup(id);
    }

    @PutMapping("/group/{id}")
    public void updateGroup(@PathVariable Long id, @RequestBody @Valid SourceGroupReq req) {
        baseService.updateGroup(id, req);
    }
}