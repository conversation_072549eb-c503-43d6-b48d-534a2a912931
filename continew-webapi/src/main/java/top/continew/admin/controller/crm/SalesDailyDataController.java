package top.continew.admin.controller.crm;

import io.swagger.v3.oas.annotations.Operation;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.crm.SalesDailyDataQuery;
import top.continew.admin.biz.model.req.crm.SalesDailyDataReq;
import top.continew.admin.biz.model.req.crm.SalesDailyDataConvertReq;
import top.continew.admin.biz.model.resp.crm.SalesDailyDataDetailResp;
import top.continew.admin.biz.model.resp.crm.SalesDailyDataResp;
import top.continew.admin.biz.service.crm.SalesDailyDataService;

import java.util.List;

/**
 * 商务每日数据管理 API
 *
 * <AUTHOR>
 * @since 2025/07/11 10:55
 */
@Tag(name = "商务每日数据管理 API")
@RestController
@CrudRequestMapping(value = "/biz/salesDailyData", api = {Api.PAGE, Api.UPDATE, Api.DETAIL, Api.DELETE, Api.EXPORT})
public class SalesDailyDataController extends BaseController<SalesDailyDataService, SalesDailyDataResp, SalesDailyDataDetailResp, SalesDailyDataQuery, SalesDailyDataReq> {

    @PostMapping("/batchAdd")
    @Operation(summary = "批量新增", description = "批量新增")
    public void batchAdd(@RequestBody List<SalesDailyDataReq> req){
        baseService.batchAdd(req);
    }

    @PostMapping("/createLead")
    @Operation(summary = "创建线索", description = "创建线索")
    public void createLead(Long id){
        baseService.createLead(id);
    }

    @PostMapping("/convert")
    @Operation(summary = "转化数据", description = "将销售日报数据转化为线索或商机")
    public void convert(@RequestBody SalesDailyDataConvertReq req){
        baseService.convert(req);
    }
}