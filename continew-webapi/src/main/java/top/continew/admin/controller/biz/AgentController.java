package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.AgentQuery;
import top.continew.admin.biz.model.req.AgentReq;
import top.continew.admin.biz.model.resp.AgentDetailResp;
import top.continew.admin.biz.model.resp.AgentResp;
import top.continew.admin.biz.service.AgentService;

/**
 * 中介管理 API
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Tag(name = "中介管理 API")
@RestController
@CrudRequestMapping(value = "/biz/agent", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class AgentController extends BaseController<AgentService, AgentResp, AgentDetailResp, Agent<PERSON>uery, AgentReq> {}