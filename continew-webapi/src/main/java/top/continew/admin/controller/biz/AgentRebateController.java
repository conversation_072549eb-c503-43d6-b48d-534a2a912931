package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.query.AgentRebateQuery;
import top.continew.admin.biz.model.req.AgentRebateConfirmReq;
import top.continew.admin.biz.model.req.AgentRebateReq;
import top.continew.admin.biz.model.resp.AgentRebateDetailResp;
import top.continew.admin.biz.model.resp.AgentRebateResp;
import top.continew.admin.biz.service.AgentRebateService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.log.annotation.Log;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 中介返点管理 API
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Tag(name = "中介返点管理 API")
@RestController
@CrudRequestMapping(value = "/biz/agentRebate", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
public class AgentRebateController extends BaseController<AgentRebateService, AgentRebateResp, AgentRebateDetailResp, AgentRebateQuery, AgentRebateReq> {

    @Log(ignore = true)
    @GetMapping("summary")
    public Map<String, BigDecimal> getTotalRebateAmount(AgentRebateQuery query) {
        return this.baseService.getTotalRebateAmount(query);
    }

    @Operation(summary = "确认返点", description = "确认返点")
    @SaCheckPermission("biz:agentRebate:confirm")
    @PutMapping("/{id}/confirm")
    public void confirm(@PathVariable("id") Long id, @RequestBody AgentRebateConfirmReq req) {
        req.setId(id);
        this.baseService.confirmRebate(req);
    }
}