package top.continew.admin.controller.crm;

import top.continew.admin.biz.model.query.crm.CustomerVisitStrategyQuery;
import top.continew.admin.biz.model.req.crm.CustomerVisitStrategyReq;
import top.continew.admin.biz.model.resp.crm.CustomerExecuteStrategyResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitStrategyDetailResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitStrategyResp;
import top.continew.admin.biz.service.crm.CustomerVisitStrategyService;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;


/**
 * 客户回访策略管理 API
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Tag(name = "客户回访策略管理 API")
@RestController
@CrudRequestMapping(value = "/biz/customerVisitStrategy", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CustomerVisitStrategyController extends BaseController<CustomerVisitStrategyService, CustomerVisitStrategyResp, CustomerVisitStrategyDetailResp, CustomerVisitStrategyQuery, CustomerVisitStrategyReq> {

    /**
     * 执行客户回访策略
     *
     * @param id 策略ID
     * @return 执行结果
     */
    @PostMapping("/{id}/execute")
    @Operation(summary = "执行客户回访策略", description = "执行指定的客户回访策略，创建回访任务")
    public CustomerExecuteStrategyResp executeStrategy(@PathVariable Long id) {
        CustomerExecuteStrategyResp result = baseService.executeStrategy(id);
        return result;
    }

}