package top.continew.admin.controller;

import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.req.AdAccountOrderAddReq;
import top.continew.admin.biz.service.CustomerRequirementService;



@RestController
@RequestMapping("/api/order")
@RequiredArgsConstructor
public class AdAccountOrderController {

    private final CustomerRequirementService customerRequirementService;

    @PostMapping("/requirement/add")
    @Operation(summary = "新增下户需求",description = "新增下户需求")
    public Long add(@RequestBody @Validated AdAccountOrderAddReq req) {
        return customerRequirementService.customerApply(req);
    }

}
