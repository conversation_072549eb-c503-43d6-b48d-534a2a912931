package top.continew.admin.controller;

import cn.hutool.extra.spring.SpringUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.service.*;

@RestController
@RequestMapping("/api/adAccount")
@RequiredArgsConstructor
public class AdAccountController {

    private final AdAccountOrderService adAccountOrderService;

    private final CustomerOrderGroupService customerOrderGroupService;

    private final ClearOrderService clearOrderService;

    private final RechargeOrderService rechargeOrderService;

    private final RefundOrderService refundOrderService;

    @PostMapping("/receive")
    @Operation(summary = "客户确认接收广告户",description = "客户确认接收广告户")
    public void receive(@RequestBody AdAccountOrderReceiveReq req) {

        adAccountOrderService.receive(req);
    }

    @PostMapping("/createGroup")
    @Operation(summary = "创建广告户分组",description = "创建广告户分组")
    public void createGroup(@RequestBody @Validated CustomerOrderGroupReq req) {
        customerOrderGroupService.createGroup(req);
    }

    @PostMapping("/{customerId}/deleteGroup")
    @Operation(summary = "删除广告户分组",description = "删除广告户分组")
    public void deleteGroup(@RequestBody @Validated IdsReq req, @PathVariable Long customerId) {
        customerOrderGroupService.deleteGroup(req.getIds(),customerId);
    }

    @PostMapping("/editGroup")
    @Operation(summary = "编辑广告户分组",description = "编辑广告户分组")
    public void editGroup(@RequestBody @Validated CustomerOrderGroupReq req) {

        customerOrderGroupService.editGroup(req);
    }

    @PostMapping("/distributeGroup")
    @Operation(summary = "广告户分配广告户分组",description = "广告户分配广告户分组")
    public void distributeGroup(@RequestBody @Validated DistributeGroupReq req) {
        customerOrderGroupService.distributeGroup(req);
    }

    @PostMapping("/{customerId}/clearGroup")
    @Operation(summary = "清零广告户分组",description = "清零广告户分组")
    public void clearGroup(@RequestBody @Validated IdsReq req, @PathVariable Long customerId) {
        customerOrderGroupService.clearGroup(req,customerId);
    }

    @PostMapping("/clearAdAccount")
    @Operation(summary = "清零广告户",description = "清零广告户")
    public void clearAdAccount(@RequestBody @Validated ClearOrderReq req) {
        clearOrderService.clearApply(req);
    }


    @PostMapping("/rechargeAdAccount")
    @Operation(summary = "充值广告户",description = "充值广告户")
    public void recharge(@RequestBody @Validated RechargeOrderByCustomerReq req) {
        rechargeOrderService.rechargeApply(req);
    }

    @PostMapping("/refundAdAccount")
    @Operation(summary = "减款广告户",description = "减款广告户")
    public void refund(@RequestBody @Validated RefundOrderByCustomerReq req) {
        refundOrderService.refundApply(req);
    }

}
