<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.continew</groupId>
        <artifactId>continew-admin</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>continew-client-webapi</artifactId>
    <description>API 及打包部署模块</description>

    <properties>
        <!-- ### 打包配置相关 ### -->
        <!-- 启动类 -->
        <main-class>top.continew.admin.ContiNewClientApplication</main-class>
        <!-- 程序 jar 输出目录 -->
        <bin-path>bin/</bin-path>
        <!-- 配置文件输出目录 -->
        <config-path>config/</config-path>
        <!-- 依赖 jar 输出目录 -->
        <lib-path>lib/</lib-path>
    </properties>

    <dependencies>
        <!-- ContiNew Starter 日志模块 - 拦截器版（Spring Boot Actuator HttpTrace 增强版） -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-log-interceptor</artifactId>
        </dependency>

        <!-- 系统管理模块（存放系统管理模块相关功能，例如：部门管理、角色管理、用户管理等） -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-module-system</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>continew-client</finalName>
        <plugins>
            <!-- Maven 打包插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>