stages:
  - build
  - deploy

build:
  stage: build
  script:
    - mvn clean package -DskipTests
  artifacts:
    paths:
      - continew-webapi/target/*.jar
  when: manual
  only:
    - master

deploy:
  stage: deploy
  needs: ["build"]
  script:
    - echo "$SSH_PRIVATE_KEY" > private_key
    - chmod 600 private_key
    - rsync -avz -e "ssh -i private_key -o StrictHostKeyChecking=no" continew-webapi/target/continew-admin.jar $SSH_USER@$SERVER_IP:/root
    - ssh -i private_key $SSH_USER@$SERVER_IP "mv /root/continew-admin.jar /opt/facebook/cms; cd /opt/facebook/cms; sh facebook-admin.sh restart"
  only:
    - master

build-client:
  stage: build
  script:
    - mvn clean package -DskipTests
  artifacts:
    paths:
      - continew-client-webapi/target/*.jar
  when: manual
  only:
    - master

deploy-client:
  stage: deploy
  needs: ["build-client"]
  script:
    - echo "$SSH_PRIVATE_KEY" > private_key
    - chmod 600 private_key
    - rsync -avz -e "ssh -i private_key -o StrictHostKeyChecking=no" continew-client-webapi/target/continew-client.jar root@*************:/opt/scm/scm-client-api/bak
    - ssh -i private_key root@************* "cd /opt/scm/scm-client-api;[ -f continew-client.jar ] && cp continew-client.jar bak/continew-client.jar.$(date +%Y%m%d%H%M%S);mv bak/continew-client.jar .;sh api-service.sh restart"
  only:
    - master