[{"label": "Abidjan, Africa (GMT+00:00)", "value": "Africa/Abidjan"}, {"label": "Accra, Africa (GMT+00:00)", "value": "Africa/Accra"}, {"label": "Adak, America (GMT-09:00)", "value": "America/Adak"}, {"label": "Addis Ababa, Africa (GMT+03:00)", "value": "Africa/Addis_Ababa"}, {"label": "Adelaide, Australia (GMT+09:30)", "value": "Australia/Adelaide"}, {"label": "Aden, Asia (GMT+03:00)", "value": "Asia/Aden"}, {"label": "Algiers, Africa (GMT+01:00)", "value": "Africa/Algiers"}, {"label": "Almaty, Asia (GMT+05:00)", "value": "Asia/Almaty"}, {"label": "Amman, Asia (GMT+03:00)", "value": "Asia/Amman"}, {"label": "Amsterdam, Europe (GMT+02:00)", "value": "Europe/Amsterdam"}, {"label": "Anadyr, Asia (GMT+12:00)", "value": "Asia/Anadyr"}, {"label": "Anchorage, America (GMT-08:00)", "value": "America/Anchorage"}, {"label": "Andorra, Europe (GMT+02:00)", "value": "Europe/Andorra"}, {"label": "Anguilla, America (GMT-04:00)", "value": "America/Anguilla"}, {"label": "<PERSON><PERSON><PERSON><PERSON>, Indian (GMT+03:00)", "value": "Indian/Antananarivo"}, {"label": "Antigua, America (GMT-04:00)", "value": "America/Antigua"}, {"label": "Apia, Pacific (GMT+13:00)", "value": "Pacific/Apia"}, {"label": "Aqtau, Asia (GMT+05:00)", "value": "Asia/Aqtau"}, {"label": "Aqtobe, Asia (GMT+05:00)", "value": "Asia/Aqtobe"}, {"label": "Araguaina, America (GMT-03:00)", "value": "America/Araguaina"}, {"label": "Aruba, America (GMT-04:00)", "value": "America/Aruba"}, {"label": "Ashgabat, Asia (GMT+05:00)", "value": "Asia/Ashgabat"}, {"label": "Asmara, Africa (GMT+03:00)", "value": "Africa/Asmara"}, {"label": "Astrakhan, Europe (GMT+04:00)", "value": "Europe/Astrakhan"}, {"label": "Asuncion, America (GMT-03:00)", "value": "America/Asuncion"}, {"label": "Athens, Europe (GMT+03:00)", "value": "Europe/Athens"}, {"label": "Atikokan, America (GMT-05:00)", "value": "America/Atikokan"}, {"label": "Atyrau, Asia (GMT+05:00)", "value": "Asia/Atyrau"}, {"label": "Auckland, Pacific (GMT+12:00)", "value": "Pacific/Auckland"}, {"label": "Azores, Atlantic (GMT+00:00)", "value": "Atlantic/Azores"}, {"label": "Baghdad, Asia (GMT+03:00)", "value": "Asia/Baghdad"}, {"label": "Bahia Banderas, America (GMT-06:00)", "value": "America/Bahia_Banderas"}, {"label": "Bahia, America (GMT-03:00)", "value": "America/Bahia"}, {"label": "Bahrain, Asia (GMT+03:00)", "value": "Asia/Bahrain"}, {"label": "Baku, Asia (GMT+04:00)", "value": "Asia/Baku"}, {"label": "Bamako, Africa (GMT+00:00)", "value": "Africa/Bamako"}, {"label": "Bangkok, Asia (GMT+07:00)", "value": "Asia/Bangkok"}, {"label": "Bangui, Africa (GMT+01:00)", "value": "Africa/Bangui"}, {"label": "Banjul, Africa (GMT+00:00)", "value": "Africa/Banjul"}, {"label": "Barbados, America (GMT-04:00)", "value": "America/Barbados"}, {"label": "Barnaul, Asia (GMT+07:00)", "value": "Asia/Barnaul"}, {"label": "Beirut, Asia (GMT+03:00)", "value": "Asia/Beirut"}, {"label": "Belem, America (GMT-03:00)", "value": "America/Belem"}, {"label": "Belgrade, Europe (GMT+02:00)", "value": "Europe/Belgrade"}, {"label": "Belize, America (GMT-06:00)", "value": "America/Belize"}, {"label": "Berlin, Europe (GMT+02:00)", "value": "Europe/Berlin"}, {"label": "Bermuda, Atlantic (GMT-03:00)", "value": "Atlantic/Bermuda"}, {"label": "Beulah, North Dakota, America (GMT-05:00)", "value": "America/North_Dakota/Beulah"}, {"label": "Bishkek, Asia (GMT+06:00)", "value": "Asia/Bishkek"}, {"label": "Bissau, Africa (GMT+00:00)", "value": "Africa/Bissau"}, {"label": "Blanc-Sablon, America (GMT-04:00)", "value": "America/Blanc-Sablon"}, {"label": "Blantyre, Africa (GMT+02:00)", "value": "Africa/Blantyre"}, {"label": "Boa Vista, America (GMT-04:00)", "value": "America/Boa_Vista"}, {"label": "Bogota, America (GMT-05:00)", "value": "America/Bogota"}, {"label": "Boise, America (GMT-06:00)", "value": "America/Boise"}, {"label": "Bougainville, Pacific (GMT+11:00)", "value": "Pacific/Bougainville"}, {"label": "Bratislava, Europe (GMT+02:00)", "value": "Europe/Bratislava"}, {"label": "Brazzaville, Africa (GMT+01:00)", "value": "Africa/Brazzaville"}, {"label": "Brisbane, Australia (GMT+10:00)", "value": "Australia/Brisbane"}, {"label": "Broken Hill, Australia (GMT+09:30)", "value": "Australia/Broken_Hill"}, {"label": "Brunei, Asia (GMT+08:00)", "value": "Asia/Brunei"}, {"label": "Brussels, Europe (GMT+02:00)", "value": "Europe/Brussels"}, {"label": "Bucharest, Europe (GMT+03:00)", "value": "Europe/Bucharest"}, {"label": "Budapest, Europe (GMT+02:00)", "value": "Europe/Budapest"}, {"label": "Buenos Aires, America (GMT-03:00)", "value": "America/Buenos_Aires"}, {"label": "Buenos Aires, Argentina, America (GMT-03:00)", "value": "America/Argentina/Buenos_Aires"}, {"label": "Bujumbura, Africa (GMT+02:00)", "value": "Africa/Bujumbura"}, {"label": "Busingen, Europe (GMT+02:00)", "value": "Europe/Busingen"}, {"label": "CET (GMT+02:00)", "value": "CET"}, {"label": "CST6CDT (GMT-05:00)", "value": "CST6CDT"}, {"label": "Cairo, Africa (GMT+03:00)", "value": "Africa/Cairo"}, {"label": "Calcutta, Asia (GMT+05:30)", "value": "Asia/Calcutta"}, {"label": "Cambridge Bay, America (GMT-06:00)", "value": "America/Cambridge_Bay"}, {"label": "Campo Grande, America (GMT-04:00)", "value": "America/Campo_Grande"}, {"label": "Canary, Atlantic (GMT+01:00)", "value": "Atlantic/Canary"}, {"label": "Cancun, America (GMT-05:00)", "value": "America/Cancun"}, {"label": "Cape Verde, Atlantic (GMT-01:00)", "value": "Atlantic/Cape_Verde"}, {"label": "Caracas, America (GMT-04:00)", "value": "America/Caracas"}, {"label": "Casablanca, Africa (GMT+01:00)", "value": "Africa/Casablanca"}, {"label": "Casey, Antarctica (GMT+08:00)", "value": "Antarctica/Casey"}, {"label": "Catamarca, Argentina, America (GMT-03:00)", "value": "America/Argentina/Catamarca"}, {"label": "Cayenne, America (GMT-03:00)", "value": "America/Cayenne"}, {"label": "Cayman, America (GMT-05:00)", "value": "America/Cayman"}, {"label": "Center, North Dakota, America (GMT-05:00)", "value": "America/North_Dakota/Center"}, {"label": "Ceuta, Africa (GMT+02:00)", "value": "Africa/Ceuta"}, {"label": "<PERSON><PERSON>, Indian (GMT+06:00)", "value": "Indian/Chagos"}, {"label": "Chatham, Pacific (GMT+12:45)", "value": "Pacific/Chatham"}, {"label": "Chicago, America (GMT-05:00)", "value": "America/Chicago"}, {"label": "Chihuahua, America (GMT-06:00)", "value": "America/Chihuahua"}, {"label": "Chisinau, Europe (GMT+03:00)", "value": "Europe/Chisinau"}, {"label": "Chita, Asia (GMT+09:00)", "value": "Asia/Chita"}, {"label": "Choibalsan, Asia (GMT+08:00)", "value": "Asia/Choibalsan"}, {"label": "Christmas, Indian (GMT+07:00)", "value": "Indian/Christmas"}, {"label": "Chuuk, Pacific (GMT+10:00)", "value": "Pacific/Chuuk"}, {"label": "<PERSON><PERSON>, Indian (GMT+06:30)", "value": "Indian/Cocos"}, {"label": "Colombo, Asia (GMT+05:30)", "value": "Asia/Colombo"}, {"label": "<PERSON><PERSON>, Indian (GMT+03:00)", "value": "Indian/Comoro"}, {"label": "Conakry, Africa (GMT+00:00)", "value": "Africa/Conakry"}, {"label": "Copenhagen, Europe (GMT+02:00)", "value": "Europe/Copenhagen"}, {"label": "Cordoba, Argentina, America (GMT-03:00)", "value": "America/Argentina/Cordoba"}, {"label": "Costa Rica, America (GMT-06:00)", "value": "America/Costa_Rica"}, {"label": "Creston, America (GMT-07:00)", "value": "America/Creston"}, {"label": "Cuiaba, America (GMT-04:00)", "value": "America/Cuiaba"}, {"label": "Curacao, America (GMT-04:00)", "value": "America/Curacao"}, {"label": "Currie, Australia (GMT+10:00)", "value": "Australia/Currie"}, {"label": "Dakar, Africa (GMT+00:00)", "value": "Africa/Dakar"}, {"label": "Damascus, Asia (GMT+03:00)", "value": "Asia/Damascus"}, {"label": "Danmarkshavn, America (GMT+00:00)", "value": "America/Danmarkshavn"}, {"label": "Dar es Salaam, Africa (GMT+03:00)", "value": "Africa/Dar_es_Salaam"}, {"label": "Darwin, Australia (GMT+09:30)", "value": "Australia/Darwin"}, {"label": "Davis, Antarctica (GMT+07:00)", "value": "Antarctica/Davis"}, {"label": "Dawson Creek, America (GMT-07:00)", "value": "America/Dawson_Creek"}, {"label": "Dawson, America (GMT-07:00)", "value": "America/Dawson"}, {"label": "Denver, America (GMT-06:00)", "value": "America/Denver"}, {"label": "Detroit, America (GMT-04:00)", "value": "America/Detroit"}, {"label": "Dhaka, Asia (GMT+06:00)", "value": "Asia/Dhaka"}, {"label": "Dili, Asia (GMT+09:00)", "value": "Asia/Dili"}, {"label": "Djibouti, Africa (GMT+03:00)", "value": "Africa/Djibouti"}, {"label": "Dominica, America (GMT-04:00)", "value": "America/Dominica"}, {"label": "Douala, Africa (GMT+01:00)", "value": "Africa/Douala"}, {"label": "Dubai, Asia (GMT+04:00)", "value": "Asia/Dubai"}, {"label": "Dublin, Europe (GMT+01:00)", "value": "Europe/Dublin"}, {"label": "DumontDUrville, Antarctica (GMT+10:00)", "value": "Antarctica/DumontDUrville"}, {"label": "Dushanbe, Asia (GMT+05:00)", "value": "Asia/Dushanbe"}, {"label": "EET (GMT+03:00)", "value": "EET"}, {"label": "EST (GMT-05:00)", "value": "EST"}, {"label": "EST5EDT (GMT-04:00)", "value": "EST5EDT"}, {"label": "Easter, Pacific (GMT-06:00)", "value": "Pacific/Easter"}, {"label": "Edmonton, America (GMT-06:00)", "value": "America/Edmonton"}, {"label": "<PERSON><PERSON>te, Pacific (GMT+11:00)", "value": "Pacific/Efate"}, {"label": "Eirunepe, America (GMT-05:00)", "value": "America/Eirunepe"}, {"label": "El <PERSON>, Africa (GMT+01:00)", "value": "Africa/El_Aaiun"}, {"label": "El Salvador, America (GMT-06:00)", "value": "America/El_Salvador"}, {"label": "Enderbury, Pacific (GMT+13:00)", "value": "Pacific/Enderbury"}, {"label": "Etc/GMT (GMT+00:00)", "value": "Etc/GMT"}, {"label": "Etc/GMT+0 (GMT+00:00)", "value": "Etc/GMT+0"}, {"label": "Etc/GMT+1 (GMT-01:00)", "value": "Etc/GMT+1"}, {"label": "Etc/GMT+10 (GMT-10:00)", "value": "Etc/GMT+10"}, {"label": "Etc/GMT+11 (GMT-11:00)", "value": "Etc/GMT+11"}, {"label": "Etc/GMT+12 (GMT-12:00)", "value": "Etc/GMT+12"}, {"label": "Etc/GMT+2 (GMT-02:00)", "value": "Etc/GMT+2"}, {"label": "Etc/GMT+3 (GMT-03:00)", "value": "Etc/GMT+3"}, {"label": "Etc/GMT+4 (GMT-04:00)", "value": "Etc/GMT+4"}, {"label": "Etc/GMT+5 (GMT-05:00)", "value": "Etc/GMT+5"}, {"label": "Etc/GMT+6 (GMT-06:00)", "value": "Etc/GMT+6"}, {"label": "Etc/GMT+7 (GMT-07:00)", "value": "Etc/GMT+7"}, {"label": "Etc/GMT+8 (GMT-08:00)", "value": "Etc/GMT+8"}, {"label": "Etc/GMT+9 (GMT-09:00)", "value": "Etc/GMT+9"}, {"label": "Etc/GMT-0 (GMT+00:00)", "value": "Etc/GMT-0"}, {"label": "Etc/GMT-1 (GMT+01:00)", "value": "Etc/GMT-1"}, {"label": "Etc/GMT-10 (GMT+10:00)", "value": "Etc/GMT-10"}, {"label": "Etc/GMT-11 (GMT+11:00)", "value": "Etc/GMT-11"}, {"label": "Etc/GMT-12 (GMT+12:00)", "value": "Etc/GMT-12"}, {"label": "Etc/GMT-13 (GMT+13:00)", "value": "Etc/GMT-13"}, {"label": "Etc/GMT-14 (GMT+14:00)", "value": "Etc/GMT-14"}, {"label": "Etc/GMT-2 (GMT+02:00)", "value": "Etc/GMT-2"}, {"label": "Etc/GMT-3 (GMT+03:00)", "value": "Etc/GMT-3"}, {"label": "Etc/GMT-4 (GMT+04:00)", "value": "Etc/GMT-4"}, {"label": "Etc/GMT-5 (GMT+05:00)", "value": "Etc/GMT-5"}, {"label": "Etc/GMT-6 (GMT+06:00)", "value": "Etc/GMT-6"}, {"label": "Etc/GMT-7 (GMT+07:00)", "value": "Etc/GMT-7"}, {"label": "Etc/GMT-8 (GMT+08:00)", "value": "Etc/GMT-8"}, {"label": "Etc/GMT-9 (GMT+09:00)", "value": "Etc/GMT-9"}, {"label": "Etc/GMT0 (GMT+00:00)", "value": "Etc/GMT0"}, {"label": "Eucla, Australia (GMT+08:45)", "value": "Australia/Eucla"}, {"label": "F<PERSON>ofo, Pacific (GMT+13:00)", "value": "Pacific/Fakaofo"}, {"label": "Famagusta, Asia (GMT+03:00)", "value": "Asia/Famagusta"}, {"label": "Faroe, Atlantic (GMT+01:00)", "value": "Atlantic/Faroe"}, {"label": "Fiji, Pacific (GMT+12:00)", "value": "Pacific/Fiji"}, {"label": "Fort Nelson, America (GMT-07:00)", "value": "America/Fort_Nelson"}, {"label": "Fortaleza, America (GMT-03:00)", "value": "America/Fortaleza"}, {"label": "Freetown, Africa (GMT+00:00)", "value": "Africa/Freetown"}, {"label": "Funafuti, Pacific (GMT+12:00)", "value": "Pacific/Funafuti"}, {"label": "GMT (GMT+00:00)", "value": "GMT"}, {"label": "Gaborone, Africa (GMT+02:00)", "value": "Africa/Gaborone"}, {"label": "Galapagos, Pacific (GMT-06:00)", "value": "Pacific/Galapagos"}, {"label": "Gambier, Pacific (GMT-09:00)", "value": "Pacific/Gambier"}, {"label": "Gaza, Asia (GMT+03:00)", "value": "Asia/Gaza"}, {"label": "Gibraltar, Europe (GMT+02:00)", "value": "Europe/Gibraltar"}, {"label": "Glace Bay, America (GMT-03:00)", "value": "America/Glace_Bay"}, {"label": "Godthab, America (GMT-01:00)", "value": "America/Godthab"}, {"label": "Goose Bay, America (GMT-03:00)", "value": "America/Goose_Bay"}, {"label": "Grand Turk, America (GMT-04:00)", "value": "America/Grand_Turk"}, {"label": "Greenwich Time (GMT+00:00)", "value": "Etc/Greenwich"}, {"label": "Grenada, America (GMT-04:00)", "value": "America/Grenada"}, {"label": "Guadalcanal, Pacific (GMT+11:00)", "value": "Pacific/Guadalcanal"}, {"label": "Guadeloupe, America (GMT-04:00)", "value": "America/Guadeloupe"}, {"label": "Guam, Pacific (GMT+10:00)", "value": "Pacific/Guam"}, {"label": "Guatemala, America (GMT-06:00)", "value": "America/Guatemala"}, {"label": "Guayaquil, America (GMT-05:00)", "value": "America/Guayaquil"}, {"label": "Guernsey, Europe (GMT+01:00)", "value": "Europe/Guernsey"}, {"label": "Guyana, America (GMT-04:00)", "value": "America/Guyana"}, {"label": "HST (GMT-10:00)", "value": "HST"}, {"label": "Halifax, America (GMT-03:00)", "value": "America/Halifax"}, {"label": "Harare, Africa (GMT+02:00)", "value": "Africa/Harare"}, {"label": "Havana, America (GMT-04:00)", "value": "America/Havana"}, {"label": "Hebron, Asia (GMT+03:00)", "value": "Asia/Hebron"}, {"label": "Helsinki, Europe (GMT+03:00)", "value": "Europe/Helsinki"}, {"label": "Hermosillo, America (GMT-07:00)", "value": "America/Hermosillo"}, {"label": "<PERSON>, Asia (GMT+07:00)", "value": "Asia/Ho_Chi_Minh"}, {"label": "Hobart, Australia (GMT+10:00)", "value": "Australia/Hobart"}, {"label": "Hong Kong, Asia (GMT+08:00)", "value": "Asia/Hong_Kong"}, {"label": "Honolulu, Pacific (GMT-10:00)", "value": "Pacific/Honolulu"}, {"label": "Hovd, Asia (GMT+07:00)", "value": "Asia/Hovd"}, {"label": "Indianapolis, America (GMT-04:00)", "value": "America/Indianapolis"}, {"label": "Indianapolis, Indiana, America (GMT-04:00)", "value": "America/Indiana/Indianapolis"}, {"label": "Inuvik, America (GMT-06:00)", "value": "America/Inuvik"}, {"label": "Iqaluit, America (GMT-04:00)", "value": "America/Iqaluit"}, {"label": "Irkutsk, Asia (GMT+08:00)", "value": "Asia/Irkutsk"}, {"label": "Isle of Man, Europe (GMT+01:00)", "value": "Europe/Isle_of_Man"}, {"label": "Istanbul, Asia (GMT+03:00)", "value": "Asia/Istanbul"}, {"label": "Istanbul, Europe (GMT+03:00)", "value": "Europe/Istanbul"}, {"label": "Jakarta, Asia (GMT+07:00)", "value": "Asia/Jakarta"}, {"label": "Jamaica, America (GMT-05:00)", "value": "America/Jamaica"}, {"label": "Jayapura, Asia (GMT+09:00)", "value": "Asia/Jayapura"}, {"label": "Jersey, Europe (GMT+01:00)", "value": "Europe/Jersey"}, {"label": "Jerusalem, Asia (GMT+03:00)", "value": "Asia/Jerusalem"}, {"label": "Johannesburg, Africa (GMT+02:00)", "value": "Africa/Johannesburg"}, {"label": "Juba, Africa (GMT+02:00)", "value": "Africa/Juba"}, {"label": "Jujuy, Argentina, America (GMT-03:00)", "value": "America/Argentina/Jujuy"}, {"label": "Juneau, America (GMT-08:00)", "value": "America/Juneau"}, {"label": "Kabul, Asia (GMT+04:30)", "value": "Asia/Kabul"}, {"label": "Kaliningrad, Europe (GMT+02:00)", "value": "Europe/Kaliningrad"}, {"label": "Kamchatka, Asia (GMT+12:00)", "value": "Asia/Kamchatka"}, {"label": "Kampala, Africa (GMT+03:00)", "value": "Africa/Kampala"}, {"label": "Karachi, Asia (GMT+05:00)", "value": "Asia/Karachi"}, {"label": "Kathmandu, Asia (GMT+05:45)", "value": "Asia/Kathmandu"}, {"label": "Katmandu, Asia (GMT+05:45)", "value": "Asia/Katmandu"}, {"label": "<PERSON><PERSON><PERSON><PERSON>, Indian (GMT+05:00)", "value": "Indian/Kerguelen"}, {"label": "Khandyga, Asia (GMT+09:00)", "value": "Asia/Khandyga"}, {"label": "Khartoum, Africa (GMT+02:00)", "value": "Africa/Khartoum"}, {"label": "Kiev, Europe (GMT+03:00)", "value": "Europe/Kiev"}, {"label": "Kigali, Africa (GMT+02:00)", "value": "Africa/Kigali"}, {"label": "Kinshasa, Africa (GMT+01:00)", "value": "Africa/Kinshasa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>, Pacific (GMT+14:00)", "value": "Pacific/Kiritimati"}, {"label": "<PERSON><PERSON>, Europe (GMT+03:00)", "value": "Europe/Kirov"}, {"label": "Knox, Indiana, America (GMT-05:00)", "value": "America/Indiana/Knox"}, {"label": "Kolkata, Asia (GMT+05:30)", "value": "Asia/Kolkata"}, {"label": "Kosrae, Pacific (GMT+11:00)", "value": "Pacific/Kosrae"}, {"label": "Kralendijk, America (GMT-04:00)", "value": "America/Kralendijk"}, {"label": "Krasnoyarsk, Asia (GMT+07:00)", "value": "Asia/Krasnoyarsk"}, {"label": "Kuala Lumpur, Asia (GMT+08:00)", "value": "Asia/Kuala_Lumpur"}, {"label": "Kuching, Asia (GMT+08:00)", "value": "Asia/Kuching"}, {"label": "Kuwait, Asia (GMT+03:00)", "value": "Asia/Kuwait"}, {"label": "<PERSON><PERSON><PERSON>lein, Pacific (GMT+12:00)", "value": "Pacific/Kwajalein"}, {"label": "La Paz, America (GMT-04:00)", "value": "America/La_Paz"}, {"label": "La Rioja, Argentina, America (GMT-03:00)", "value": "America/Argentina/La_Rioja"}, {"label": "Lagos, Africa (GMT+01:00)", "value": "Africa/Lagos"}, {"label": "Libreville, Africa (GMT+01:00)", "value": "Africa/Libreville"}, {"label": "Lima, America (GMT-05:00)", "value": "America/Lima"}, {"label": "<PERSON><PERSON><PERSON>, Australia (GMT+10:00)", "value": "Australia/Lindeman"}, {"label": "Lisbon, Europe (GMT+01:00)", "value": "Europe/Lisbon"}, {"label": "Ljubljana, Europe (GMT+02:00)", "value": "Europe/Ljubljana"}, {"label": "Lome, Africa (GMT+00:00)", "value": "Africa/Lome"}, {"label": "London, Europe (GMT+01:00)", "value": "Europe/London"}, {"label": "Longyearbyen, Arctic (GMT+02:00)", "value": "Arctic/Longyearbyen"}, {"label": "<PERSON>, Australia (GMT+10:30)", "value": "Australia/Lord_Howe"}, {"label": "Los Angeles, America (GMT-07:00)", "value": "America/Los_Angeles"}, {"label": "Louisville, Kentucky, America (GMT-04:00)", "value": "America/Kentucky/Louisville"}, {"label": "Lower Princes, America (GMT-04:00)", "value": "America/Lower_Princes"}, {"label": "Luanda, Africa (GMT+01:00)", "value": "Africa/Luanda"}, {"label": "Lubumbashi, Africa (GMT+02:00)", "value": "Africa/Lubumbashi"}, {"label": "Lusaka, Africa (GMT+02:00)", "value": "Africa/Lusaka"}, {"label": "Luxembourg, Europe (GMT+02:00)", "value": "Europe/Luxembourg"}, {"label": "MET (GMT+02:00)", "value": "MET"}, {"label": "MST (GMT-07:00)", "value": "MST"}, {"label": "MST7MDT (GMT-06:00)", "value": "MST7MDT"}, {"label": "Macau, Asia (GMT+08:00)", "value": "Asia/Macau"}, {"label": "Maceio, America (GMT-03:00)", "value": "America/Maceio"}, {"label": "Macquarie, Antarctica (GMT+10:00)", "value": "Antarctica/Macquarie"}, {"label": "Madeira, Atlantic (GMT+01:00)", "value": "Atlantic/Madeira"}, {"label": "Madrid, Europe (GMT+02:00)", "value": "Europe/Madrid"}, {"label": "Magadan, Asia (GMT+11:00)", "value": "Asia/Magadan"}, {"label": "<PERSON><PERSON>, Indian (GMT+04:00)", "value": "Indian/Mahe"}, {"label": "<PERSON><PERSON>, Pacific (GMT+12:00)", "value": "Pacific/Majuro"}, {"label": "Makassar, Asia (GMT+08:00)", "value": "Asia/Makassar"}, {"label": "Malabo, Africa (GMT+01:00)", "value": "Africa/Malabo"}, {"label": "Maldives, Indian (GMT+05:00)", "value": "Indian/Maldives"}, {"label": "Malta, Europe (GMT+02:00)", "value": "Europe/Malta"}, {"label": "Managua, America (GMT-06:00)", "value": "America/Managua"}, {"label": "Manaus, America (GMT-04:00)", "value": "America/Manaus"}, {"label": "Manila, Asia (GMT+08:00)", "value": "Asia/Manila"}, {"label": "Maputo, Africa (GMT+02:00)", "value": "Africa/Maputo"}, {"label": "Marengo, Indiana, America (GMT-04:00)", "value": "America/Indiana/Marengo"}, {"label": "Mariehamn, Europe (GMT+03:00)", "value": "Europe/Mariehamn"}, {"label": "Marigot, America (GMT-04:00)", "value": "America/Marigot"}, {"label": "Marquesas, Pacific (GMT-09:30)", "value": "Pacific/Marquesas"}, {"label": "Martinique, America (GMT-04:00)", "value": "America/Martinique"}, {"label": "Maseru, Africa (GMT+02:00)", "value": "Africa/Maseru"}, {"label": "Matamoros, America (GMT-05:00)", "value": "America/Matamoros"}, {"label": "Mauritius, Indian (GMT+04:00)", "value": "Indian/Mauritius"}, {"label": "Mawson, Antarctica (GMT+05:00)", "value": "Antarctica/Mawson"}, {"label": "<PERSON><PERSON>, Indian (GMT+03:00)", "value": "Indian/Mayotte"}, {"label": "Mazatlan, America (GMT-07:00)", "value": "America/Mazatlan"}, {"label": "Mbabane, Africa (GMT+02:00)", "value": "Africa/Mbabane"}, {"label": "McMurdo, Antarctica (GMT+12:00)", "value": "Antarctica/McMurdo"}, {"label": "Melbourne, Australia (GMT+10:00)", "value": "Australia/Melbourne"}, {"label": "Mendoza, Argentina, America (GMT-03:00)", "value": "America/Argentina/Mendoza"}, {"label": "Menominee, America (GMT-05:00)", "value": "America/Menominee"}, {"label": "Merida, America (GMT-06:00)", "value": "America/Merida"}, {"label": "Metlakatla, America (GMT-08:00)", "value": "America/Metlakatla"}, {"label": "Mexico City, America (GMT-06:00)", "value": "America/Mexico_City"}, {"label": "Midway, Pacific (GMT-11:00)", "value": "Pacific/Midway"}, {"label": "Minsk, Europe (GMT+03:00)", "value": "Europe/Minsk"}, {"label": "Miquelon, America (GMT-02:00)", "value": "America/Miquelon"}, {"label": "Mogadishu, Africa (GMT+03:00)", "value": "Africa/Mogadishu"}, {"label": "Monaco, Europe (GMT+02:00)", "value": "Europe/Monaco"}, {"label": "Moncton, America (GMT-03:00)", "value": "America/Moncton"}, {"label": "Monrovia, Africa (GMT+00:00)", "value": "Africa/Monrovia"}, {"label": "Monterrey, America (GMT-06:00)", "value": "America/Monterrey"}, {"label": "Montevideo, America (GMT-03:00)", "value": "America/Montevideo"}, {"label": "Monticello, Kentucky, America (GMT-04:00)", "value": "America/Kentucky/Monticello"}, {"label": "Montreal, America (GMT-04:00)", "value": "America/Montreal"}, {"label": "Montserrat, America (GMT-04:00)", "value": "America/Montserrat"}, {"label": "Moscow, Europe (GMT+03:00)", "value": "Europe/Moscow"}, {"label": "Muscat, Asia (GMT+04:00)", "value": "Asia/Muscat"}, {"label": "Nairobi, Africa (GMT+03:00)", "value": "Africa/Nairobi"}, {"label": "Nassau, America (GMT-04:00)", "value": "America/Nassau"}, {"label": "Nauru, Pacific (GMT+12:00)", "value": "Pacific/Nauru"}, {"label": "Ndjamena, Africa (GMT+01:00)", "value": "Africa/Ndjamena"}, {"label": "New Salem, North Dakota, America (GMT-05:00)", "value": "America/North_Dakota/New_Salem"}, {"label": "New York, America (GMT-04:00)", "value": "America/New_York"}, {"label": "Niamey, Africa (GMT+01:00)", "value": "Africa/Niamey"}, {"label": "Nicosia, Asia (GMT+03:00)", "value": "Asia/Nicosia"}, {"label": "Nicosia, Europe (GMT+03:00)", "value": "Europe/Nicosia"}, {"label": "Nipigon, America (GMT-04:00)", "value": "America/Nipigon"}, {"label": "Niue, Pacific (GMT-11:00)", "value": "Pacific/Niue"}, {"label": "Nome, America (GMT-08:00)", "value": "America/Nome"}, {"label": "Norfolk, Pacific (GMT+11:00)", "value": "Pacific/Norfolk"}, {"label": "Noronha, America (GMT-02:00)", "value": "America/Noronha"}, {"label": "Nouakchott, Africa (GMT+00:00)", "value": "Africa/Nouakchott"}, {"label": "Noumea, Pacific (GMT+11:00)", "value": "Pacific/Noumea"}, {"label": "Novokuznetsk, Asia (GMT+07:00)", "value": "Asia/Novokuznetsk"}, {"label": "Novosibirsk, Asia (GMT+07:00)", "value": "Asia/Novosibirsk"}, {"label": "Nuuk, America (GMT-01:00)", "value": "America/Nuuk"}, {"label": "Ojinaga, America (GMT-05:00)", "value": "America/Ojinaga"}, {"label": "Omsk, Asia (GMT+06:00)", "value": "Asia/Omsk"}, {"label": "Oral, Asia (GMT+05:00)", "value": "Asia/Oral"}, {"label": "Oslo, Europe (GMT+02:00)", "value": "Europe/Oslo"}, {"label": "Ouagadougou, Africa (GMT+00:00)", "value": "Africa/Ouagadougou"}, {"label": "PST8PDT (GMT-07:00)", "value": "PST8PDT"}, {"label": "Pago <PERSON>go, Pacific (GMT-11:00)", "value": "Pacific/Pago_Pago"}, {"label": "Palau, Pacific (GMT+09:00)", "value": "Pacific/Palau"}, {"label": "Palmer, Antarctica (GMT-03:00)", "value": "Antarctica/Palmer"}, {"label": "Panama, America (GMT-05:00)", "value": "America/Panama"}, {"label": "Pangnirtung, America (GMT-04:00)", "value": "America/Pangnirtung"}, {"label": "Paramaribo, America (GMT-03:00)", "value": "America/Paramaribo"}, {"label": "Paris, Europe (GMT+02:00)", "value": "Europe/Paris"}, {"label": "Perth, Australia (GMT+08:00)", "value": "Australia/Perth"}, {"label": "Petersburg, Indiana, America (GMT-04:00)", "value": "America/Indiana/Petersburg"}, {"label": "Phnom Penh, Asia (GMT+07:00)", "value": "Asia/Phnom_Penh"}, {"label": "Phoenix, America (GMT-07:00)", "value": "America/Phoenix"}, {"label": "Pitcairn, Pacific (GMT-08:00)", "value": "Pacific/Pitcairn"}, {"label": "Podgorica, Europe (GMT+02:00)", "value": "Europe/Podgorica"}, {"label": "Pohnpei, Pacific (GMT+11:00)", "value": "Pacific/Pohnpei"}, {"label": "Pontianak, Asia (GMT+07:00)", "value": "Asia/Pontianak"}, {"label": "Port Moresby, Pacific (GMT+10:00)", "value": "Pacific/Port_Moresby"}, {"label": "Port of Spain, America (GMT-04:00)", "value": "America/Port_of_Spain"}, {"label": "Port-au-Prince, America (GMT-04:00)", "value": "America/Port-au-Prince"}, {"label": "Porto Velho, America (GMT-04:00)", "value": "America/Porto_Velho"}, {"label": "Porto-Novo, Africa (GMT+01:00)", "value": "Africa/Porto-Novo"}, {"label": "Prague, Europe (GMT+02:00)", "value": "Europe/Prague"}, {"label": "Puerto Rico, America (GMT-04:00)", "value": "America/Puerto_Rico"}, {"label": "Punta Arenas, America (GMT-03:00)", "value": "America/Punta_Arenas"}, {"label": "Pyongyang, Asia (GMT+09:00)", "value": "Asia/Pyongyang"}, {"label": "Qatar, Asia (GMT+03:00)", "value": "Asia/Qatar"}, {"label": "Qostanay, Asia (GMT+05:00)", "value": "Asia/Qostanay"}, {"label": "Qyzylorda, Asia (GMT+05:00)", "value": "Asia/Qyzylorda"}, {"label": "Rainy River, America (GMT-05:00)", "value": "America/Rainy_River"}, {"label": "<PERSON><PERSON>, America (GMT-05:00)", "value": "America/Rankin_Inlet"}, {"label": "Rarotonga, Pacific (GMT-10:00)", "value": "Pacific/Rarotonga"}, {"label": "Recife, America (GMT-03:00)", "value": "America/Recife"}, {"label": "Regina, America (GMT-06:00)", "value": "America/Regina"}, {"label": "Resolute, America (GMT-05:00)", "value": "America/Resolute"}, {"label": "Reunion, Indian (GMT+04:00)", "value": "Indian/Reunion"}, {"label": "Reykjavik, Atlantic (GMT+00:00)", "value": "Atlantic/Reykjavik"}, {"label": "Riga, Europe (GMT+03:00)", "value": "Europe/Riga"}, {"label": "Rio Branco, America (GMT-05:00)", "value": "America/Rio_Branco"}, {"label": "Rio Gallegos, Argentina, America (GMT-03:00)", "value": "America/Argentina/Rio_Gallegos"}, {"label": "Riyadh, Asia (GMT+03:00)", "value": "Asia/Riyadh"}, {"label": "Rome, Europe (GMT+02:00)", "value": "Europe/Rome"}, {"label": "Rothera, Antarctica (GMT-03:00)", "value": "Antarctica/Rothera"}, {"label": "Saipan, Pacific (GMT+10:00)", "value": "Pacific/Saipan"}, {"label": "Sakhalin, Asia (GMT+11:00)", "value": "Asia/Sakhalin"}, {"label": "Salta, Argentina, America (GMT-03:00)", "value": "America/Argentina/Salta"}, {"label": "Samara, Europe (GMT+04:00)", "value": "Europe/Samara"}, {"label": "Samarkand, Asia (GMT+05:00)", "value": "Asia/Samarkand"}, {"label": "San Juan, Argentina, America (GMT-03:00)", "value": "America/Argentina/San_Juan"}, {"label": "San Luis, Argentina, America (GMT-03:00)", "value": "America/Argentina/San_Luis"}, {"label": "San Marino, Europe (GMT+02:00)", "value": "Europe/San_Marino"}, {"label": "Santarem, America (GMT-03:00)", "value": "America/Santarem"}, {"label": "Santiago, America (GMT-04:00)", "value": "America/Santiago"}, {"label": "Santo Domingo, America (GMT-04:00)", "value": "America/Santo_Domingo"}, {"label": "Sao Paulo, America (GMT-03:00)", "value": "America/Sao_Paulo"}, {"label": "Sao Tome, Africa (GMT+00:00)", "value": "Africa/Sao_Tome"}, {"label": "Sarajevo, Europe (GMT+02:00)", "value": "Europe/Sarajevo"}, {"label": "Saratov, Europe (GMT+04:00)", "value": "Europe/Saratov"}, {"label": "Scoresbysund, America (GMT-01:00)", "value": "America/Scoresbysund"}, {"label": "Seoul, Asia (GMT+09:00)", "value": "Asia/Seoul"}, {"label": "Shanghai, Asia (GMT+08:00)", "value": "Asia/Shanghai"}, {"label": "Simferopol, Europe (GMT+03:00)", "value": "Europe/Simferopol"}, {"label": "Singapore, Asia (GMT+08:00)", "value": "Asia/Singapore"}, {"label": "Sitka, America (GMT-08:00)", "value": "America/Sitka"}, {"label": "Skopje, Europe (GMT+02:00)", "value": "Europe/Skopje"}, {"label": "Sofia, Europe (GMT+03:00)", "value": "Europe/Sofia"}, {"label": "South Georgia, Atlantic (GMT-02:00)", "value": "Atlantic/South_Georgia"}, {"label": "Srednekolymsk, Asia (GMT+11:00)", "value": "Asia/Srednekolymsk"}, {"label": "St Barthelemy, America (GMT-04:00)", "value": "America/St_Barthelemy"}, {"label": "St Helena, Atlantic (GMT+00:00)", "value": "Atlantic/St_Helena"}, {"label": "St Johns, America (GMT-02:30)", "value": "America/St_Johns"}, {"label": "St Kitts, America (GMT-04:00)", "value": "America/St_Kitts"}, {"label": "St Lucia, America (GMT-04:00)", "value": "America/St_Lucia"}, {"label": "St Thomas, America (GMT-04:00)", "value": "America/St_Thomas"}, {"label": "St Vincent, America (GMT-04:00)", "value": "America/St_Vincent"}, {"label": "Stanley, Atlantic (GMT-03:00)", "value": "Atlantic/Stanley"}, {"label": "Stockholm, Europe (GMT+02:00)", "value": "Europe/Stockholm"}, {"label": "Swift Current, America (GMT-06:00)", "value": "America/Swift_Current"}, {"label": "Sydney, Australia (GMT+10:00)", "value": "Australia/Sydney"}, {"label": "Syowa, Antarctica (GMT+03:00)", "value": "Antarctica/Syowa"}, {"label": "Tahiti, Pacific (GMT-10:00)", "value": "Pacific/Tahiti"}, {"label": "Taipei, Asia (GMT+08:00)", "value": "Asia/Taipei"}, {"label": "Tallinn, Europe (GMT+03:00)", "value": "Europe/Tallinn"}, {"label": "Tarawa, Pacific (GMT+12:00)", "value": "Pacific/Tarawa"}, {"label": "Tashkent, Asia (GMT+05:00)", "value": "Asia/Tashkent"}, {"label": "Tbilisi, Asia (GMT+04:00)", "value": "Asia/Tbilisi"}, {"label": "Tegucigalpa, America (GMT-06:00)", "value": "America/Tegucigalpa"}, {"label": "Tehran, Asia (GMT+03:30)", "value": "Asia/Tehran"}, {"label": "Tell City, Indiana, America (GMT-05:00)", "value": "America/Indiana/Tell_City"}, {"label": "Thimphu, Asia (GMT+06:00)", "value": "Asia/Thimphu"}, {"label": "Thule, America (GMT-03:00)", "value": "America/Thule"}, {"label": "Thunder Bay, America (GMT-04:00)", "value": "America/Thunder_Bay"}, {"label": "Tijuana, America (GMT-07:00)", "value": "America/Tijuana"}, {"label": "Tirane, Europe (GMT+02:00)", "value": "Europe/Tirane"}, {"label": "Tokyo, Asia (GMT+09:00)", "value": "Asia/Tokyo"}, {"label": "Tomsk, Asia (GMT+07:00)", "value": "Asia/Tomsk"}, {"label": "Tongatapu, Pacific (GMT+13:00)", "value": "Pacific/Tongatapu"}, {"label": "Toronto, America (GMT-04:00)", "value": "America/Toronto"}, {"label": "Tortola, America (GMT-04:00)", "value": "America/Tortola"}, {"label": "Tripoli, Africa (GMT+02:00)", "value": "Africa/Tripoli"}, {"label": "Troll, Antarctica (GMT+02:00)", "value": "Antarctica/Troll"}, {"label": "Tucuman, Argentina, America (GMT-03:00)", "value": "America/Argentina/Tucuman"}, {"label": "Tunis, Africa (GMT+01:00)", "value": "Africa/Tunis"}, {"label": "UTC (GMT+00:00)", "value": "UTC"}, {"label": "Ulaanbaatar, Asia (GMT+08:00)", "value": "Asia/Ulaanbaatar"}, {"label": "Ulyanovsk, Europe (GMT+04:00)", "value": "Europe/Ulyanovsk"}, {"label": "Universal Time (GMT+00:00)", "value": "Etc/Universal"}, {"label": "Urumqi, Asia (GMT+06:00)", "value": "Asia/Urumqi"}, {"label": "Ushuaia, Argentina, America (GMT-03:00)", "value": "America/Argentina/Ushuaia"}, {"label": "Ust-Nera, Asia (GMT+10:00)", "value": "Asia/Ust-Nera"}, {"label": "Uzhgorod, Europe (GMT+03:00)", "value": "Europe/Uzhgorod"}, {"label": "Vaduz, Europe (GMT+02:00)", "value": "Europe/Vaduz"}, {"label": "Vancouver, America (GMT-07:00)", "value": "America/Vancouver"}, {"label": "Vatican, Europe (GMT+02:00)", "value": "Europe/Vatican"}, {"label": "Vevay, Indiana, America (GMT-04:00)", "value": "America/Indiana/Vevay"}, {"label": "Vienna, Europe (GMT+02:00)", "value": "Europe/Vienna"}, {"label": "Vientiane, Asia (GMT+07:00)", "value": "Asia/Vientiane"}, {"label": "Vilnius, Europe (GMT+03:00)", "value": "Europe/Vilnius"}, {"label": "Vincennes, Indiana, America (GMT-04:00)", "value": "America/Indiana/Vincennes"}, {"label": "Vladivostok, Asia (GMT+10:00)", "value": "Asia/Vladivostok"}, {"label": "Volgograd, Europe (GMT+03:00)", "value": "Europe/Volgograd"}, {"label": "Vostok, Antarctica (GMT+05:00)", "value": "Antarctica/Vostok"}, {"label": "WET (GMT+01:00)", "value": "WET"}, {"label": "Wake, Pacific (GMT+12:00)", "value": "Pacific/Wake"}, {"label": "Wallis, Pacific (GMT+12:00)", "value": "Pacific/Wallis"}, {"label": "Warsaw, Europe (GMT+02:00)", "value": "Europe/Warsaw"}, {"label": "Whitehorse, America (GMT-07:00)", "value": "America/Whitehorse"}, {"label": "Winamac, Indiana, America (GMT-04:00)", "value": "America/Indiana/Winamac"}, {"label": "Windhoek, Africa (GMT+02:00)", "value": "Africa/Windhoek"}, {"label": "Winnipeg, America (GMT-05:00)", "value": "America/Winnipeg"}, {"label": "Yakutat, America (GMT-08:00)", "value": "America/Yakutat"}, {"label": "Yakutsk, Asia (GMT+09:00)", "value": "Asia/Yakutsk"}, {"label": "Yangon, Asia (GMT+06:30)", "value": "Asia/Yangon"}, {"label": "Yekaterinburg, Asia (GMT+05:00)", "value": "Asia/Yekaterinburg"}, {"label": "Yellowknife, America (GMT-06:00)", "value": "America/Yellowknife"}, {"label": "Yerevan, Asia (GMT+04:00)", "value": "Asia/Yerevan"}, {"label": "Zagreb, Europe (GMT+02:00)", "value": "Europe/Zagreb"}, {"label": "Zaporozhye, Europe (GMT+03:00)", "value": "Europe/Zaporozhye"}, {"label": "Zulu Time (GMT+00:00)", "value": "Etc/Zulu"}, {"label": "Zurich, Europe (GMT+02:00)", "value": "Europe/Zurich"}]