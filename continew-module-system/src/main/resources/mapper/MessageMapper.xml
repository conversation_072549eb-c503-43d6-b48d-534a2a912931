<?xml version="1.0" encoding="UTF-8" ?> <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.system.mapper.MessageMapper">
    <select id="selectPageByUserId" resultType="top.continew.admin.system.model.resp.MessageResp">
        SELECT
            t1.*,
            t2.user_id,
            t2.is_read,
            t2.read_time
        FROM sys_message AS t1
            LEFT JOIN sys_message_user AS t2 ON t2.message_id = t1.id
        ${ew.getCustomSqlSegment}
    </select>
</mapper>
