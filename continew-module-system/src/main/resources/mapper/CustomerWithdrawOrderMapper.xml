<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CustomerWithdrawOrderMapper">
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.CustomerWithdrawOrderResp">
        select
        cwo.id,
        cwo.order_no,
        cwo.status,
        cwo.customer_id,
        cwo.expected_refund_time,
        cwo.remark,
        cwo.create_user,
        cwo.audit_time,
        cwo.actual_refund_time,
        cwo.actual_refund_amount,
        cwo.audit_remark,
        cwo.create_time,
        cwo.auditor,
        c.name as customer_name,
        c.balance as customer_balance
        from biz_customer_withdraw_order cwo left join biz_customer c on c.id = cwo.customer_id
        <where>
            <if test="null != query.customerId">
                and cwo.customer_id = #{query.customerId}
            </if>
            <if test="null != query.orderNo">
                and cwo.order_no = #{query.orderNo}
            </if>
            <if test="null != query.status">
                and cwo.status = #{query.status}
            </if>
            <if test="null != query.createUser">
                and cwo.create_user = #{createUser}
            </if>

            <if test="null != query.createTime and query.createTime.length!=0">
                and cwo.create_time &gt;= #{query.createTime[0]}
            </if>

            <if test="null != query.createTime and query.createTime.length>1">
                and cwo.create_time &lt;= #{query.createTime[1]}
            </if>

            <if test="null != query.expectedRefundTime and query.expectedRefundTime.length!=0">
                and cwo.expected_refund_time &gt;= #{query.expectedRefundTime[0]}
            </if>

            <if test="null != query.expectedRefundTime and query.expectedRefundTime.length>1">
                and cwo.expected_refund_time &lt;= #{query.expectedRefundTime[1]}
            </if>
        </where>
        ORDER BY cwo.create_time DESC
    </select>
</mapper>