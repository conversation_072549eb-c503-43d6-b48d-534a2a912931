<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CardBalanceMapper">
    <update id="updateCardNumber">
        UPDATE biz_card_balance a
        SET a.card_number = COALESCE(
                (SELECT b.card_number FROM biz_card b WHERE b.platform = #{platform} AND b.platform_card_id = a.platform_card_id and b.card_number!=a.card_number),
            ''
        )
        WHERE a.platform = #{platform} AND a.card_number = ''
    </update>
</mapper>