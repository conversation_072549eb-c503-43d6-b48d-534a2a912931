<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.SalesScheduleMapper">
    <select id="getUserListByTelegramId" resultType="top.continew.admin.system.model.entity.UserDO">
        select *
        from sys_user
        where
        telegram_id in
        <foreach collection="list" item="telegramId" open="(" separator="," close=")">
             #{telegramId}
        </foreach>
    </select>
</mapper>