<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.MaterialMapper">
    <sql id="base_query_sql">
        select m.*, bmc.name as channel_name
        from biz_material m
                 left join biz_business_manager_channel bmc on bmc.id = m.channel_id
    </sql>
    <sql id="stat_by_date_sql">
        select DATE (pay_date) as `date`, SUM (pay_price) as amount
        from biz_material
        <where>
            <if test="query.type != null">
                and type = #{query.type}
            </if>
            <if test="query.statTimes != null and query.statTimes.length > 0">
                and pay_date between #{query.statTimes[0]} and #{query.statTimes[1]}
            </if>
        </where>
        group by `date`
        order by `date` desc
    </sql>
    <sql id="stat_by_type_sql">
        select type, SUM(COALESCE(num, 0)) as num, SUM(pay_price) as amount
        from biz_material
        <where>
            <if test="query.type != null">
                and type = #{query.type}
            </if>
            <if test="query.statTimes != null and query.statTimes.length > 0">
                and pay_date between #{query.statTimes[0]} and #{query.statTimes[1]}
            </if>
        </where>
        group by type order by ${sort[0]} ${sort[1]}
    </sql>
    <select id="selectStatByDatePage" resultType="top.continew.admin.biz.model.resp.MaterialStatByDateResp">
        <include refid="stat_by_date_sql"/>
    </select>
    <select id="selectStatByDateList" resultType="top.continew.admin.biz.model.resp.MaterialStatByDateResp">
        <include refid="stat_by_date_sql"/>
    </select>
    <select id="selectStatByTypeList" resultType="top.continew.admin.biz.model.resp.MaterialStatByTypeResp">
        <include refid="stat_by_type_sql"/>
    </select>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.MaterialResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.MaterialResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
</mapper>