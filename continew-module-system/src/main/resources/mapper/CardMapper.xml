<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CardMapper">
    <sql id="baseQuerySql">
        SELECT card.*,
               a.keep_status,
               a.sale_status,
               a.account_status,
               a.clear_status,
               c.name AS related_customer,
               u.nickname as create_user_name
        FROM biz_card card
                 left join biz_ad_account a on a.platform_ad_id = card.platform_ad_id
                 left join biz_ad_account_order o on o.ad_account_id = card.platform_ad_id and o.status = 3
                 left join biz_customer c on c.id = o.customer_id
                 left join sys_user u on u.id = card.create_user
            ${ew.customSqlSegment}
    </sql>

    <update id="updateCardUsedAmount">
        UPDATE biz_card a
            LEFT JOIN (
            SELECT
            card_number,
            COALESCE(-SUM(trans_amount), 0) AS total_amount
            FROM biz_card_transaction
            WHERE platform = #{platform} and trans_status != 4
            GROUP BY card_number
            ) b ON a.card_number = b.card_number
            SET a.used_amount = b.total_amount
        WHERE a.platform = #{platform}
    </update>

    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.CardResp">
        <include refid="baseQuerySql"></include>
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.CardResp">
        <include refid="baseQuerySql"></include>
    </select>
    <select id="getNeedWithdrawCard" resultType="top.continew.admin.biz.model.resp.CardResp">
        select card.card_number,
               card.platform_ad_id,
               card.platform,
               card.balance,
               card.platform_card_id,
               card.status,
               a.account_status,
               a.sale_status
        from biz_card card
                 left join biz_ad_account a on a.platform_ad_id = card.platform_ad_id
        where ((card.platform = 3
            and card.balance > 0) or (card.platform = 2
            and card.balance > 1))
          and (a.sale_status = 5 or a.account_status = 2 or a.clear_status != 1);
    </select>
    <select id="cardHeadList" resultType="java.lang.String">
        SELECT DISTINCT LEFT(card_number, 6) AS card_prefix
        FROM biz_card
    </select>
</mapper>