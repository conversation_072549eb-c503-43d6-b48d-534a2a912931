<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.PurchaseReceiveOrderMapper">
    <sql id="base_query_sql">
        select ro.*, o.type, o.channel_id, bmc.name as channel_name, o.expect_num as purchase_num, o.total_price as purchase_price
        from biz_purchase_receive_order ro
                 left join biz_purchase_order o on o.id = ro.purchase_order_id
                 left join biz_business_manager_channel bmc on bmc.id = o.channel_id
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.PurchaseReceiveOrderResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.PurchaseReceiveOrderResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectPurchaseTotal" resultType="top.continew.admin.biz.model.resp.PurchaseReceiveOrderResp">
        select o.type,
               o.channel_id,
               bmc.name              as channel_name,
               SUM(ro.receive_num)   as receive_num,
               SUM(ro.receive_price) as receive_price
        from biz_purchase_receive_order ro
                 left join biz_purchase_order o on o.id = ro.purchase_order_id
                 left join biz_business_manager_channel bmc on bmc.id = o.channel_id ${ew.customSqlSegment}
    </select>
</mapper>