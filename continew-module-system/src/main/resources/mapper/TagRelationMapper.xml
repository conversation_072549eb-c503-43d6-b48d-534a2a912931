<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.TagRelationMapper">
    <select id="listAdAccountId" resultType="java.lang.Long">
        SELECT relation_id
        FROM biz_tag_relation
        WHERE type = 1 AND tag_id IN
        <foreach item="tag" collection="tags" open="(" separator="," close=")">
            #{tag}
        </foreach>
        GROUP BY relation_id
        HAVING COUNT(DISTINCT tag_id) = ${tags.size}
    </select>
    <select id="listOrderId" resultType="java.lang.Long">
        SELECT relation_id
        FROM biz_tag_relation
        WHERE type = 2 AND tag_id IN
        <foreach item="tag" collection="tags" open="(" separator="," close=")">
            #{tag}
        </foreach>
        GROUP BY relation_id
        HAVING COUNT(DISTINCT tag_id) = ${tags.size}
    </select>
    <select id="tagNamesByOrderId" resultType="java.lang.String">
        SELECT
            IFNULL(GROUP_CONCAT(t.`name`), '') AS tag_names
        FROM
            biz_tag t
                INNER JOIN biz_tag_relation tr ON tr.tag_id = t.id
        WHERE
            tr.relation_id = #{orderId}
    </select>
</mapper>