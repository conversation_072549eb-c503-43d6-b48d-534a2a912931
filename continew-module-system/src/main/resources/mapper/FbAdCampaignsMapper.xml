<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.FbAdCampaignsMapper">

    <!-- 根据广告户ID查询广告系列列表 -->
    <select id="selectByAdAccountId" resultType="top.continew.admin.biz.model.entity.FbAdCampaignsDO">
        SELECT *
        FROM biz_fb_ad_campaigns
        WHERE ad_account_id = #{adAccountId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据平台ID查询广告系列 -->
    <select id="selectByPlatformId" resultType="top.continew.admin.biz.model.entity.FbAdCampaignsDO">
        SELECT *
        FROM biz_fb_ad_campaigns
        WHERE platform_id = #{platformId}
        LIMIT 1
    </select>

    <!-- 根据状态查询广告系列列表 -->
    <select id="selectByStatus" resultType="top.continew.admin.biz.model.entity.FbAdCampaignsDO">
        SELECT *
        FROM biz_fb_ad_campaigns
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>
    <select id="selectByDeliveryStatus" resultType="top.continew.admin.biz.model.entity.FbAdCampaignsDO">
        SELECT *
        FROM biz_fb_ad_campaigns
        WHERE delivery_status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入或更新广告系列 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO biz_fb_ad_campaigns (
        platform_id, name, ad_account_id, status, daily_budget,
        lifetime_budget, bid_strategy, start_time, create_time, update_time, effective_status, delivery_status
        ) VALUES
        <foreach collection="campaigns" item="campaign" separator=",">
            (
            #{campaign.platformId},
            #{campaign.name},
            #{campaign.adAccountId},
            #{campaign.status},
            #{campaign.dailyBudget},
            #{campaign.lifetimeBudget},
            #{campaign.bidStrategy},
            #{campaign.startTime},
            NOW(),
            #{campaign.updateTime},
            #{campaign.effectiveStatus},
            #{campaign.deliveryStatus}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        ad_account_id = VALUES(ad_account_id),
        status = VALUES(status),
        daily_budget = VALUES(daily_budget),
        lifetime_budget = VALUES(lifetime_budget),
        bid_strategy = VALUES(bid_strategy),
        start_time = VALUES(start_time),
        update_time = VALUES(update_time),
        effective_status = VALUES(effective_status),
        delivery_status = VALUES(delivery_status)
    </insert>

    <!-- 批量插入广告系列（忽略重复） -->
    <insert id="batchInsertIgnore">
        INSERT IGNORE INTO biz_fb_ad_campaigns (
        platform_id, name, ad_account_id, status, daily_budget,
        lifetime_budget, bid_strategy, start_time,create_time, update_time, effective_status, delivery_status
        ) VALUES
        <foreach collection="campaigns" item="campaign" separator=",">
            (
            #{campaign.platformId},
            #{campaign.name},
            #{campaign.adAccountId},
            #{campaign.status},
            #{campaign.dailyBudget},
            #{campaign.lifetimeBudget},
            #{campaign.bidStrategy},
            #{campaign.startTime},
            NOW(),
            NOW(),
            effective_status = VALUES(effective_status),
            delivery_status = VALUES(delivery_status)
            )
        </foreach>
    </insert>

    <!-- 根据广告账户ID和更新时间删除不匹配的广告系列 -->
    <delete id="deleteByAdAccountIdAndUpdateTimeNot">
        DELETE FROM biz_fb_ad_campaigns
        WHERE ad_account_id = #{adAccountId}
        AND update_time != #{updateTime}
    </delete>
</mapper>