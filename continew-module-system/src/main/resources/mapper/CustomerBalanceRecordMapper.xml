<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CustomerBalanceRecordMapper">
    <select id="selectDashboardOverviewCustomerTransfer"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp">
        SELECT (SELECT ifnull(sum(amount), 0)
                FROM biz_customer_balance_record
                WHERE type = 1
                  and customer_id not in (select id from biz_customer where is_self_account = true)) AS total,
               (SELECT ifnull(sum(amount), 0)
                FROM biz_customer_balance_record
                WHERE type = 1
                  and customer_id not in (select id from biz_customer where is_self_account = true)
                  and trans_time >= CURDATE()
                  AND trans_time &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY))                           AS today,
               (SELECT ifnull(sum(amount), 0)
                FROM biz_customer_balance_record
                WHERE type = 1
                  and customer_id not in (select id from biz_customer where is_self_account = true)
                  and trans_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                  AND trans_time &lt; CURDATE())                                                     AS yesterday
    </select>
    <select id="selectDashboardOverviewAdAccountRecharge"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp">
        SELECT (SELECT ifnull(sum(amount), 0)
                FROM biz_customer_balance_record
                where type = 3
                  and customer_id not in (select id from biz_customer where is_self_account = true)) AS total,
               (SELECT ifnull(sum(amount), 0)
                FROM biz_customer_balance_record
                WHERE type = 3
                  and customer_id not in (select id from biz_customer where is_self_account = true)
                  AND trans_time >= CURDATE()
                  AND trans_time &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY))                           AS today,
               (SELECT ifnull(sum(amount), 0)
                FROM biz_customer_balance_record
                WHERE type = 3
                  and customer_id not in (select id from biz_customer where is_self_account = true)
                  AND trans_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                  AND trans_time &lt; CURDATE())                                                     AS yesterday
    </select>
    <select id="selectListDashboardAnalysisCustomerTransfer"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        SELECT
        DATE_FORMAT(trans_time, '%Y-%m') AS name,
        ifnull(sum(amount), 0) AS value
        FROM biz_customer_balance_record
        WHERE type = 1 and customer_id not in (select id from biz_customer where is_self_account = true) AND DATE_FORMAT(trans_time, '%Y-%m' ) IN
        <foreach collection="months" item="month" separator="," open="(" close=")">
            #{month}
        </foreach>
        GROUP BY name
        ORDER BY name
    </select>
    <select id="selectListDashboardAnalysisAdAccountRecharge"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        SELECT
        DATE_FORMAT(trans_time, '%Y-%m') AS name,
        ifnull(sum(amount), 0) AS value
        FROM biz_customer_balance_record
        WHERE type = 3 and customer_id not in (select id from biz_customer where is_self_account = true) AND DATE_FORMAT(trans_time, '%Y-%m' ) IN
        <foreach collection="months" item="month" separator="," open="(" close=")">
            #{month}
        </foreach>
        GROUP BY name
        ORDER BY name
    </select>
    <select id="selectListDashboardAnalysisTransferTimeslot"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        SELECT
        DATE_FORMAT(trans_time, '%Y-%m-%d') AS name,
        ifnull(sum(amount), 0) AS value
        FROM biz_customer_balance_record
        WHERE
        <if test="customerId != null">
            customer_id = #{customerId} AND
        </if>
        type = 1 and customer_id not in (select id from biz_customer where is_self_account = true) AND DATE_FORMAT(trans_time, '%Y-%m-%d') BETWEEN #{start} AND #{end}
        GROUP BY name
        ORDER BY name
    </select>
    <select id="selectListDashboardAnalysisRechargeTimeslot"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        SELECT
        DATE_FORMAT(trans_time, '%Y-%m-%d') AS name,
        COALESCE(sum(amount), 0) AS value
        FROM biz_customer_balance_record
        <where>
            type = 3 and customer_id not in (select id from biz_customer where is_self_account = true)
            <if test="customerId != null">
                AND customer_id = #{customerId}
            </if>
            <if test="platformAdId != null and platformAdId != ''">
                AND platform_ad_id = #{platformAdId}
            </if>
            <if test="start != null and end != null">
                AND DATE_FORMAT(trans_time, '%Y-%m-%d') BETWEEN #{start} AND #{end}
            </if>
        </where>
        GROUP BY name
        ORDER BY name
    </select>
</mapper>