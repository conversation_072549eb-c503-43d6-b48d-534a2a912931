<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.WalletTransferMapper">
    <sql id="base_query_sql">
        select t.*, c.name as customer_name
        from biz_wallet_transfer t
                 left join biz_customer_balance_record br on br.wallet_transaction_id = t.transaction_id
                 left join biz_customer c on c.id = br.customer_id
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.WalletTransferResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.WalletTransferDetailResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="getCurrentBalance" resultType="java.math.BigDecimal">
        select SUM(amount) from biz_wallet_transfer where token_symbol = 'USDT';
    </select>
</mapper>