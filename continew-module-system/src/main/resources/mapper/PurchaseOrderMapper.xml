<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.PurchaseOrderMapper">
    <sql id="base_query_sql">
        select o.*,
               bmc.name as channel_name
        from biz_purchase_order o
                 left join biz_business_manager_channel bmc on bmc.id = o.channel_id
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.PurchaseOrderResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.PurchaseOrderResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
</mapper>