<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CustomerBusinessUserMapper">
    <select id="getByCustomerId" resultType="top.continew.admin.biz.model.resp.CustomerBusinessUserResp">
        select *
        from biz_customer_business_user
        where customer_id = #{customerId}
        order by customer_id, id desc
    </select>
</mapper>