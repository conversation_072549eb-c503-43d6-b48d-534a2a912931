<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.AdAccountMapper">
    <sql id="select_page_sql">
        SELECT baa.*,
               bbmc.name                                                                                   as channelName,
               (select GROUP_CONCAT(tag_id) from biz_tag_relation where relation_id = baa.id and type = 1) as tags,
               (SELECT COUNT(id)
                FROM biz_ad_account_card
                WHERE platform_ad_id = baa.platform_ad_id
                  and is_remove = false)                                                                   AS card_count,
               if(baa.bm1_id is not null,
                  concat_ws(',', COALESCE(baa.browser_id, baa.browser_no), baa.bm1_browser, bm1.ops_browser, bm1.reserve_browser,
                            bm1.reserve_browser_bak,
                            bm1.observe_browser),
                  concat_ws(',', COALESCE(baa.browser_id, baa.browser_no), baa.bm1_browser, bm.ops_browser, bm.reserve_browser,
                            bm.reserve_browser_bak,
                            bm.observe_browser))                                                           as bm_browser,
               bm1.platform_id                                                                             as bm1_platform_id,
               (select count(id)
                from biz_test_order_item
                WHERE platform_ad_id = baa.platform_ad_id)                                                 as testOrderCount
        FROM biz_ad_account baa
                 left join biz_fb_account fa on fa.platform_account_id = baa.platform_account_id
                 left join biz_business_manager bm on bm.id = baa.business_manager_id
                 left join biz_business_manager bm1 on bm1.id = baa.bm1_id
                 left join biz_business_manager_channel bbmc on bbmc.id = baa.bm_item_channel_id
    </sql>
    <sql id="select_list_sql">
        SELECT baa.*,
               bbmc.name                                                                                   as channelName,
               (select GROUP_CONCAT(t.name) from biz_tag_relation tr left join biz_tag t on t.id = tr.tag_id where tr.relation_id = baa.id and tr.type = 1) as tags,
               (SELECT COUNT(id)
                FROM biz_ad_account_card
                WHERE platform_ad_id = baa.platform_ad_id
                  and is_remove = false)                                                                   AS card_count,
               if(baa.bm1_id is not null,
                  concat_ws(',', COALESCE(baa.browser_id, baa.browser_no), baa.bm1_browser, bm1.ops_browser, bm1.reserve_browser,
                            bm1.reserve_browser_bak,
                            bm1.observe_browser),
                  concat_ws(',', COALESCE(baa.browser_id, baa.browser_no), baa.bm1_browser, bm.ops_browser, bm.reserve_browser,
                            bm.reserve_browser_bak,
                            bm.observe_browser))                                                           as bm_browser,
               bm1.platform_id                                                                             as bm1_platform_id,
               (select count(id)
                from biz_test_order_item
                WHERE platform_ad_id = baa.platform_ad_id)                                                 as testOrderCount
        FROM biz_ad_account baa
                 left join biz_fb_account fa on fa.platform_account_id = baa.platform_account_id
                 left join biz_business_manager bm on bm.id = baa.business_manager_id
                 left join biz_business_manager bm1 on bm1.id = baa.bm1_id
                 left join biz_business_manager_channel bbmc on bbmc.id = baa.bm_item_channel_id
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.AdAccountResp">
        <include refid="select_page_sql" />
            ${ew.customSqlSegment}
    </select>
    <select id="getAdAccountInventory" resultType="top.continew.admin.biz.model.resp.AdAccountInventoryResp">
        select timezone, count(*) as num, count(IF(real_adtrust_dsl > 50 or real_adtrust_dsl = -1, 1, null)) as
        high_limit_num, count(IF(vo_status = 2, 1, null)) as vo_num
        from biz_ad_account
        where timezone != ''
        and account_status = 1
        and keep_status = 4
        and sale_status in (1, 9)
        and usable = true
        <if test="before22hourFilter">
            and bm_auth_time &lt;= DATE_SUB(CURRENT_TIME(), INTERVAL 22 HOUR)
        </if>
        group by timezone
        order by timezone
    </select>
    <select id="selectDashboardOverviewInventory"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp">
        SELECT (SELECT count(*) FROM biz_ad_account WHERE account_status = 1) AS total,
               (SELECT count(*)
                FROM biz_ad_account
                WHERE account_status = 1
                  and create_time >= CURDATE()
                  AND create_time &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY))   AS today,
               (SELECT count(*)
                FROM biz_ad_account
                WHERE account_status = 1
                  and create_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                  AND create_time &lt; CURDATE())                             AS yesterday
    </select>
    <select id="selectListDashboardAnalysisInventory"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        select timezone as name, count(*) as value
        from biz_ad_account
        where timezone != ''
          and account_status = 1
          and keep_status = 4
          and sale_status in (1, 9)
          and usable = true
          and bm_auth_time &lt;= DATE_SUB(CURRENT_TIME(), INTERVAL 22 HOUR)
        group by timezone
        order by name
    </select>

    <select id="selectInactiveAccounts" resultType="top.continew.admin.biz.model.resp.AdAccountResp">
        SELECT
            t5.name as customer_name,
            t3.platform_ad_id,
            t3.clear_status,
            t3.keep_status,
            concat_ws(',', COALESCE(t3.browser_id, t3.browser_no), t3.bm1_browser, bm.ops_browser, bm.reserve_browser, bm.reserve_browser_bak, bm.observe_browser) as bm_browser,
            t2.finish_time,
            t3.account_status,
            t3.timezone
        FROM biz_ad_account t3
                 INNER JOIN biz_ad_account_order t2 ON t3.platform_ad_id = t2.ad_account_id
                 LEFT JOIN biz_customer t5 ON t5.id = t2.customer_id
                 left join biz_business_manager bm on bm.id = COALESCE(t3.bm1_id, t3.business_manager_id)
                 LEFT JOIN (
            SELECT
                ad_account_id,
                customer_id,
                SUM(spend) AS total_spend
            FROM biz_ad_account_insight
            WHERE stat_date BETWEEN CURDATE() - INTERVAL 7 DAY AND CURDATE()
            GROUP BY ad_account_id, customer_id
        ) t4 ON t4.ad_account_id = t2.ad_account_id AND t4.customer_id = t2.customer_id
        WHERE t2.status = 3 AND t3.account_status = 1 AND COALESCE(t4.total_spend, 0) = 0 and t2.finish_time <![CDATA[<]]> (CURDATE() - INTERVAL 7 DAY)
        <if test="query.customerId!= null">
            AND t2.customer_id = #{query.customerId}
        </if>
        <if test="query.platformAdId!= null">
            AND t2.ad_account_id = #{query.platformAdId}
        </if>
        <if test="query.clearStatus != null">
            AND t3.clear_status = #{query.clearStatus}
        </if>
        <if test="query.timezone!= null and query.timezone.length!=0">
            AND t3.timezone = #{query.timezone}
        </if>
    </select>
    <select id="selectCountInactiveAccounts" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM biz_ad_account t3
        INNER JOIN biz_ad_account_order t2 ON t3.platform_ad_id = t2.ad_account_id
        LEFT JOIN biz_customer t5 ON t5.id = t2.customer_id
        LEFT JOIN (
        SELECT
        ad_account_id,
        customer_id,
        SUM(spend) AS total_spend
        FROM biz_ad_account_insight
        WHERE stat_date BETWEEN CURDATE() - INTERVAL 7 DAY AND CURDATE()
        GROUP BY ad_account_id, customer_id
        ) t4 ON t4.ad_account_id = t2.ad_account_id AND t4.customer_id = t2.customer_id
        WHERE t2.status = 3 AND t3.account_status = 1 AND COALESCE(t4.total_spend, 0) = 0 and t2.finish_time <![CDATA[<]]> (CURDATE() - INTERVAL 7 DAY)
        <if test="query.customerId!= null">
            AND t2.customer_id = #{query.customerId}
        </if>
        <if test="query.platformAdId!= null">
            AND t2.ad_account_id = #{query.platformAdId}
        </if>
        <if test="query.clearStatus != null">
            AND t3.clear_status = #{query.clearStatus}
        </if>
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.AdAccountResp">
        <include refid="select_list_sql"/>
        ${ew.customSqlSegment}
    </select>

    <update id="updateBrowserNo">
        UPDATE biz_ad_account
        set browser_no =
                CASE
                    WHEN find_in_set(${browserNo}, browser_no) THEN browser_no
                    ELSE CONCAT(browser_no, ',', ${browserNo})
                    END
        where platform_ad_id = ${platformAdId}
    </update>
    <update id="updateRemark">
        UPDATE biz_ad_account
        SET remark = CONCAT(
        #{remark},
        IF(IFNULL(remark, '') = '', '', ' '),
        IFNULL(remark, '')
        )
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectKeepStatusStat" resultType="top.continew.admin.biz.model.resp.AdAccountKeepStatusStatResp">
        select keep_status, timezone, count(*) as count
        from biz_ad_account
        where create_time between #{query.startTime} and #{query.endTime}
        group by keep_status, timezone
    </select>

    <select id="getTotalInventory" resultType="java.lang.Integer">
        SELECT count(*) FROM biz_ad_account where account_status = 1 and keep_status = 4 and usable = true
        <if test="saleStatuses!= null">
            and sale_status IN
            <foreach collection="saleStatuses" item="saleStatus" open="(" close=")" separator=",">
                #{saleStatus}
            </foreach>
        </if>
    </select>
    <select id="selectBrowserNoByPlatformAdIds" resultType="top.continew.admin.biz.model.resp.AdAccountBrowserResp">
        SELECT baa.platform_ad_id,
               if(baa.bm1_id is not null,
                  concat_ws(',', COALESCE(baa.browser_id, baa.browser_no), baa.bm1_browser, bm1.ops_browser, bm1.reserve_browser,
                            bm1.reserve_browser_bak,
                            bm1.observe_browser),
                  concat_ws(',', COALESCE(baa.browser_id, baa.browser_no), baa.bm1_browser, bm.ops_browser, bm.reserve_browser,
                            bm.reserve_browser_bak,
                            bm.observe_browser)) as browser_no
        FROM biz_ad_account baa
                 left join biz_business_manager bm on bm.id = baa.business_manager_id
                 left join biz_business_manager bm1 on bm1.id = baa.bm1_id
        WHERE baa.platform_ad_id IN
        <foreach collection="platformAdIds" item="platformAdId" open="(" separator="," close=")">
            #{platformAdId}
        </foreach>
    </select>

    <select id="selectInventoryList" resultType="top.continew.admin.biz.model.resp.AdAccountResp">
        SELECT baa.platform_ad_id,
               baa.sale_status,
               baa.appeal_status,
               baa.bm_item_type as bm_type
        FROM biz_ad_account baa
        where baa.account_status = 1
          and baa.keep_status = 4
          and baa.usable = true
          and baa.sale_status in (1, 3, 9)
    </select>
    <select id="getAdAccountKeepSuccessInventory"
            resultType="top.continew.admin.biz.model.resp.AdAccountInventoryResp">
        select timezone, count(*) as num
        from biz_ad_account
        where timezone != ''
          and account_status = 1
          and keep_status = 3
          and sale_status = 1
          and usable = true
        group by timezone
        order by timezone
    </select>

    <select id="selectWaitSaleAdAccountList" resultType="top.continew.admin.biz.model.entity.AdAccountDO">
        SELECT a.* FROM biz_ad_account a
        left join biz_business_manager b on b.id=a.business_manager_id
        WHERE a.account_status = 1
        AND a.sale_status = 1
        and usable = true
        AND a.timezone = #{timeZone}
        <choose>
            <when test="bmType != null and bmType!=-1">
                <if test="useCleanBm5 == 1">
                    AND NOT EXISTS (
                    SELECT 1 FROM biz_ad_account temp
                    WHERE temp.bm_id = a.bm_id
                    AND temp.sale_status = 2
                    )
                </if>
                AND a.keep_status = 4
                AND a.bm_auth_time &lt;= #{authTimeThreshold}
                AND b.type = #{bmType}
            </when>
            <otherwise>
                AND a.keep_status = 3
                AND a.business_manager_id is null
            </otherwise>
        </choose>
        <if test="isLowLimit != null">
            <choose>
                <when test="isLowLimit">
                    AND a.real_adtrust_dsl = 50
                </when>
                <otherwise>
                    AND (a.real_adtrust_dsl > 50 or a.real_adtrust_dsl = -1)
                </otherwise>
            </choose>
        </if>
        <if test="requireVo != null">
            <choose>
                <when test="requireVo">
                    AND a.vo_status = 2
                </when>
                <otherwise>
                    AND a.vo_status != 2
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="getExternalAccountInventory"
            resultType="top.continew.admin.biz.model.resp.AdAccountInventoryResp">
        select timezone, count(*) as num
        from biz_ad_account
        where timezone != ''
          and account_status = 1
          and keep_status = 4
          and sale_status = 9
          and usable = true
          and LENGTH(platform_ad_id) > 9
        group by timezone
        order by timezone
    </select>
    <select id="getRechargeAmountAfterClear" resultType="java.math.BigDecimal">
        SELECT (COALESCE(
                       (SELECT SUM(IF(r.type = 3, r.amount, 0)) - SUM(IF(r.type = 5, r.amount, 0))
                        FROM biz_customer_balance_record r
                        WHERE r.customer_id = (SELECT customer_id
                                               FROM biz_ad_account_order
                                               WHERE ad_account_id = #{platformAdId}
                                                 AND status = 3)
                          AND r.platform_ad_id = #{platformAdId}
                          AND r.trans_time > COALESCE(
                                (SELECT finish_time
                                 FROM biz_clear_order
                                 WHERE customer_id = (SELECT customer_id
                                                      FROM biz_ad_account_order
                                                      WHERE ad_account_id = #{platformAdId}
                                                        AND status = 3)
                                   AND platform_ad_id = #{platformAdId}
                                   AND status = 3
                                 ORDER BY finish_time DESC
                                 LIMIT 1),
                                '2024-10-01 00:00:00')), 0) +
               (SELECT COALESCE(SUM(amount), 0)
                FROM biz_recharge_order
                WHERE customer_id = (SELECT customer_id
                                     FROM biz_ad_account_order
                                     WHERE ad_account_id = #{platformAdId}
                                       AND status = 3)
                  AND platform_ad_id = #{platformAdId}
                  AND status IN (1, 2)
                  AND create_time > COALESCE(
                        (SELECT finish_time
                         FROM biz_clear_order
                         WHERE customer_id = (SELECT customer_id
                                              FROM biz_ad_account_order
                                              WHERE ad_account_id = #{platformAdId}
                                                AND status = 3)
                           AND platform_ad_id = #{platformAdId}
                           AND status = 3
                         ORDER BY finish_time DESC
                         LIMIT 1),
                        '2024-10-01 00:00:00'))) AS total_recharge_amount;
    </select>


</mapper>