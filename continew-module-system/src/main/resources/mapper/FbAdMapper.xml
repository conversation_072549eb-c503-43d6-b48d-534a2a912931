<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.FbAdMapper">

    <!-- 根据广告户ID查询广告列表 -->
    <select id="selectByAdAccountId" resultType="top.continew.admin.biz.model.entity.FbAdDO">
        SELECT *
        FROM biz_fb_ad
        WHERE ad_account_id = #{adAccountId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据广告组ID查询广告列表 -->
    <select id="selectByAdsetId" resultType="top.continew.admin.biz.model.entity.FbAdDO">
        SELECT *
        FROM biz_fb_ad
        WHERE adset_id = #{adsetId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据系列ID查询广告列表 -->
    <select id="selectByCampaignId" resultType="top.continew.admin.biz.model.entity.FbAdDO">
        SELECT *
        FROM biz_fb_ad
        WHERE campaign_id = #{campaignId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据平台ID查询广告 -->
    <select id="selectByPlatformId" resultType="top.continew.admin.biz.model.entity.FbAdDO">
        SELECT *
        FROM biz_fb_ad
        WHERE platform_id = #{platformId}
        LIMIT 1
    </select>

    <!-- 根据状态查询广告列表 -->
    <select id="selectByStatus" resultType="top.continew.admin.biz.model.entity.FbAdDO">
        SELECT *
        FROM biz_fb_ad
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 根据实际生效状态查询广告列表 -->
    <select id="selectByEffectiveStatus" resultType="top.continew.admin.biz.model.entity.FbAdDO">
        SELECT *
        FROM biz_fb_ad
        WHERE effective_status = #{effectiveStatus}
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入或更新广告 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO biz_fb_ad (
            platform_id, name, ad_account_id, status, effective_status, 
            daily_budget, lifetime_budget, bid_strategy, adset_id, 
            campaign_id, delivery_status, create_time, update_time
        ) VALUES
        <foreach collection="ads" item="ad" separator=",">
            (
                #{ad.platformId},
                #{ad.name},
                #{ad.adAccountId},
                #{ad.status},
                #{ad.effectiveStatus},
                #{ad.dailyBudget},
                #{ad.lifetimeBudget},
                #{ad.bidStrategy},
                #{ad.adsetId},
                #{ad.campaignId},
                #{ad.deliveryStatus},
                NOW(),
                #{ad.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            name = VALUES(name),
            ad_account_id = VALUES(ad_account_id),
            status = VALUES(status),
            effective_status = VALUES(effective_status),
            daily_budget = VALUES(daily_budget),
            lifetime_budget = VALUES(lifetime_budget),
            bid_strategy = VALUES(bid_strategy),
            adset_id = VALUES(adset_id),
            campaign_id = VALUES(campaign_id),
            delivery_status = VALUES(delivery_status),
            update_time = VALUES(update_time)
    </insert>

    <!-- 批量插入广告（忽略重复） -->
    <insert id="batchInsertIgnore">
        INSERT IGNORE INTO biz_fb_ad (
            platform_id, name, ad_account_id, status, effective_status, 
            daily_budget, lifetime_budget, bid_strategy, adset_id, 
            campaign_id, delivery_status, create_time, update_time
        ) VALUES
        <foreach collection="ads" item="ad" separator=",">
            (
                #{ad.platformId},
                #{ad.name},
                #{ad.adAccountId},
                #{ad.status},
                #{ad.effectiveStatus},
                #{ad.dailyBudget},
                #{ad.lifetimeBudget},
                #{ad.bidStrategy},
                #{ad.adsetId},
                #{ad.campaignId},
                #{ad.deliveryStatus},
                NOW(),
                NOW()
            )
        </foreach>
    </insert>

    <!-- 根据广告账户ID和更新时间删除不匹配的广告 -->
    <delete id="deleteByAdAccountIdAndUpdateTimeNot">
        DELETE FROM biz_fb_ad
        WHERE ad_account_id = #{adAccountId}
        AND update_time != #{updateTime}
    </delete>

     <!-- 根据广告组ID删除广告 -->
    <delete id="deleteByAdsetId">
        DELETE FROM biz_fb_ad
        WHERE ad_account_id = #{adAccountId} and adset_id = #{adsetId} and campaign_id = #{campaignId}
    </delete>
    
    <!-- 根据广告系列ID删除广告 -->
    <delete id="deleteByCampaignId">
        DELETE FROM biz_fb_ad
        WHERE ad_account_id = #{adAccountId} and campaign_id = #{campaignId}
    </delete>
</mapper>