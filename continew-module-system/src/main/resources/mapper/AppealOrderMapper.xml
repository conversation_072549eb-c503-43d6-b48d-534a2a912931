<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.AppealOrderMapper">
    <sql id="selectAppealOrder">
        select o.*,
               c.name                                as customer_name,
               a.clear_status,
               a.bm_id                               as bm_id,
               bm.status                             as bm_status,
               u.nickname                            as handle_user_name,
               concat_ws(',', COALESCE(a.browser_id, a.browser_no), a.bm1_browser, browser_bm.ops_browser, browser_bm.reserve_browser,
                         browser_bm.reserve_browser_bak,
                         browser_bm.observe_browser) as browser_no
        from biz_appeal_order o
                 left join biz_ad_account a on a.platform_ad_id = o.platform_ad_id
                 left join sys_user u on u.id = o.handle_user
                 left join biz_customer c on c.id = o.customer_id
                 left join biz_business_manager browser_bm on browser_bm.id = COALESCE(a.bm1_id, a.business_manager_id)
                 left join biz_business_manager bm on bm.id = a.business_manager_id

    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.AppealOrderResp">
        <include refid="selectAppealOrder"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.AppealOrderResp">
        <include refid="selectAppealOrder"/>
        ${ew.customSqlSegment}
    </select>
</mapper>