<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.PersonalAccountMapper">
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.PersonalAccountResp">
        SELECT pa.*,
               CASE pa.type
                   WHEN 1 THEN (SELECT COUNT(*) FROM biz_business_manager WHERE ops_browser = pa.browser_no)
                   WHEN 2 THEN (SELECT COUNT(*)
                                FROM biz_business_manager
                                WHERE reserve_browser = pa.browser_no or reserve_browser_bak = pa.browser_no)
                   WHEN 3 THEN (SELECT COUNT(*) FROM biz_business_manager WHERE observe_browser = pa.browser_no)
                   ELSE 0
                   END  AS bmNum,
               bmc.name as channel_name
        FROM biz_personal_account pa
                 left join biz_business_manager_channel bmc on bmc.id = pa.channel_id
            ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.PersonalAccountResp">
        SELECT pa.*,
               CASE pa.type
                   WHEN 1 THEN (SELECT COUNT(*) FROM biz_business_manager WHERE ops_browser = pa.browser_no)
                   WHEN 2 THEN (SELECT COUNT(*)
                                FROM biz_business_manager
                                WHERE reserve_browser = pa.browser_no or reserve_browser_bak = pa.browser_no)
                   WHEN 3 THEN (SELECT COUNT(*) FROM biz_business_manager WHERE observe_browser = pa.browser_no)
                   ELSE 0
                   END  AS bmNum,
               bmc.name as channel_name
        FROM biz_personal_account pa
                 left join biz_business_manager_channel bmc on bmc.id = pa.channel_id
            ${ew.customSqlSegment}
    </select>
</mapper>