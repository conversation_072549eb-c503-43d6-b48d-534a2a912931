<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.TestOrderItemMapper">
    <select id="listByAdAccountId" resultType="top.continew.admin.biz.model.resp.AdAccountTestOrderItemResp">
        select bto.title as orderName,
               bto.trello_url,
               btoi.status,
               btoi.remark
        from biz_test_order_item btoi
                 left join biz_test_order bto on bto.id = btoi.order_id
        where btoi.platform_ad_id = #{adAccountId}
    </select>
</mapper>