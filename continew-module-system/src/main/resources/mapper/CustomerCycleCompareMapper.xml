<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.biz.mapper.CustomerCycleCompareMapper">

    <!-- 获取客户基础统计数据 -->
   <select id="selectCustomerBasicData" resultType="top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp">
    SELECT 
        c.id as customerId,
        c.create_time as createTime,
        COALESCE(COUNT(DISTINCT aao.ad_account_id), 0) as totalAccounts,
        COALESCE(COUNT(DISTINCT CASE WHEN aa.account_status = 1 and aao.status=3 THEN aao.ad_account_id END), 0) as totalNormalAccounts,
        COALESCE(COUNT(DISTINCT CASE WHEN aa.account_status = 2 THEN aao.ad_account_id END), 0) as totalDeadAccounts,
        COALESCE(COUNT(DISTINCT CASE WHEN aao.status = 5 THEN aao.ad_account_id END), 0) as totalRecycledAccounts
    FROM biz_customer c
    LEFT JOIN biz_ad_account_order aao ON c.id = aao.customer_id
    LEFT JOIN biz_ad_account aa ON aao.ad_account_id = aa.platform_ad_id
    WHERE c.id IN
    <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
        #{customerId}
    </foreach>
    <if test="query.startTime != null and query.endTime != null">
        AND aao.finish_time >= #{query.startTime}
        AND aao.finish_time &lt;= #{query.endTime}
    </if>
   AND aao.status in (3,5)
   GROUP BY c.id
</select>
    
    <!-- 获取客户空置户数据 -->
    <select id="selectCustomerIdleData" resultType="top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp">
        SELECT 
            c.id as customerId,
            COALESCE(COUNT(DISTINCT CASE WHEN csd.card_spent &lt; 10 THEN csd.ad_account_id END), 0) as totalIdleAccounts
        FROM biz_customer c
        LEFT JOIN (
            WITH account_spent AS (
                SELECT customer_id,
                       ad_account_id,
                       COALESCE(-SUM(trans_amount), 0) as card_spent
                FROM biz_card_transaction
                WHERE trans_status != 4
                  AND stat_time >= DATE_SUB(CURDATE(), INTERVAL <if test="query.idleDays != null">#{query.idleDays}</if><if test="query.idleDays == null">7</if> DAY)
                  AND customer_id IS NOT NULL
                GROUP BY customer_id, ad_account_id
            )
            SELECT o.customer_id,
                   o.ad_account_id,
                   COALESCE(account_spent.card_spent, 0) AS card_spent
            FROM biz_ad_account_order o
            LEFT JOIN biz_ad_account a ON a.platform_ad_id = o.ad_account_id
            LEFT JOIN account_spent ON account_spent.ad_account_id = o.ad_account_id 
                                    AND account_spent.customer_id = o.customer_id
            WHERE o.status = 3 AND a.account_status = 1
              AND o.finish_time &lt; DATE_SUB(CURDATE(), INTERVAL <if test="query.idleDays != null">#{query.idleDays}</if><if test="query.idleDays == null">7</if> DAY)
        ) csd ON c.id = csd.customer_id
        WHERE c.id IN
        <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        GROUP BY c.id
    </select>

    <!-- 获取客户消耗数据 -->
    <select id="selectCustomerSpentData" resultType="top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp">
        SELECT 
            ct.customer_id as customerId,
            COALESCE(ABS(SUM(ct.trans_amount)), 0) as totalSpent
        FROM biz_card_transaction ct

        WHERE ct.trans_status != 4 AND ct.customer_id IN
        <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        <if test="query.startTime != null and query.endTime != null">
            AND ct.stat_time >= #{query.startTime}
            AND ct.stat_time &lt;= #{query.endTime}
        </if>
        GROUP BY ct.customer_id
    </select>

    <!-- 获取客户智能近七天平均单天消耗数据 -->
    <select id="selectCustomerSmartRecentSpentData" resultType="top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp">
        WITH customer_last_spent AS (
            -- 获取每个客户最后一笔消耗的时间
            SELECT 
                customer_id,
                MAX(stat_time) as last_spent_date
            FROM biz_card_transaction
            WHERE trans_status != 4 
            AND customer_id IN
            <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                #{customerId}
            </foreach>
            GROUP BY customer_id
        ),
        customer_recent_spent AS (
            -- 基于最后消耗时间倒推7天计算消耗
            SELECT 
                ct.customer_id,
                COALESCE(ABS(SUM(ct.trans_amount)), 0) as total_spent
            FROM biz_card_transaction ct
            INNER JOIN customer_last_spent cls ON ct.customer_id = cls.customer_id
            WHERE ct.trans_status != 4
            AND ct.stat_time >= DATE_SUB(cls.last_spent_date, INTERVAL 6 DAY)
            AND ct.stat_time &lt;= cls.last_spent_date
            GROUP BY ct.customer_id
        )
        SELECT 
            customer_id as customerId,
            ROUND(total_spent / 7, 2) as averageDaySpent
        FROM customer_recent_spent
    </select>


    <!-- 按消耗区间统计广告户数量 -->
    <select id="selectAccountSpentRangeDistribution" resultType="map">
        SELECT 
            spend_range,
            COUNT(*) AS account_count
        FROM (
            SELECT 
                ct.ad_account_id AS adAccountId,
                COALESCE(ABS(SUM(ct.trans_amount)), 0) AS spent,
                <foreach collection="spentRanges" item="range" separator=" " open="CASE" close="END AS spend_range">
                    <if test="range.max != null">
                        WHEN COALESCE(ABS(SUM(ct.trans_amount)), 0) >= #{range.min} AND COALESCE(ABS(SUM(ct.trans_amount)), 0) &lt; #{range.max} THEN #{range.label}
                    </if>
                    <if test="range.max == null">
                        WHEN COALESCE(ABS(SUM(ct.trans_amount)), 0) >= #{range.min} THEN #{range.label}
                    </if>
                </foreach>
            FROM biz_card_transaction ct
            WHERE ct.customer_id = #{customerId}
                AND ct.trans_status != 4
                AND ct.stat_time >= #{startDate}
                AND ct.stat_time &lt;= #{endDate}
            GROUP BY ct.ad_account_id
        ) tmp
        WHERE spend_range IS NOT NULL
        GROUP BY spend_range
    </select>

    <!-- 批量获取客户基础统计数据 -->
    <select id="selectBatchCustomerBasicData" resultType="top.continew.admin.biz.model.resp.CustomerCycleCompareBatchResp">
        SELECT 
            c.id as customerId,
            #{period} as period,
            c.create_time as createTime,
            COALESCE(COUNT(DISTINCT aao.ad_account_id), 0) as totalAccounts,
            COALESCE(COUNT(DISTINCT CASE WHEN aa.account_status=1 and aao.status = 3 THEN aao.ad_account_id END), 0) as totalNormalAccounts,
            COALESCE(COUNT(DISTINCT CASE WHEN aa.account_status = 2 THEN aao.ad_account_id END), 0) as totalDeadAccounts,
            COALESCE(COUNT(DISTINCT CASE WHEN aao.status = 5 THEN aao.ad_account_id END), 0) as totalRecycledAccounts
        FROM biz_customer c
        LEFT JOIN biz_ad_account_order aao ON c.id = aao.customer_id
        LEFT JOIN biz_ad_account aa ON aao.ad_account_id = aa.platform_ad_id
        WHERE c.id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND aao.finish_time >= #{startDate}
        AND aao.finish_time &lt;= #{endDate}
        AND aao.status in (3,5)
        GROUP BY c.id
    </select>

    <!-- 批量获取客户消耗数据 -->
    <select id="selectBatchCustomerSpentData" resultType="top.continew.admin.biz.model.resp.CustomerCycleCompareBatchResp">
        SELECT 
            ct.customer_id as customerId,
            #{period} as period,
            COALESCE(ABS(SUM(ct.trans_amount)), 0) as totalSpent
        FROM biz_card_transaction ct
        WHERE ct.trans_status != 4 
        AND ct.customer_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND ct.stat_time >= #{startDate}
        AND ct.stat_time &lt;= #{endDate}
        GROUP BY ct.customer_id
    </select>

    <!-- 批量获取账户消耗区间分布 -->
    <select id="selectBatchAccountSpentRangeDistribution" resultType="map">
        SELECT 
            customer_id as customerId,
            #{period} as period,
            spend_range,
            COUNT(*) AS account_count
        FROM (
            SELECT 
                ct.customer_id,
                ct.ad_account_id AS adAccountId,
                COALESCE(ABS(SUM(ct.trans_amount)), 0) AS spent,
                <foreach collection="spentRanges" item="range" separator=" " open="CASE" close="END AS spend_range">
                    <if test="range.max != null">
                        WHEN COALESCE(ABS(SUM(ct.trans_amount)), 0) >= #{range.min} AND COALESCE(ABS(SUM(ct.trans_amount)), 0) &lt; #{range.max} THEN #{range.label}
                    </if>
                    <if test="range.max == null">
                        WHEN COALESCE(ABS(SUM(ct.trans_amount)), 0) >= #{range.min} THEN #{range.label}
                    </if>
                </foreach>
            FROM biz_card_transaction ct
            WHERE ct.customer_id IN
            <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                #{customerId}
            </foreach>
            AND ct.trans_status != 4
            AND ct.stat_time >= #{startDate}
            AND ct.stat_time &lt;= #{endDate}
            GROUP BY ct.customer_id, ct.ad_account_id
        ) tmp
        WHERE spend_range IS NOT NULL
        GROUP BY tmp.customer_id, spend_range
    </select>
</mapper>