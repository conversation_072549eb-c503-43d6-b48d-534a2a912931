<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.BusinessManagerItemMapper">
    <sql id="base_sql">
        SELECT bbm.platform_id AS businessManager,
               bbmc.name       AS businessManagerChannel,
               a.sale_status,
               a.sale_time     AS saleTime,
               bbmi.*
        FROM biz_business_manager_item bbmi
                 LEFT JOIN biz_business_manager bbm ON bbmi.business_manager_id = bbm.id
                 LEFT JOIN biz_business_manager_channel bbmc ON bbmi.channel_id = bbmc.id
                 LEFT JOIN biz_ad_account a ON bbmi.platform_ad_id = a.platform_ad_id
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.BusinessManagerItemResp">
        <include refid="base_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.BusinessManagerItemResp">
        <include refid="base_sql"/>
        ${ew.customSqlSegment}
    </select>
</mapper>