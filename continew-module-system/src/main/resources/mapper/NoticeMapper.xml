<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.system.mapper.NoticeMapper">

    <select id="selectDashboardList"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardNoticeResp">
        SELECT
            id, title, type
        FROM sys_notice
        WHERE (effective_time IS NULL OR NOW() > effective_time)
        AND (terminate_time IS NULL OR terminate_time > NOW())
        <if test="userId != null">
            AND (notice_scope = 1 OR (notice_scope = 2 AND JSON_EXTRACT(notice_users, "$[0]") = CAST(#{userId} AS CHAR)))
        </if>
        ORDER BY sort ASC, effective_time DESC
        LIMIT 5
    </select>
</mapper>