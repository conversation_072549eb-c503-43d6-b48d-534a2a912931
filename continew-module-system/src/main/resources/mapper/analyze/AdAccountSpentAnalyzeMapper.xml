<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.analyze.AdAccountSpentAnalyzeMapper">

    <select id="selectPageCustomerBase" resultType="top.continew.admin.biz.model.resp.CustomerSpentResp">
        WITH adAccountOrders AS (SELECT
                                     *
                                 FROM
                                     (
                                         SELECT
                                             aao.id,
                                             aao.ad_account_id,
                                             aao.customer_id,
                                             aao.first_start_campaign_time,
                                             ROW_NUMBER() OVER ( PARTITION BY aao.customer_id,aao.ad_account_id ORDER BY aao.id DESC ) AS rn
                                         FROM
                                             biz_ad_account_order aao
                                         WHERE
                                             aao.STATUS IN ( 3, 5 )
                                            <if test="query.startTime != null and query.endTime != null">
                                                AND aao.finish_time BETWEEN #{query.startTime} AND #{query.endTime}
                                            </if>
                                            <if test="query.customerIds != null and query.customerIds.size() > 0">
                                                AND aao.customer_id IN
                                                <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
                                                    #{customerId}
                                                </foreach>
                                            </if>
                                     ) AS latest_orders
                                 WHERE
                                     rn = 1)
        select
            c.id as customerId,
            c.name as customerName,
            count(ao.id) as totalSubAccounts
        from adAccountOrders ao
        join biz_customer c on c.id = ao.customer_id
        group by c.id
</select>

    <select id="selectAdUsageCount" resultType="java.lang.Integer">
        SELECT COUNT( CASE WHEN first_start_campaign_time IS NOT NULL THEN 1 END ) as adUsageCount
        FROM (
            SELECT 
            aao.ad_account_id,
            aao.customer_id,
            aao.first_start_campaign_time,
            aao.status,
            ROW_NUMBER() OVER (
                PARTITION BY <if test="null != customerId">aao.customer_id,</if> aao.ad_account_id
                ORDER BY aao.id DESC
            ) as rn
            FROM biz_ad_account_order aao
            WHERE aao.status in (3, 5)
              <if test="customerId != null">
                  AND aao.customer_id = #{customerId}
              </if>
              <if test="startTime != null and endTime != null">
                  AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
              </if>
              <if test="adAccountIds != null and adAccountIds.size() > 0">
                  AND aao.ad_account_id IN
                  <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                      #{item}
                  </foreach>
              </if>
        ) latest_orders
        WHERE rn = 1
    </select>


    <select id="selectAllSpentCount" resultType="top.continew.admin.biz.model.resp.CustomerSpentStatisticsResp">
        -- 查询出符合条件的下户订单的消耗金额
        WITH LatestOrders AS (
            SELECT 
            aao.customer_id, 
            aao.ad_account_id,
            aao.first_start_campaign_time,
            aao.status,
            aao.recycle_time,
            aa.account_status,
            ROW_NUMBER() OVER (
                PARTITION BY <if test="null != customerId">aao.customer_id,</if> aao.ad_account_id
                ORDER BY aao.id DESC
            ) as rn
            FROM biz_ad_account_order aao
            LEFT JOIN biz_ad_account aa ON aao.ad_account_id = aa.platform_ad_id
            WHERE  aao.status IN (3,5)
            <if test="customerId != null">
                AND aao.customer_id = #{customerId}
            </if>
            <if test="startTime != null and endTime != null">
                AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="adAccountIds != null and adAccountIds.size() > 0">
                AND aao.ad_account_id IN
                <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        ),
        CustomerTotalSpend AS (
            SELECT 
            lo.customer_id, 
            lo.ad_account_id,
            lo.account_status,
            (SELECT IFNULL(SUM(aai.spend), 0)
                FROM biz_ad_account_insight aai
                WHERE aai.ad_account_id = lo.ad_account_id AND aai.customer_id = lo.customer_id
                AND aai.stat_date > DATE(DATE_ADD(lo.first_start_campaign_time, INTERVAL #{day} DAY))
                AND aai.stat_date &lt;=
                    CASE
                        WHEN lo.status = 5 THEN DATE(lo.recycle_time)
                        WHEN lo.status = 3 THEN DATE(NOW())
                    END) AS totalSpend
            FROM LatestOrders lo
            WHERE lo.rn = 1 AND lo.first_start_campaign_time IS NOT NULL
        )
        SELECT
        -- 不消耗数：统计 totalSpend 为 0 且过滤掉封禁广告户的下户订单数
        COUNT(CASE WHEN totalSpend = 0 AND account_status !=2 THEN 1 END) AS nonSpendingCount,
        -- 低消耗数：统计大于0 totalSpend 小于amount 的下户订单数
        COUNT(CASE WHEN totalSpend > 0 AND totalSpend &lt; #{amount} THEN 1 END) AS lowSpendingCount,
        -- 正常消耗数：统计 totalSpend >= amount 的下户订单数
        COUNT(CASE WHEN totalSpend >= #{amount} THEN 1 END) AS normalSpendingCount
        FROM CustomerTotalSpend
    </select>

    <select id="getCustomerDashboardStats"
            resultType="top.continew.admin.biz.model.resp.CustomerSpentStatisticsResp">
        WITH adAccountOders AS (
        SELECT
        *
        FROM
        (
        SELECT
        aao.id,
        aao.ad_account_id,
        aao.customer_id,
        aao.status,
        aao.recycle_time,
        aa.account_status,
        aao.first_start_campaign_time,
        ROW_NUMBER() OVER ( PARTITION BY aao.customer_id,aao.ad_account_id ORDER BY aao.id DESC ) AS rn
        FROM
        biz_ad_account_order aao
        LEFT JOIN biz_ad_account aa ON aao.ad_account_id = aa.platform_ad_id
        <where>
            AND aao.status IN (3, 5)
            <if test="query.customerIds != null and query.customerIds.size() > 0">
                AND aao.customer_id IN
                <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.endTime != null">
                AND aao.finish_time BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
        </where>
        ) AS latest_orders
        WHERE
        rn = 1
        ),
        CustomerTotalSpend AS (
        SELECT
        ao.customer_id,
        ao.ad_account_id,
        ao.account_status,
        (
        SELECT
        IFNULL( SUM( aai.spend ), 0 )
        FROM
        biz_ad_account_insight aai
        WHERE
        aai.ad_account_id = ao.ad_account_id
        AND aai.customer_id = ao.customer_id
        AND aai.stat_date > DATE(
        DATE_ADD( ao.first_start_campaign_time, INTERVAL #{day} DAY ))
        AND aai.stat_date &lt;=
        CASE

        WHEN ao.`status` = 5 THEN
        DATE( ao.recycle_time )
        WHEN ao.`status` = 3 THEN
        DATE(
        NOW())
        END
        ) AS totalSpend
        FROM
        adAccountOders ao
        WHERE
        ao.first_start_campaign_time IS NOT NULL
        ) SELECT
        COUNT( ao.id ) AS totalSubAccounts,
        COUNT( CASE WHEN ao.first_start_campaign_time IS NOT NULL THEN 1 END ) AS adUsageCount,
        COUNT( CASE WHEN ct.totalSpend = 0 AND ct.account_status!=2 THEN 1 END ) AS nonSpendingCount,
        COUNT( CASE WHEN ct.totalSpend > 0 AND totalSpend &lt; #{amount} THEN 1 END ) AS lowSpendingCount,
        COUNT( CASE WHEN ct.totalSpend >= #{amount} THEN 1 END ) AS normalSpendingCount
        FROM
        adAccountOders ao
        LEFT JOIN CustomerTotalSpend ct ON ct.ad_account_id = ao.ad_account_id
        AND ct.customer_id = ao.customer_id
    </select>




    <select id="selectAdIdByCardHeader" resultType="java.lang.String">
        WITH LatestOrders AS (
            SELECT 
            aao.ad_account_id,
            ROW_NUMBER() OVER (
                PARTITION BY <if test="null != customerId">aao.customer_id,</if> aao.ad_account_id
                ORDER BY aao.id DESC
            ) as rn
        FROM biz_ad_account_order aao
        LEFT JOIN biz_card c ON c.platform_ad_id = aao.ad_account_id
        WHERE aao.status IN (3, 5)
          AND c.card_number LIKE CONCAT(#{cardHeader}, '%')
            <if test="adAccountIds != null and adAccountIds.size() > 0">
                AND aao.ad_account_id IN
                <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerId != null">
                AND aao.customer_id = #{customerId}
            </if>
            <if test="startTime != null and endTime != null">
                AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
            </if>
    )
    SELECT ad_account_id
    FROM LatestOrders
    WHERE rn = 1
</select>

    <select id="selectAdIdByTimeZone" resultType="java.lang.String">
        WITH LatestOrders AS (
            SELECT 
            aao.ad_account_id,
            ROW_NUMBER() OVER (
                PARTITION BY <if test="null != customerId">aao.customer_id,</if> aao.ad_account_id
                ORDER BY aao.id DESC
            ) as rn
        FROM biz_ad_account_order aao
        LEFT JOIN biz_ad_account aa ON aa.platform_ad_id = aao.ad_account_id
        WHERE aao.status IN (3, 5)
          AND aa.timezone = #{timezone}
            <if test="adAccountIds != null and adAccountIds.size() > 0">
                AND aao.ad_account_id IN
                <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerId != null">
                AND aao.customer_id = #{customerId}
            </if>
            <if test="startTime != null and endTime != null">
                AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
            </if>
    )
    SELECT ad_account_id
    FROM LatestOrders
    WHERE rn = 1
</select>

    <select id="selectAdIdByFailTrans" resultType="java.lang.String">
        WITH LatestOrders AS (
            SELECT 
            aao.ad_account_id,
            aao.customer_id,
            ROW_NUMBER() OVER (
                PARTITION BY <if test="null != customerId">aao.customer_id,</if> aao.ad_account_id
                ORDER BY aao.id DESC
            ) as rn
        FROM biz_ad_account_order aao
        WHERE aao.status IN (3, 5)
            <if test="customerId != null">
                AND aao.customer_id = #{customerId}
            </if>
            <if test="adAccountIds != null and adAccountIds.size() > 0">
                AND aao.ad_account_id IN
                <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null and endTime != null">
                AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
            </if>
    )
    SELECT  t.ad_account_id
    FROM biz_card_transaction t
    INNER JOIN LatestOrders lo ON lo.ad_account_id = t.ad_account_id AND lo.customer_id = t.customer_id
    JOIN (
        SELECT customer_id, ad_account_id, MAX(stat_time) AS latest_stat_time
        FROM biz_card_transaction
        WHERE trans_status = 4
          <if test="customerId != null">
              AND customer_id = #{customerId}
          </if>
            <if test="adAccountIds != null and adAccountIds.size() > 0">
                AND ad_account_id IN
                <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY customer_id, ad_account_id
    ) m ON t.ad_account_id = m.ad_account_id AND t.stat_time = m.latest_stat_time AND t.customer_id = m.customer_id
    WHERE t.trans_status = 4 AND lo.rn = 1
        <if test="customerId != null">
            AND t.customer_id = #{customerId}
        </if>
        <if test="adAccountIds != null and adAccountIds.size() > 0">
            AND t.ad_account_id IN
            <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
</select>

    <select id="selectAdIdByOneDollar" resultType="java.lang.String">
        WITH LatestOrders AS (
            SELECT 
            aao.ad_account_id,
            ROW_NUMBER() OVER (
                PARTITION BY <if test="null != customerId">aao.customer_id,</if> aao.ad_account_id
                ORDER BY aao.id DESC
            ) as rn
        FROM biz_ad_account_order aao
        WHERE aao.status IN (3, 5)
          AND aao.is_one_dollar = 1
            <if test="customerId != null">
                AND aao.customer_id = #{customerId}
            </if>
            <if test="adAccountIds != null and adAccountIds.size() > 0">
                AND aao.ad_account_id IN
                <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null and endTime != null">
                AND aao.finish_time BETWEEN #{startTime} AND #{endTime}
            </if>
    )
    SELECT ad_account_id
    FROM LatestOrders
    WHERE rn = 1
</select>

<select id="selectLatestAdAccountOrdersByAdIds" resultType="top.continew.admin.biz.model.entity.AdAccountOrderDO">
    WITH LatestOrders AS (
    SELECT
    aao.ad_account_id,aao.finish_time,aao.status,aao.customer_id,aao.recycle_time,aao.id,
    ROW_NUMBER() OVER (
        PARTITION BY aao.ad_account_id
        ORDER BY aao.id DESC
    ) as rn
    FROM biz_ad_account_order aao
    WHERE aao.status IN (3, 5)
    <if test="adAccountIds != null and adAccountIds.size() > 0">
        AND aao.ad_account_id IN
        <foreach item="item" index="index" collection="adAccountIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    )
    SELECT ad_account_id,finish_time
    FROM LatestOrders
    WHERE rn = 1
</select>


</mapper>