<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.biz.mapper.crm.CustomerAccountRelMapper">

    <select id="selectByAccountIdAndEntityTypeAndEntityId" resultType="top.continew.admin.biz.model.entity.crm.CustomerAccountRelDO">
        SELECT *
        FROM biz_customer_account_rel
        WHERE customer_account_id = #{accountId}
          AND entity_type = #{entityType}
          AND entity_id = #{entityId}
        LIMIT 1
    </select>

    <update id="updateOtherRelNotPrimary">
        UPDATE biz_customer_account_rel
        SET is_primary = 0
        WHERE entity_type = #{entityType}
          AND entity_id = #{entityId}
          AND customer_account_id != #{accountId}
    </update>

    <select id="countByAccountId" resultType="int">
        SELECT COUNT(1)
        FROM biz_customer_account_rel
        WHERE customer_account_id = #{accountId}
    </select>

    <select id="selectAccountsByEntityTypeAndEntityId" resultType="top.continew.admin.biz.model.resp.crm.CustomerAccountResp">
        SELECT r.id as rel_id, r.entity_type, r.entity_id,
               a.id as account_id, a.account_type, a.account, a.social_account_id,s.account as social_account
        FROM biz_customer_account_rel r
        LEFT JOIN biz_customer_account a ON r.customer_account_id = a.id
        LEFT JOIN biz_social_account s on s.id=a.social_account_id
        WHERE r.entity_type = #{entityType}
          AND r.entity_id = #{entityId}
    </select>

</mapper>