<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.crm.CustomerVisitTaskMapper">
    <select id="selectTaskPage" resultType="top.continew.admin.biz.model.entity.crm.CustomerVisitTaskDO">
        SELECT l.*
        FROM biz_customer_visit_task l
        LEFT JOIN sys_user ur ON l.assignee_id = ur.id
        <where>
            <if test="query.assigneeId != null">
                AND l.assignee_id = #{query.assigneeId}
            </if>
            <if test="query.taskStatus != null">
                AND l.task_status = #{query.taskStatus}
            </if>
            <if test="query.customerId != null">
                AND l.customer_id = #{query.customerId}
            </if>
        </where>
        ORDER BY l.id DESC
    </select>
</mapper>