<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.crm.CustomerVisitTaskDetailMapper">
    
    <select id="selectByTaskId" resultType="top.continew.admin.biz.model.entity.crm.CustomerVisitTaskDetailDO">
        SELECT *
        FROM biz_customer_visit_task_detail
        WHERE task_id = #{taskId}
        ORDER BY visit_time DESC
    </select>
    
    <delete id="deleteByTaskId">
        DELETE FROM biz_customer_visit_task_detail
        WHERE task_id = #{taskId}
    </delete>
    
</mapper>