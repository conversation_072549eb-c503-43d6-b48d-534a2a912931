<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.biz.mapper.crm.CustomerAccountMapper">

    <select id="selectByAccountAndType" resultType="top.continew.admin.biz.model.entity.crm.CustomerAccountDO">
        SELECT *
        FROM biz_customer_account
        WHERE account = #{account}
          AND account_type = #{accountType}
        LIMIT 1
    </select>

</mapper>