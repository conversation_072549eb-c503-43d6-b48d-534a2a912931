<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.crm.LeadMapper">
    <update id="updateLongTermFollowUp">
        update biz_lead set status=3,remind_time=#{remindTime} where status=2
    </update>

    <select id="selectLeadPage" resultType="top.continew.admin.biz.model.entity.crm.LeadDO">
        SELECT l.*
        FROM biz_lead l
        LEFT JOIN sys_user ur ON l.handler_user_id = ur.id
        <where>
            <if test="query.sourceId != null">
                AND l.source_id = #{query.sourceId}
            </if>
            <if test="query.status != null">
                AND l.status = #{query.status}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND l.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerIndustry != null">
                AND l.customer_industry = #{query.customerIndustry}
            </if>
            <if test="query.invalidReason != null">
                AND l.invalid_reason = #{query.invalidReason}
            </if>
            <if test="query.opportunityId != null">
                AND l.opportunity_id = #{query.opportunityId}
            </if>
            <if test="query.handlerUserId != null">
                AND l.handler_user_id = #{query.handlerUserId}
            </if>
            <if test="query.createTime != null and query.createTime.length == 2">
                AND l.create_time BETWEEN #{query.createTime[0]} AND #{query.createTime[1]}
            </if>
            <if test="query.lastFollowTime != null and query.lastFollowTime.length == 2">
                AND l.last_follow_time BETWEEN #{query.lastFollowTime[0]} AND #{query.lastFollowTime[1]}
            </if>
        </where>
        ORDER BY l.id DESC
    </select>

    <!-- 统计待跟进线索数量 -->
    <select id="countPendingLeads" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM biz_lead
        WHERE handler_user_id = #{handlerUserId}
        AND status = 1
    </select>

    <!-- 优化后的今日需跟进线索数量统计 -->
    <select id="countTodayFollowLeads" resultType="java.lang.Long">
        SELECT 
            COALESCE(
                (SELECT COUNT(*) FROM biz_lead 
                 WHERE handler_user_id = #{handlerUserId}
                 AND status = 2
                 AND last_follow_time IS NOT NULL
                 AND DATE_ADD(last_follow_time, INTERVAL #{pendingOverHour} HOUR) BETWEEN #{todayStart} AND #{todayEnd}), 0
            ) +
            COALESCE(
                (SELECT COUNT(*) FROM biz_lead 
                 WHERE handler_user_id = #{handlerUserId}
                 AND remind_time IS NOT NULL
                 AND remind_time BETWEEN #{todayStart} AND #{todayEnd}), 0
            ) AS total_count
    </select>
    
    <!-- 优化后的超时线索数量统计 -->
    <select id="countOverdueLeads" resultType="java.lang.Long">
        SELECT 
            COALESCE(
                (SELECT COUNT(*) FROM biz_lead 
                 WHERE handler_user_id = #{handlerUserId}
                 AND status = 2
                 AND last_follow_time IS NOT NULL
                 AND DATE_ADD(last_follow_time, INTERVAL #{pendingOverHour} HOUR) &lt; #{currentTime}), 0
            ) +
            COALESCE(
                (SELECT COUNT(*) FROM biz_lead 
                 WHERE handler_user_id = #{handlerUserId}
                 AND remind_time IS NOT NULL
                 AND remind_time &lt; #{currentTime}), 0
            ) AS total_count
    </select>

</mapper>