<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.crm.CustomerVisitStrategyMapper">

    <select id="findCustomerIdsByBasicConditions" resultType="java.lang.Long">
        SELECT DISTINCT c.id
        FROM biz_customer c
        <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
            INNER JOIN biz_customer_tag ct ON c.id = ct.customer_id
        </if>
        <where>
            <if test="basicCondition.customerType != null">
                AND c.type = #{basicCondition.customerType}
            </if>
            <if test="basicCondition.startDate != null">
                AND c.create_time >= #{basicCondition.startDate}
            </if>
            <if test="basicCondition.endDate != null">
                AND c.create_time &lt; #{basicCondition.endDate}
            </if>
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                AND ct.tag_id IN
                <foreach collection="basicCondition.salesTagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
            <if test="basicCondition.isRefund != null">
                AND c.status = 2
            </if>
        </where>
    </select>

    <!-- 根据广告户数查询客户ID和户数（支持基础条件合并） -->
    <select id="findCustomersByAdAccountCountWithBasicConditions" resultType="top.continew.admin.biz.model.resp.CustomerConditionResp">
        SELECT 
            ac.customer_id as customerId,
            'adAccountCount' as conditionType,
            ac.account_count as conditionValue,
            CONCAT('广告户数量:', ac.account_count) as conditionDescription
        FROM (
            SELECT aao.customer_id, COUNT(*) as account_count
            FROM biz_ad_account_order aao
            INNER JOIN biz_customer bc ON aao.customer_id = bc.id
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                INNER JOIN biz_customer_tag bct ON bc.id = bct.customer_id
            </if>
            WHERE aao.status = 3
            <if test="basicCondition.customerType != null">
                AND bc.type = #{basicCondition.customerType}
            </if>
            <!-- 基础条件：创建时间 -->
            <if test="basicCondition.startDate != null">
                AND bc.create_time >= #{basicCondition.startDate}
            </if>
            <if test="basicCondition.endDate != null">
                AND bc.create_time &lt; #{basicCondition.endDate}
            </if>
            <!-- 基础条件：销售标签 -->
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                AND bct.tag_id IN
                <foreach collection="basicCondition.salesTagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
            <!-- 基础条件：退款客户 -->
            <if test="basicCondition.isRefund != null and basicCondition.isRefund">
                AND (SELECT 1 FROM biz_refund_order ro WHERE ro.customer_id = bc.id AND ro.status = 3)
            </if>
            <!-- 过滤条件 -->
            <if test="filterCustomerIds != null and filterCustomerIds.size() > 0">
                AND aao.customer_id IN
                <foreach collection="filterCustomerIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            GROUP BY aao.customer_id
        ) ac
        <where>
            <!-- 高级条件：广告户数 -->
            <if test="minCount != null">
                ac.account_count >= #{minCount}
            </if>
            <if test="maxCount != null">
                <if test="minCount != null">AND</if>
                ac.account_count &lt;= #{maxCount}
            </if>
        </where>
    </select>

    <!-- 根据正常户数量查询客户ID和户数（支持基础条件合并） -->
    <select id="findCustomersByNormalAccountCountWithBasicConditions" resultType="top.continew.admin.biz.model.resp.CustomerConditionResp">
        SELECT 
            nac.customer_id as customerId,
            'normalAccountCount' as conditionType,
            nac.normal_account_count as conditionValue,
            CONCAT('正常户数量:', nac.normal_account_count) as conditionDescription
        FROM (
            SELECT o.customer_id, COUNT(DISTINCT a.id) as normal_account_count
            FROM biz_ad_account_order o
            INNER JOIN biz_ad_account a ON o.ad_account_id = a.platform_ad_id
            INNER JOIN biz_customer bc ON o.customer_id = bc.id
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                INNER JOIN biz_customer_tag bct ON bc.id = bct.customer_id
            </if>
            WHERE o.status = 3
            AND a.account_status = 1
            <if test="basicCondition.customerType != null">
                AND bc.type = #{basicCondition.customerType}
            </if>
            <!-- 基础条件：创建时间 -->
            <if test="basicCondition.startDate != null">
                AND bc.create_time >= #{basicCondition.startDate}
            </if>
            <if test="basicCondition.endDate != null">
                AND bc.create_time &lt; #{basicCondition.endDate}
            </if>
            <!-- 基础条件：销售标签 -->
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                AND bct.tag_id IN
                <foreach collection="basicCondition.salesTagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
            <!-- 基础条件：退款客户 -->
            <if test="basicCondition.isRefund != null and basicCondition.isRefund">
                AND (SELECT 1 FROM biz_refund_order ro WHERE ro.customer_id = bc.id AND ro.status = 3)
            </if>
            <!-- 过滤条件 -->
            <if test="filterCustomerIds != null and filterCustomerIds.size() > 0">
                AND o.customer_id IN
                <foreach collection="filterCustomerIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            GROUP BY o.customer_id
        ) nac
        <where>
            <!-- 高级条件：正常户数量 -->
            <if test="minCount != null">
                nac.normal_account_count >= #{minCount}
            </if>
            <if test="maxCount != null">
                <if test="minCount != null">AND</if>
                nac.normal_account_count &lt;= #{maxCount}
            </if>
        </where>
    </select>

    <select id="findCustomerIdsByEmptyAccountCountWithBasicConditions" resultType="top.continew.admin.biz.model.resp.CustomerConditionResp">
        WITH account_spent AS (
            SELECT customer_id, ad_account_id, COALESCE(-SUM(trans_amount), 0) as card_spent
            FROM biz_card_transaction FORCE INDEX (idx_trans_time_status)
            WHERE trans_satus != 4
            AND stat_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            AND customer_id IS NOT NULL
            <if test="filterCustomerIds != null and filterCustomerIds.size() > 0">
                AND customer_id IN
                <foreach collection="filterCustomerIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            GROUP BY customer_id, ad_account_id
        ),
        customer_empty_accounts AS (
            SELECT o.customer_id, COUNT(*) as empty_account_count
            FROM biz_ad_account_order o FORCE INDEX (idx_status_finish_time)
            INNER JOIN biz_customer bc ON o.customer_id = bc.id
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                INNER JOIN biz_customer_tag bct ON bc.id = bct.customer_id
            </if>
            LEFT JOIN biz_ad_account a ON a.platform_ad_id = o.ad_account_id
            LEFT JOIN account_spent ON account_spent.ad_account_id = o.ad_account_id
            AND account_spent.customer_id = o.customer_id
            WHERE o.status = 3
            AND o.finish_time &lt; DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            AND a.account_status = 1
            AND COALESCE(account_spent.card_spent, 0) &lt; 10
            AND bc.status = 1
            <if test="basicCondition.customerType != null">
                AND bc.type = #{basicCondition.customerType}
            </if>
            <!-- 基础条件：创建时间 -->
            <if test="basicCondition.startDate != null">
                AND bc.create_time >= #{basicCondition.startDate}
            </if>
            <if test="basicCondition.endDate != null">
                AND bc.create_time &lt; #{basicCondition.endDate}
            </if>
            <!-- 基础条件：销售标签 -->
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                AND bct.tag_id IN
                <foreach collection="basicCondition.salesTagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
            <!-- 基础条件：退款客户 -->
            <if test="basicCondition.isRefund != null and basicCondition.isRefund">
                AND (SELECT 1 FROM biz_refund_order ro WHERE ro.customer_id = bc.id AND ro.status = 3)
            </if>
            <!-- 过滤条件 -->
            <if test="filterCustomerIds != null and filterCustomerIds.size() > 0">
                AND o.customer_id IN
                <foreach collection="filterCustomerIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            GROUP BY o.customer_id
        )
        SELECT cea.customer_id as customerId,
        'emptyAccountCount' as conditionType,
        CAST(cea.empty_account_count AS CHAR) as conditionValue,
        CONCAT('空置户数量:', cea.empty_account_count) as conditionDescription
        FROM customer_empty_accounts cea
        <where>
            <!-- 高级条件：空置户数量 -->
            <if test="minCount != null">
                cea.empty_account_count >= #{minCount}
            </if>
            <if test="maxCount != null">
                <if test="minCount != null">AND</if>
                cea.empty_account_count &lt;= #{maxCount}
            </if>
        </where>
    </select>

    <!-- 根据消耗额区间查询客户ID列表（支持基础条件合并） -->
    <select id="findCustomersByConsumptionAmountWithBasicConditions" resultType="top.continew.admin.biz.model.resp.CustomerConditionResp">
        SELECT
        tc.customer_id as customerId,
        'consumptionAmount' as conditionType,
        CAST(tc.total_consumption AS CHAR) as conditionValue,
        CONCAT('消耗金额:', ROUND(tc.total_consumption, 2)) as conditionDescription
        FROM (
        SELECT bct.customer_id, -SUM(bct.trans_amount) as total_consumption
        FROM biz_card_transaction bct
        INNER JOIN biz_customer bc ON bct.customer_id = bc.id
        <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
            INNER JOIN biz_customer_tag bctag ON bc.id = bctag.customer_id
        </if>
        WHERE bct.trans_status != 4
        <if test="basicCondition.customerType != null">
            AND bc.type = #{basicCondition.customerType}
        </if>
        <!-- 基础条件：创建时间 -->
        <if test="basicCondition.startDate != null">
            AND bc.create_time >= #{basicCondition.startDate}
        </if>
        <if test="basicCondition.endDate != null">
            AND bc.create_time &lt; #{basicCondition.endDate}
        </if>
        <!-- 基础条件：销售标签 -->
        <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
            AND bctag.tag_id IN
            <foreach collection="basicCondition.salesTagIds" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
        </if>
        <!-- 基础条件：退款客户 -->
        <if test="basicCondition.isRefund != null and basicCondition.isRefund">
            AND (SELECT 1 FROM biz_refund_order ro WHERE ro.customer_id = bc.id AND ro.status = 3)
        </if>
        <!-- 过滤条件 -->
        <if test="filterCustomerIds != null and filterCustomerIds.size() > 0">
            AND bct.customer_id IN
            <foreach collection="filterCustomerIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <!-- 消耗时间范围 -->
        <if test="startDate != null">
            AND DATE(bct.stat_time) >= #{startDate}
        </if>
        <if test="endDate != null">
            AND DATE(bct.stat_time) &lt; #{endDate}
        </if>
        GROUP BY bct.customer_id
        ) tc
        <where>
            <!-- 高级条件：消耗额区间 -->
            <if test="minAmount != null">
                tc.total_consumption >= #{minAmount}
            </if>
            <if test="maxAmount != null">
                <if test="minAmount != null">AND</if>
                tc.total_consumption &lt;= #{maxAmount}
            </if>
        </where>
    </select>
    
    <!-- 获取指定时间段内客户的消耗数据（用于趋势分析） -->
    <select id="findCustomersConsumptionByPeriod" resultType="top.continew.admin.biz.model.resp.CustomerConditionResp">
        SELECT
            tc.customer_id as customerId,
            'consumptionTrend' as conditionType,
            CAST(tc.total_consumption AS CHAR) as conditionValue,
            CONCAT('消耗金额:', ROUND(tc.total_consumption, 2)) as conditionDescription
        FROM (
            SELECT bct.customer_id, -SUM(bct.trans_amount) as total_consumption
            FROM biz_card_transaction bct
            INNER JOIN biz_customer bc ON bct.customer_id = bc.id
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                INNER JOIN biz_customer_tag bctag ON bc.id = bctag.customer_id
            </if>
            WHERE bct.trans_status != 4
            <if test="basicCondition.customerType != null">
                AND bc.type = #{basicCondition.customerType}
            </if>
            <!-- 基础条件：创建时间 -->
            <if test="basicCondition.startDate != null">
                AND bc.create_time >= #{basicCondition.startDate}
            </if>
            <if test="basicCondition.endDate != null">
                AND bc.create_time &lt; #{basicCondition.endDate}
            </if>
            <!-- 基础条件：销售标签 -->
            <if test="basicCondition.salesTagIds != null and basicCondition.salesTagIds.size() > 0">
                AND bctag.tag_id IN
                <foreach collection="basicCondition.salesTagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
            <!-- 基础条件：退款客户 -->
            <if test="basicCondition.isRefund != null and basicCondition.isRefund">
                AND (SELECT 1 FROM biz_refund_order ro WHERE ro.customer_id = bc.id AND ro.status = 3)
            </if>
            <!-- 过滤条件 -->
            <if test="filterCustomerIds != null and filterCustomerIds.size() > 0">
                AND bct.customer_id IN
                <foreach collection="filterCustomerIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <!-- 消耗时间范围 -->
            AND DATE(bct.stat_time) >= #{startDate}
            AND DATE(bct.stat_time) &lt; #{endDate}
            GROUP BY bct.customer_id
        ) tc
    </select>
    
    </mapper>