<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CustomerDailyStatMapper">
    <sql id="base_query_sql">
        select stat.*,
               ROUND(LEAST(stat.today_used_account / stat.total_normal_account, 1), 4) as used_rate,
               c.name                                                                            as customer_name,
               u.nickname                                                                        as business_user_name
        from biz_customer_daily_stat stat
                 left join biz_customer c on c.id = stat.customer_id
                 left join sys_user u on u.id = c.business_user_id
    </sql>
    <sql id="base_select_daily_query_sql">
        select stat_date,
               SUM(total_account)                                                                as total_account,
               SUM(total_finish_account)                                                         as total_finish_account,
               SUM(total_normal_account)                                                         as total_normal_account,
               SUM(today_used_account)                                                           as today_used_account,
               CONCAT(FORMAT(SUM(today_used_account) / SUM(total_normal_account) * 100, 2), '%') as used_rate,
               SUM(today_ban_account)                                                            as today_ban_account,
               SUM(today_open_account)                                                           as today_open_account,
               SUM(today_card_spent)                                                             as today_card_spent
        from biz_customer_daily_stat
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.CustomerDailyStatResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.CustomerDailyStatDetailResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectDailyPage" resultType="top.continew.admin.biz.model.resp.CustomerDailyStatByDateResp">
        <include refid="base_select_daily_query_sql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectDailyList" resultType="top.continew.admin.biz.model.resp.CustomerDailyStatByDateResp">
        <include refid="base_select_daily_query_sql"/>
        ${ew.customSqlSegment}
    </select>
</mapper>