<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.AgentRebateMapper">
    <sql id="base_query_sql">
        select ar.*, a.name as agent_name, u.nickname as business_user_name, c.name as customer_name
        from biz_agent_rebate ar
                 left join biz_agent a on a.id = ar.agent_id
                 left join sys_user u on u.id = a.business_user_id
                 left join biz_customer c on c.id = ar.customer_id
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.AgentRebateResp">
        <include refid="base_query_sql" />
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.AgentRebateDetailResp">
        <include refid="base_query_sql"/>
        ${ew.customSqlSegment}
    </select>
</mapper>