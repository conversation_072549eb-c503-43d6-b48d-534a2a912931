<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.BusinessManagerMapper">
    <sql id="baseSelectSql">
        SELECT bm.*,
               (select count(*)
                from biz_ad_account ad
                where ad.business_manager_id = bm.id) as use_num,
               (select count(*)
                from biz_ad_account ad
                where ad.bm1_id = bm.id)              as use_black_num,
               COALESCE(bmi.use_item_num, 0)          AS use_item_num,
               EXISTS (
                   SELECT 1
                   FROM biz_ad_account ad
                   WHERE ad.business_manager_id = bm.id AND ad.real_adtrust_dsl = 50
               ) AS `drop`
        FROM biz_business_manager bm

                 LEFT JOIN (SELECT business_manager_id, COUNT(*) AS use_item_num
                            FROM biz_business_manager_item
                            WHERE is_use = true
                            GROUP BY business_manager_id) bmi ON bmi.business_manager_id = bm.id ${ew.customSqlSegment}
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.BusinessManagerResp">
        <include refid="baseSelectSql"/>
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.BusinessManagerResp">
        <include refid="baseSelectSql"/>
    </select>
</mapper>