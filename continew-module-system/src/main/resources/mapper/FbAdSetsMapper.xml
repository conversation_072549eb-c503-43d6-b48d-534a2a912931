<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.FbAdSetsMapper">

    <!-- 根据广告户ID查询广告组列表 -->
    <select id="selectByAdAccountId" resultType="top.continew.admin.biz.model.entity.FbAdSetsDO">
        SELECT *
        FROM biz_fb_ad_sets
        WHERE ad_account_id = #{adAccountId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据系列ID查询广告组列表 -->
    <select id="selectByCampaignId" resultType="top.continew.admin.biz.model.entity.FbAdSetsDO">
        SELECT *
        FROM biz_fb_ad_sets
        WHERE campaign_id = #{campaignId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据平台ID查询广告组 -->
    <select id="selectByPlatformId" resultType="top.continew.admin.biz.model.entity.FbAdSetsDO">
        SELECT *
        FROM biz_fb_ad_sets
        WHERE platform_id = #{platformId}
        LIMIT 1
    </select>

    <!-- 根据状态查询广告组列表 -->
    <select id="selectByStatus" resultType="top.continew.admin.biz.model.entity.FbAdSetsDO">
        SELECT *
        FROM biz_fb_ad_sets
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入或更新广告组 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO biz_fb_ad_sets (
            platform_id, name, ad_account_id, status, daily_budget, 
            lifetime_budget, bid_strategy, campaign_id, delivery_status, optimization_goal, custom_event_type, create_time, update_time
        ) VALUES
        <foreach collection="adSets" item="adSet" separator=",">
            (
                #{adSet.platformId},
                #{adSet.name},
                #{adSet.adAccountId},
                #{adSet.status},
                #{adSet.dailyBudget},
                #{adSet.lifetimeBudget},
                #{adSet.bidStrategy},
                #{adSet.campaignId},
                #{adSet.deliveryStatus},
                #{adSet.optimizationGoal},
                #{adSet.customEventType},
                NOW(),
                #{adSet.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            name = VALUES(name),
            ad_account_id = VALUES(ad_account_id),
            status = VALUES(status),
            daily_budget = VALUES(daily_budget),
            lifetime_budget = VALUES(lifetime_budget),
            bid_strategy = VALUES(bid_strategy),
            campaign_id = VALUES(campaign_id),
            delivery_status = VALUES(delivery_status),
            optimization_goal = VALUES(optimization_goal),
            custom_event_type = VALUES(custom_event_type),
            update_time = VALUES(update_time)
    </insert>

    <!-- 批量插入广告组（忽略重复） -->
    <insert id="batchInsertIgnore">
        INSERT IGNORE INTO biz_fb_ad_sets (
            platform_id, name, ad_account_id, status, daily_budget, 
            lifetime_budget, bid_strategy, campaign_id, delivery_status, optimization_goal, custom_event_type, create_time, update_time
        ) VALUES
        <foreach collection="adSets" item="adSet" separator=",">
            (
                #{adSet.platformId},
                #{adSet.name},
                #{adSet.adAccountId},
                #{adSet.status},
                #{adSet.dailyBudget},
                #{adSet.lifetimeBudget},
                #{adSet.bidStrategy},
                #{adSet.campaignId},
                #{adSet.deliveryStatus},
                #{adSet.optimizationGoal},
                #{adSet.customEventType},
                NOW(),
                NOW()
            )
        </foreach>
    </insert>

    <!-- 根据广告账户ID和更新时间删除不匹配的广告组 -->
    <delete id="deleteByAdAccountIdAndUpdateTimeNot">
        DELETE FROM biz_fb_ad_sets
        WHERE ad_account_id = #{adAccountId}
        AND update_time != #{updateTime}
    </delete>

    <!-- 根据广告系列ID删除广告组 -->
    <delete id="deleteByCampaignId">
        DELETE FROM biz_fb_ad_sets
        WHERE campaign_id = #{campaignId} and ad_account_id = #{adAccountId}
    </delete>
</mapper>