<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CustomerRequirementMapper">
<select id="getSummary" resultType="java.util.Map">
    SELECT 
        timezone,
        SUM(quantity) as count
    FROM biz_customer_requirement
    <where>
        <if test="query.customerId != null">
            AND customer_id = #{query.customerId}
        </if>
        <if test="query.timezone != null and query.timezone != ''">
            AND timezone = #{query.timezone}
        </if>
        <if test="query.requirementTime != null">
            AND requirement_time BETWEEN #{query.requirementTime[0]} AND #{query.requirementTime[1]}
        </if>
        <if test="query.createTime != null">
            AND create_time BETWEEN #{query.createTime[0]} AND #{query.createTime[1]}
        </if>
        <if test="query.remark != null and query.remark != ''">
            AND remark LIKE CONCAT('%', #{query.remark}, '%')
        </if>

        <if test="query.handleUser != null">
            AND handle_user = #{query.handleUser}
        </if>
        <if test="query.status != null">
            AND status = #{query.status}
        </if>
    </where>
    GROUP BY timezone
</select>
</mapper>