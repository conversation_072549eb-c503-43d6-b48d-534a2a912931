<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.biz.mapper.CustomerStatisticsMapper">


    <sql id="STAT_SQL">
        select customer_id as id,
        name,
        businessUserName,
        count(DISTINCT ad_account_id) as totalAccount,
        SUM(IF(account_status = 2, 1, 0)) as deadAccount,
        SUM(pay_amount) as payAmount,
        SUM(total_spent) as totalSpend,
        SUM(recharge_amount) as totalRecharge,
        SUM(IF(!enable_prepay and clear_status != 3 and status = 3, (spend_cap - amount_spent), 0)) +
        SUM(IF(enable_prepay and clear_status != 3 and status = 3, (recharge_amount - all_spent), 0)) + balance as
        occupiedAmount,
        SUM(IF(!enable_prepay and clear_status != 3 and status = 3, (spend_cap - amount_spent), 0)) +
        SUM(IF(enable_prepay and clear_status != 3 and status = 3, (recharge_amount - all_spent), 0)) as fbBalance,
        SUM(card_spent) as cardSpent,
        SUM(card_balance) as cardBalance,
        ROUND(SUM(IF(account_status = 2, 1, 0)) / count(*) * 100, 2) as deadRate,
        ROUND(SUM(total_spent) / count(*), 2) as averageFbSpend,
        ROUND(SUM(card_spent) / count(*), 2) as averageCardSpend,
        ROUND(SUM(recharge_amount) / count(*), 2) as averageRecharge
        from (select o.customer_id,
        o.clear_status,
        c.name,
        c.balance,
        o.enable_prepay,
        o.total_spent as all_spent,
        u.nickname as businessUserName,
        o.pay_amount,
        o.ad_account_id,
        a.account_status,
        o.status,
        a.spend_cap, a.amount_spent,
        (select COALESCE(sum(IF(r.type = 3, r.amount, 0)), 0) -
        COALESCE(sum(IF(r.type in (4, 5), r.amount, 0)), 0)
        from biz_customer_balance_record r
        where r.platform_ad_id = o.ad_account_id
        and r.customer_id = o.customer_id
        <choose>
            <when test="query.statTimes != null">
                AND r.trans_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
            </when>
            <otherwise>
                AND r.trans_time >= o.pay_time
                AND (o.status != 5 OR r.trans_time &lt;=
                o.recycle_time)
            </otherwise>
        </choose>
        ) as recharge_amount,
        (select COALESCE(SUM(i.spend), 0) from biz_ad_account_insight i
        where i.customer_id = o.customer_id and i.ad_account_id = o.ad_account_id
        <choose>
            <when test="query.statTimes != null">
                AND i.stat_date BETWEEN DATE(GREATEST(#{query.statTimes[0]}, o.finish_time)) AND
                DATE(LEAST(#{query.statTimes[1]}, ifnull(o.recycle_time,
                #{query.statTimes[1]})))
            </when>
            <otherwise>
                AND i.stat_date >= DATE (o.finish_time)
                AND (o.status != 5 OR i.stat_date &lt;=
                DATE (o.recycle_time))
            </otherwise>
        </choose>
        ) as total_spent,
        (SELECT COALESCE(-SUM(t.trans_amount), 0)
        FROM biz_card_transaction t
        WHERE t.ad_account_id = o.ad_account_id
        AND t.trans_status != 4
        <choose>
            <when test="query.statTimes != null">
                AND t.stat_time <![CDATA[>=]]> GREATEST(#{query.statTimes[0]}, o.finish_time)
                AND t.stat_time <![CDATA[<=]]> LEAST(#{query.statTimes[1]}, ifnull(o.recycle_time,
                #{query.statTimes[1]}))
            </when>
            <otherwise>
                AND t.stat_time >= o.finish_time
                AND (o.status != 5 OR t.stat_time &lt;=
                o.recycle_time)
            </otherwise>
        </choose>
        ) AS card_spent,
        (select COALESCE(SUM(c.balance), 0)
        from biz_card c
        where c.platform_ad_id = o.ad_account_id
        and o.status = 3) AS card_balance from biz_ad_account_order o
        left join biz_ad_account a
        on a.platform_ad_id = o.ad_account_id
        LEFT JOIN biz_customer c
        ON c.id = o.customer_id
        LEFT JOIN sys_user u ON c.business_user_id = u.id
        where o.status in (3, 5) and c.type = 1 and c.is_self_account = false
        <if test="query.customerId != null">
            AND o.customer_id = #{query.customerId}
        </if>
        <if test="query.timezone != null and query.timezone != ''">
            AND a.timezone = #{query.timezone}
        </if>
        <if test="query.businessUserId != null">
            AND c.business_user_id = #{query.businessUserId}
        </if>
        <if test="query.orderTimes != null">
            AND o.finish_time BETWEEN #{query.orderTimes[0]} AND #{query.orderTimes[1]}
        </if>
        ) customer_order_stat
        group by customer_id
        order by ${query.sort[0]} ${query.sort[1]}
    </sql>


    <select id="selectStatisticsWithCondition" resultType="top.continew.admin.biz.model.resp.CustomerStatisticsResp">
        <include refid="STAT_SQL"/>
    </select>


    <select id="selectCustomerStatisticsList"
            resultType="top.continew.admin.biz.model.resp.CustomerStatisticsResp">
        <include refid="STAT_SQL"/>
    </select>


    <select id="selectStatisticsWithoutCondition" resultType="top.continew.admin.biz.model.resp.CustomerStatisticsResp">
        <include refid="STAT_SQL"/>
    </select>

    <sql id="selectBusinessStatisticsSql">
        SELECT
        u.id,
        u.nickname,
        -- 累计客户数
        (
        SELECT COUNT(DISTINCT c.id)
        FROM biz_customer c
        WHERE c.business_user_id = u.id
        ) AS total_customer,
        -- 总打款
        (
        SELECT COALESCE(SUM(cbr.amount), 0)
        FROM biz_customer_balance_record cbr
        LEFT JOIN biz_customer c ON cbr.customer_id = c.id
        WHERE c.business_user_id = u.id AND cbr.type = 1
        <if test="query.statTimes != null">
            AND cbr.trans_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
        </if>
        ) AS total_paid,
        -- 总充值
        (
        SELECT COALESCE(sum(IF(cbr.type = 3, cbr.amount, 0)), 0) - COALESCE(sum(IF(cbr.type in (4, 5), cbr.amount, 0)),
        0)
        FROM biz_customer_balance_record cbr
        LEFT JOIN biz_customer c ON cbr.customer_id = c.id
        WHERE c.business_user_id = u.id
        <if test="query.statTimes != null">
            AND cbr.trans_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
        </if>
        ) AS total_recharge,
        -- 总消耗
        (
        SELECT COALESCE(SUM(ai.spend), 0)
        FROM biz_ad_account_insight ai
        LEFT JOIN biz_customer c ON ai.customer_id = c.id
        WHERE c.business_user_id = u.id
        <if test="query.statTimes != null">
            AND ai.stat_date BETWEEN date(#{query.statTimes[0]}) AND date(#{query.statTimes[1]})
        </if>
        ) AS total_spend,
        -- 死户数量
        (
        SELECT COUNT(DISTINCT a.platform_ad_id)
        FROM biz_ad_account_order ao
        LEFT JOIN biz_ad_account a ON ao.ad_account_id = a.platform_ad_id
        LEFT JOIN biz_customer c ON ao.customer_id = c.id
        WHERE c.business_user_id = u.id AND ao.status = 3 AND a.account_status = 2
        <if test="query.statTimes != null">
            AND ao.finish_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
        </if>
        ) AS dead_account,
        -- 占用资金
        (
        SELECT COALESCE(SUM(aa.spend_cap), 0) - COALESCE(SUM(aa.amount_spent), 0)
        FROM biz_ad_account aa
        LEFT JOIN biz_ad_account_order ao ON ao.ad_account_id = aa.platform_ad_id
        LEFT JOIN biz_customer c ON ao.customer_id = c.id
        WHERE c.business_user_id = u.id AND ao.status = 3 AND ao.clear_status != 3
        ) AS occupied_amount,
        -- 总户数
        (
        SELECT COUNT(DISTINCT ao.ad_account_id)
        FROM biz_ad_account_order ao
        LEFT JOIN biz_customer c ON ao.customer_id = c.id
        WHERE c.business_user_id = u.id AND ao.status = 3
        <if test="query.statTimes != null">
            AND ao.finish_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
        </if>
        ) AS total_account,
        -- 新增客户数（排除有退过款的）
        (
        SELECT COUNT(DISTINCT c.id)
        FROM biz_customer c
        WHERE c.business_user_id = u.id
        <if test="query.statTimes != null">
            AND c.create_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
        </if>
        AND NOT EXISTS (
        SELECT 1 FROM biz_refund_order ro
        WHERE ro.customer_id = c.id AND ro.status = 3
        )
        ) AS total_new_customer,
        -- 退款客户数
        (
        SELECT COUNT(DISTINCT ro.customer_id)
        FROM biz_refund_order ro
        LEFT JOIN biz_customer c ON ro.customer_id = c.id
        WHERE c.business_user_id = u.id
        AND ro.status = 3
        <if test="query.statTimes != null">
            AND ro.finish_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
        </if>
        ) AS total_refund_customer
        FROM sys_user u
        WHERE u.id != 1 AND exists(select 1 from biz_customer c where c.business_user_id=u.id)

        <if test="query.businessUserId != null">
            AND u.id = #{query.businessUserId}
        </if>
        <if test="null != query.sortField and query.sortField.length!=0">
            ORDER BY ${query.sortField}
            <choose>
                <when test="null != query.ascSortFlag and query.ascSortFlag">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        </if>
    </sql>


    <select id="selectBusinessStatistics" resultType="top.continew.admin.biz.model.resp.BusinessStatisticsResp">
        <include refid="selectBusinessStatisticsSql" />
    </select>


    <select id="selectStatisticsOverviewWithoutCondition" resultType="top.continew.admin.biz.model.resp.CustomerStatisticsOverviewResp">
        SELECT
            id,
            SUM(total_account) as totalAccount,
            SUM(dead_account) as deadAccount,
            SUM(total_spend) as totalSpend,
            SUM(total_paid) as totalPaid,
            SUM(occupied_amount) as occupiedAmount,
            SUM(total_recharge) as totalRecharge,
            SUM(card_balance) as cardBalance
        FROM (<include refid="STAT_SQL"/>) AS customer_data
        <where>
            <if test="query.customerId != null">
                AND id = #{query.customerId}
            </if>
        </where>
    </select>

<select id="selectCustomerStatisticsSummary" resultType="top.continew.admin.biz.model.resp.CustomerStatisticsOverviewResp">
    select
    count(*) as totalAccount,
    SUM(IF(account_status = 2, 1, 0)) as deadAccount,
    SUM(pay_amount) as payAmount,
    SUM(total_spent) as totalSpend,
    SUM(recharge_amount) as totalRecharge,
    SUM(IF(!enable_prepay and clear_status != 3 and status = 3, (spend_cap - amount_spent), 0)) + SUM(IF(enable_prepay
    and clear_status != 3 and status = 3, (recharge_amount - all_spent), 0)) as occupiedAmount,
    SUM(card_spent) as cardSpent,
    SUM(card_balance) as cardBalance,
    ROUND(SUM(IF(account_status = 2, 1, 0)) / count(*) * 100, 2) as deadRate,
    ROUND(SUM(total_spent) / count(*), 2) as averageFbSpend,
    ROUND(SUM(card_spent) / count(*), 2) as averageCardSpend,
    ROUND(SUM(recharge_amount) / count(*), 2) as averageRecharge
    from (select o.customer_id,
    c.name,
    c.balance,
    o.enable_prepay,
    o.status,
    o.total_spent as all_spent,
    u.nickname as businessUserName,
    o.pay_amount,
    o.ad_account_id,
    a.account_status,
    o.clear_status,
    a.spend_cap, a.amount_spent,
    (select COALESCE(sum(IF(r.type = 3, r.amount, 0)), 0) -
    COALESCE(sum(IF(r.type in (4, 5), r.amount, 0)), 0)
    from biz_customer_balance_record r
    where r.platform_ad_id = o.ad_account_id
    and r.customer_id = o.customer_id
    <if test="query.statTimes != null">
        AND r.trans_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
    </if>
    ) as recharge_amount,
    (select COALESCE(SUM(i.spend), 0) from biz_ad_account_insight i
    where i.customer_id = o.customer_id and i.ad_account_id = o.ad_account_id
    <if test="query.statTimes != null">
        AND i.stat_date BETWEEN DATE(#{query.statTimes[0]}) AND DATE(#{query.statTimes[1]})
    </if>
    ) as total_spent,
    (SELECT COALESCE(-SUM(t.trans_amount), 0)
    FROM biz_card_transaction t
    WHERE t.ad_account_id = o.ad_account_id
    AND t.trans_status != 4
    <choose>
        <when test="query.statTimes != null">
            AND t.stat_time <![CDATA[>=]]> GREATEST(#{query.statTimes[0]}, o.finish_time)
            AND t.stat_time <![CDATA[<=]]> LEAST(#{query.statTimes[1]}, ifnull(o.recycle_time,
            #{query.statTimes[1]}))
        </when>
        <otherwise>
            AND t.stat_time >= o.finish_time
            AND (o.status != 5 OR t.stat_time &lt;=
            o.recycle_time)
        </otherwise>
    </choose>
    ) AS card_spent,
    (select COALESCE(SUM(c.balance), 0)
    from biz_card c
    where c.platform_ad_id = o.ad_account_id
    and o.status = 3) AS card_balance from biz_ad_account_order o
    left join biz_ad_account a
    on a.platform_ad_id = o.ad_account_id
    LEFT JOIN biz_customer c
    ON c.id = o.customer_id
    LEFT JOIN sys_user u ON c.business_user_id = u.id
    where o.status in (3, 5) and c.type = 1 and c.is_self_account = false
    <if test="query.customerId != null">
        AND o.customer_id = #{query.customerId}
    </if>
    <if test="query.timezone != null and query.timezone != ''">
        AND a.timezone = #{query.timezone}
    </if>
    <if test="query.businessUserId != null">
        AND c.business_user_id = #{query.businessUserId}
    </if>
    <if test="query.orderTimes != null">
        AND o.finish_time BETWEEN #{query.orderTimes[0]} AND #{query.orderTimes[1]}
    </if>
    ) customer_order_stat
</select>
    <select id="selectBusinessStatisticsList"
            resultType="top.continew.admin.biz.model.resp.BusinessStatisticsResp">
        <include refid="selectBusinessStatisticsSql" />
    </select>
    <select id="selectCustomerSpentTrendList"
            resultType="top.continew.admin.biz.model.resp.CustomerSpentTrendResp">

        with customer_date_spent as (select customer_id,
                                            DATE(stat_time)    as stat_time,
                                            -SUM(trans_amount) as trans_amount
                                     from biz_card_transaction
                                     where customer_id is not null
                                       and trans_status != 4
                                       and stat_time >= DATE_SUB(CURDATE(), INTERVAL 2 DAY)
                                     group by customer_id, DATE(stat_time))

        select c.name,
               COALESCE((select trans_amount
                         from customer_date_spent spent
                         where spent.customer_id = c.id
                           and spent.stat_time = CURDATE()), 0)                           as today_spent,
               COALESCE((select trans_amount
                         from customer_date_spent spent
                         where spent.customer_id = c.id
                           and spent.stat_time = DATE_SUB(CURDATE(), INTERVAL 1 DAY)), 0) as yesterday_spent,
               COALESCE((select trans_amount
                         from customer_date_spent spent
                         where spent.customer_id = c.id
                           and spent.stat_time = DATE_SUB(CURDATE(), INTERVAL 2 DAY)), 0) as day_before_yesterday_spent
        from biz_customer c
            ${ew.customSqlSegment}
    </select>
</mapper>
