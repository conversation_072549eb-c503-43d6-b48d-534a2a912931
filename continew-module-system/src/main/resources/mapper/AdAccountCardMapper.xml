<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.AdAccountCardMapper">
    <select id="listByPlatformAdId" resultType="top.continew.admin.biz.model.resp.AdAccountCardResp">
        select adc.*, c.balance, c.used_amount, c.id as card_id, c.status as card_status
        from biz_ad_account_card adc
                 left join biz_card c on c.card_number = adc.full_card_number where adc.platform_ad_id = #{platformAdId}
    </select>
</mapper>