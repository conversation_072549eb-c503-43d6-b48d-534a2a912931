<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.ClearOrderMapper">
    <sql id="selectRechargeOrder">
        select o.*,
               c.name                                                                 as customer_name,
               a.bm_id                                                                as bm_id,
               u.nickname                                                             as handle_user_name,
               concat_ws(',', COALESCE(a.browser_id, a.browser_no), a.bm1_browser, bm.ops_browser, bm.reserve_browser, bm.reserve_browser_bak, bm.observe_browser) as browser_no
        from biz_clear_order o
                 left join biz_ad_account a on a.platform_ad_id = o.platform_ad_id
                 left join sys_user u on u.id = o.handle_user
                 left join biz_customer c on c.id = o.customer_id
                 left join biz_business_manager bm on bm.id = COALESCE(a.bm1_id, a.business_manager_id)

    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.ClearOrderResp">
        <include refid="selectRechargeOrder"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.ClearOrderResp">
        <include refid="selectRechargeOrder"/>
        ${ew.customSqlSegment}
    </select>
    <!-- 清零账户分析查询 -->
        <select id="selectClearAccountAnalyze" resultType="top.continew.admin.biz.model.resp.ClearAccountAnalyzeResp">
            SELECT
            a.customer_id,
            c.name AS customer_name,
            b.platform_ad_id,
            d.account_status,
            d.timezone,
            b.status,
            b.finish_time,
            DATEDIFF(NOW(), b.finish_time) AS sleep_days
            FROM biz_ad_account_order a
            INNER JOIN (
                SELECT
                b1.customer_id,
                b1.platform_ad_id,
                b1.status,
                b1.finish_time
                FROM biz_clear_order b1
                WHERE b1.status = 3
                    AND b1.id = (
                        SELECT MAX(b2.id)
                        FROM biz_clear_order b2
                        WHERE b2.customer_id = b1.customer_id
                        AND b2.platform_ad_id = b1.platform_ad_id
                    )
            ) b ON b.customer_id = a.customer_id AND b.platform_ad_id = a.ad_account_id
            INNER JOIN biz_customer c ON a.customer_id = c.id
            INNER JOIN biz_ad_account d ON d.platform_ad_id = a.ad_account_id
            LEFT JOIN biz_recharge_order ro ON ro.customer_id = a.customer_id AND ro.platform_ad_id = a.ad_account_id AND ro.status = 3 AND ro.finish_time > b.finish_time
            <where>
                a.status = 3 AND ro.id IS NULL
                <if test="query.customerId != null">
                    AND a.customer_id = #{query.customerId}
                </if>
                <if test="query.platformAdId != null and query.platformAdId != ''">
                    AND a.ad_account_id = #{query.platformAdId}
                </if>
                <if test="query.minSleepDays != null">
                    AND DATEDIFF(NOW(), b.finish_time) <![CDATA[>=]]> #{query.minSleepDays}
                </if>
                <if test="query.maxSleepDays != null">
                    AND DATEDIFF(NOW(), b.finish_time) <![CDATA[<=]]> #{query.maxSleepDays}
                </if>
                <if test="query.timezone != null and query.timezone != ''">
                    AND d.timezone = #{query.timezone}
                </if>

                 <if test="query.accountStatusList != null and query.accountStatusList.size() > 0">
                    AND d.account_status IN
                    <foreach collection="query.accountStatusList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>
            </where>
            ORDER BY b.finish_time ASC
        </select>
    <select id="selectUserOrderStat" resultType="top.continew.admin.biz.model.resp.UserOrderStatResp">
        select u.nickname, COUNT(*) as num, SUM(o.clear_amount) as amount
        from biz_clear_order o
        left join sys_user u on u.id = o.handle_user
        where o.status = 3
        <if test="start != null and end != null">
            AND o.create_time BETWEEN #{start} AND #{end}
        </if>
        group by u.nickname order by num desc
    </select>
</mapper>