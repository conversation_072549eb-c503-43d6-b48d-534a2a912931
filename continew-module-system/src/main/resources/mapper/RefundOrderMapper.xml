<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.RefundOrderMapper">

    <sql id="selectRefundOrder">
        select o.*,
               c.name                        as customer_name,
               a.bm_id                       as bm_id,
               u.nickname                    as handle_user_name,
               concat_ws(',', COALESCE(a.browser_id, a.browser_no), bm.ops_browser, bm.reserve_browser, bm.reserve_browser_bak,
                         bm.observe_browser) as browser_no
        from biz_refund_order o
                 left join biz_ad_account a on a.platform_ad_id = o.platform_ad_id
                 left join sys_user u on u.id = o.handle_user
                 left join biz_customer c on c.id = o.customer_id
                 LEFT JOIN biz_business_manager bm ON bm.id = COALESCE(a.bm1_id, a.business_manager_id)
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.RefundOrderResp">
        <include refid="selectRefundOrder"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.RefundOrderResp">
        <include refid="selectRefundOrder"/>
        ${ew.customSqlSegment}
    </select>
</mapper>