/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import cn.hutool.core.util.NumberUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.service.CardService;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
public class RobotCardPlatformQueryStrategyImpl implements RobotCommandStrategy {

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    private final CardService cardService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.CARD_PLATFORM_QUERY;
    }

    @Override
    public String execute(Update update) {
        long chatId = update.getMessage().getChatId();
        if (chatId != 7259931323L && chatId != 5992643206L)
            return null;
        StringBuilder result = new StringBuilder();
        CardOpsStrategy cardvpService = cardOpsStrategyFactory.findStrategy(CardPlatformEnum.CARD_VP);
        BigDecimal cvRechargeLimit = cardService.getPlatformBalance(CardPlatformEnum.CARD_VP);
        BigDecimal cvBalance = cardvpService.getCurrentBalance();
        result.append("cv充值额度：").append(NumberUtil.toStr(cvRechargeLimit)).append("\n");
        result.append("cv可用余额：").append(NumberUtil.toStr(cvBalance)).append("\n");
        CardOpsStrategy gzyService = cardOpsStrategyFactory.findStrategy(CardPlatformEnum.PHOTON_PAY);
        BigDecimal gzyBalance = gzyService.getCurrentBalance();
        result.append("光子易可用余额：").append(NumberUtil.toStr(gzyBalance)).append("\n");
        return result.toString();
    }
}
