package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.mapper.BusinessManagerItemMapper;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.entity.BusinessManagerItemDO;
import top.continew.admin.biz.model.query.BusinessManagerItemQuery;
import top.continew.admin.biz.model.req.BmItemReq;
import top.continew.admin.biz.model.req.BusinessManagerItemReq;
import top.continew.admin.biz.model.req.BusinessManagerReq;
import top.continew.admin.biz.model.resp.BusinessManagerItemDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerItemResp;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.BusinessManagerItemService;
import top.continew.admin.biz.service.BusinessManagerService;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * bm坑位业务实现
 *
 * <AUTHOR>
 * @since 2025/03/11 11:52
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BusinessManagerItemServiceImpl extends BaseServiceImpl<BusinessManagerItemMapper, BusinessManagerItemDO, BusinessManagerItemResp, BusinessManagerItemDetailResp, BusinessManagerItemQuery, BusinessManagerItemReq> implements BusinessManagerItemService {

    private final BusinessManagerService businessManagerService;

    private final AdAccountService adAccountService;

    @Override
    public PageResp<BusinessManagerItemResp> page(BusinessManagerItemQuery query, PageQuery pageQuery) {
        QueryWrapper<BusinessManagerItemDO> wrapper = this.buildQueryWrapper(query);
        wrapper.orderByDesc("bbmi.id");
        IPage<BusinessManagerItemResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), wrapper);
        return PageResp.build(page);
    }

    @Override
    protected void afterAdd(BusinessManagerItemReq req, BusinessManagerItemDO entity) {
        businessManagerService.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
            .setSql("num = num + 1")
            .eq(BusinessManagerDO::getId, req.getBusinessManagerId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBusinessManagerItem(BmItemReq req) {
        List<BusinessManagerItemDO> saveList = new ArrayList<>();
        Integer num = req.getNum();
        for (int i = 0; i < num; i++) {
            BusinessManagerItemDO businessManagerItemDO = new BusinessManagerItemDO();
            businessManagerItemDO.setBusinessManagerId(req.getBmId());
            businessManagerItemDO.setName("坑位" + (i + 1));
            businessManagerItemDO.setType(req.getType());
            businessManagerItemDO.setChannelId(req.getChannelId());
            businessManagerItemDO.setOwnMethod(req.getOwnMethod());
            businessManagerItemDO.setUnitPrice(req.getUnitPrice());
            businessManagerItemDO.setRemark(req.getRemark());
            saveList.add(businessManagerItemDO);
        }
        saveBatch(saveList);
        businessManagerService.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
            .setSql("num = num + " + saveList.size())
            .eq(BusinessManagerDO::getId, req.getBmId()));
    }

    @Override
    public void addBusinessManagerItem(BusinessManagerReq req, BusinessManagerDO businessManager) {
        List<BusinessManagerItemDO> saveList = new ArrayList<>();
        Integer num = req.getNum();
        BigDecimal unitPrice = req.getUnitPrice().divide(new BigDecimal(num), 2, RoundingMode.HALF_UP);
        for (int i = 0; i < num; i++) {
            BusinessManagerItemDO businessManagerItemDO = new BusinessManagerItemDO();
            businessManagerItemDO.setBusinessManagerId(businessManager.getId());
            businessManagerItemDO.setName("坑位" + (i + 1));
            businessManagerItemDO.setType(req.getType());
            businessManagerItemDO.setIsBu(req.getIsBu());
            businessManagerItemDO.setChannelId(req.getChannelId());
            businessManagerItemDO.setOwnMethod(1);
            businessManagerItemDO.setUnitPrice(unitPrice);
            businessManagerItemDO.setRemark(req.getRemark());
            saveList.add(businessManagerItemDO);
        }
        saveBatch(saveList);
    }

    @Override
    public void banBusinessManager(Long businessManagerId) {
        update(new LambdaUpdateWrapper<BusinessManagerItemDO>().set(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.BANNED)
            .set(BusinessManagerItemDO::getBanTime, LocalDateTime.now())
            .eq(BusinessManagerItemDO::getBusinessManagerId, businessManagerId)
            .eq(BusinessManagerItemDO::getOwnMethod, BusinessManagerOwnMethodEnum.CLAIM.getValue())
            .eq(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.NORMAL));
    }

    @Override
    public void updatePrice(BusinessManagerDO businessManager) {
        long num = this.count(Wrappers.<BusinessManagerItemDO>lambdaQuery()
            .eq(BusinessManagerItemDO::getBusinessManagerId, businessManager.getId())
            .eq(BusinessManagerItemDO::getOwnMethod, BusinessManagerOwnMethodEnum.CLAIM.getValue()));
        if (num > 0) {
            BigDecimal unitPrice = businessManager.getUnitPrice()
                .divide(BigDecimal.valueOf(num), 2, RoundingMode.HALF_UP);
            update(new LambdaUpdateWrapper<BusinessManagerItemDO>().set(BusinessManagerItemDO::getUnitPrice, unitPrice)
                .eq(BusinessManagerItemDO::getBusinessManagerId, businessManager.getId())
                .eq(BusinessManagerItemDO::getOwnMethod, BusinessManagerOwnMethodEnum.CLAIM.getValue()));
        }
    }

    @Override
    protected void beforeUpdate(BusinessManagerItemReq req, Long id) {
        if (!req.getStatus().equals(BusinessManagerStatusEnum.BANNED)) {
            req.setBanTime(null);
        }
        if (!req.getIsUse()) {
            req.setUseTime(null);
        } else if (req.getUseTime() == null) {
            req.setUseTime(LocalDateTime.now());
        }
    }

    @Override
    protected void afterUpdate(BusinessManagerItemReq req, BusinessManagerItemDO entity) {
        if (StringUtils.isNotBlank(req.getPlatformAdId()) && req.getIsUse()) {
            BusinessManagerDO businessManagerDO = businessManagerService.getById(entity.getBusinessManagerId());
            AdAccountDO accountDO = adAccountService.getByPlatformAdId(req.getPlatformAdId());
            CheckUtils.throwIf(accountDO == null, "广告户不存在");
            LambdaUpdateWrapper<AdAccountDO> lambdaUpdateWrapper = Wrappers.<AdAccountDO>lambdaUpdate()
                .set(AdAccountDO::getBmId, businessManagerDO.getPlatformId())
                .set(AdAccountDO::getBusinessManagerId, entity.getBusinessManagerId())
                .set(AdAccountDO::getBmAuthTime, req.getUseTime())
                .set(AdAccountDO::getKeepStatus, AdAccountKeepStatusEnum.SUCCESS)
                .set(AdAccountDO::getBmItemType, req.getType())
                .set(AdAccountDO::getBmItemChannelId, req.getChannelId())
                .eq(AdAccountDO::getPlatformAdId, req.getPlatformAdId());
            if (!businessManagerDO.getType().equals(BusinessManagerTypeEnum.BM5)) {
                if (accountDO.getSaleStatus().equals(AdAccountSaleStatusEnum.WAIT)) {
                    lambdaUpdateWrapper.set(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.LOCK);
                }
            }
            adAccountService.update(lambdaUpdateWrapper);
        }
    }

    @Override
    public void export(BusinessManagerItemQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<BusinessManagerItemResp> list = baseMapper.selectCustomList(this.buildQueryWrapper(query));
        ExcelUtils.export(list, "导出数据", BusinessManagerItemResp.class, response);

    }

    @Override
    protected void beforeDelete(List<Long> ids) {
        for (Long id : ids) {
            BusinessManagerItemDO businessManagerItemDO = this.getById(id);
            businessManagerService.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                .setSql("num = num - 1")
                .eq(BusinessManagerDO::getId, businessManagerItemDO.getBusinessManagerId()));
        }
    }
}