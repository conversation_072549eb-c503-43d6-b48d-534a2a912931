package top.continew.admin.biz.utils;

import java.util.Random;

public class RandomEnglishNameGenerator {
    private static final String[] FIRST_NAMES = {"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "Robinson", "Clark", "Rodriguez", "Lewis", "Lee", "Walker", "Hall", "Allen", "Young", "Hernandez",
        "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores", "Green", "Adams", "Nelson", "Baker", "Hall",
        "Rivera", "Campbell", "<PERSON>", "<PERSON>", "Roberts", "Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker",
        "<PERSON>", "Edwards", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Morris", "Rogers", "Reed", "Cook", "<PERSON>",
        "Bell", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"};

    private static final <PERSON>[] <PERSON><PERSON>_NAMES = {"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "Daniel", "Matthew", "Anthony", "Mark", "Donald", "Steven", "Paul", "Andrew",
        "Joshua", "Kevin", "Brian", "George", "Edward", "Ronald", "Timothy", "Jason", "Jeffrey", "Gary", "Ryan",
        "Nicholas", "Eric", "Jacob", "Jonathan", "Larry", "Frank", "Scott", "Justin", "Brandon", "Samuel", "Benjamin",
        "Adam", "Patrick", "Raymond", "Jack", "Dennis", "Jerry", "Tyler", "Aaron", "Henry", "Douglas", "Peter",
        "Walter", "Jose", "Ethan", "Kyle", "Carl", "Arthur", "Gerald", "Roger", "Keith", "Lawrence", "Terry", "Sean",
        "Christian", "Austin", "Jesse", "Joe", "Albert", "Alan", "Billy", "Bruce", "Willie", "Gabriel", "Ralph", "Roy",
        "Dylan", "Juan", "Jordan", "Leonard", "Jared", "Eugene", "Carlos", "Russell", "Bobby", "Victor", "Martin",
        "Ernest", "Phillip", "Murray", "Leroy", "Jimmie", "Julius", "Harold", "Benny", "Freddie"};

    private static final Random random = new Random();

    // 生成随机英文名
    public static String generateRandomName() {
        String firstName = FIRST_NAMES[random.nextInt(FIRST_NAMES.length)];
        String lastName = LAST_NAMES[random.nextInt(LAST_NAMES.length)];
        return firstName + " " + lastName;
    }
}
