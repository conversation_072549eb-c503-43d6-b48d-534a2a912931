package top.continew.admin.biz.model.resp;

import java.io.Serial;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * bm管理员详情信息
 *
 * <AUTHOR>
 * @since 2025/04/22 16:45
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "bm管理员详情信息")
public class BusinessManagerUserDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * BM ID
     */
    @Schema(description = "BM ID")
    @ExcelProperty(value = "BM ID")
    private String bmId;

    /**
     * USER ID
     */
    @Schema(description = "USER ID")
    @ExcelProperty(value = "USER ID")
    private String userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    @ExcelProperty(value = "用户名")
    private String username;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    @ExcelProperty(value = "邮箱")
    private String userEmail;

    /**
     * 用户权限
     */
    @Schema(description = "用户权限")
    @ExcelProperty(value = "用户权限")
    private String userRole;

    /**
     * 是否移除
     */
    @Schema(description = "是否移除")
    @ExcelProperty(value = "是否移除")
    private Boolean isRemove;
}