package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "客户消耗统计信息响应体")
public class CustomerSpentResp {

    /**
     * 客户ID
     * 这是客户在系统中的唯一标识符。
     */
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 客户名称
     * 客户的公司或个人名称。
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 总下户数
     * 该客户下属的所有子账户总数。
     */
    @Schema(description = "总下户数")
    private Integer totalSubAccounts;

    /**
     * 广告使用数
     * 正在使用广告服务的子账户数量。
     */
    @Schema(description = "广告使用数")
    private Integer adUsageCount;

    /**
     * 不消耗数
     * 在统计周期内，没有任何广告消耗的子账户数量。
     */
    @Schema(description = "不消耗数")
    private Integer nonSpendingCount;

    /**
     * 低消耗数
     * 在统计周期内，广告消耗低于预设阈值的子账户数量。
     */
    @Schema(description = "低消耗数")
    private Integer lowSpendingCount;

    /**
     * 正常消耗数
     * 在统计周期内，广告消耗正常的子账户数量。
     */
    @Schema(description = "正常消耗数")
    private Integer normalSpendingCount;


}
