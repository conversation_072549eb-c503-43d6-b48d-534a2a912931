/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "广告户金额核对响应")
public class AdAccountCheckAmountResp {
    private String platformAdId;

    @Schema(description = "广告户总金额")
    private BigDecimal adAmount;

    @Schema(description = "卡台总金额")
    private BigDecimal cardAmount;

    @Schema(description = "差值(广告户总金额-卡台总金额)")
    private BigDecimal diffAmount;

    private String timezone;
}