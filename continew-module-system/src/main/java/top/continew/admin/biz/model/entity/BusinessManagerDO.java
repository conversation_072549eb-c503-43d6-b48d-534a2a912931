/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.*;
import top.continew.starter.extension.crud.annotation.DictField;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * BM5账号实体
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
@DictField(labelKey = "platformId")
@Data
@TableName("biz_business_manager")
public class BusinessManagerDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联渠道
     */
    private Long channelId;

    /**
     * BM5 ID
     */
    private String platformId;

    /**
     * 浏览器编号
     */
    private String browserNo;

    private BusinessManagerStatusEnum status;

    private Boolean isUse;

    private String remark;

    private String content;

    private Integer num;

    private String user;

    private LocalDateTime useTime;

     private BigDecimal unitPrice;

    private LocalDateTime banTime;

    private String opsBrowser;
    private String reserveBrowser;
    private String observeBrowser;

    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;
    /**
     * 售后原因
     */
    private String afterSaleReason;

    private BusinessManagerTypeEnum type;

    private Boolean isExternal;

    private Boolean isEnterpriseAuth;

    private Boolean isRemoveAdmin;

    private String reserveBrowserBak;

    private BusinessManagerBannedReasonEnum bannedReason;

    private Boolean isBu;

    private String syncBrowserNo;
}