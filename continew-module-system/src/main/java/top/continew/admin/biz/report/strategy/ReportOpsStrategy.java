/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy;

import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.system.model.entity.UserDO;

public interface ReportOpsStrategy {

    /**
     * 对应操作类型
     *
     * @return
     */
    ReportType getReport();

    /**
     * 返回需要记录的数据
     *
     * @param browserReq
     * @param userDO
     * @return
     */
    String handle(BrowserReq browserReq, UserDO userDO);
}
