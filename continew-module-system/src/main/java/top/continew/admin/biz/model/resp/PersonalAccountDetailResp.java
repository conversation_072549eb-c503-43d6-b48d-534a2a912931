package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.PersonalAccountStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAppealStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 个号详情信息
 *
 * <AUTHOR>
 * @since 2025/02/27 14:48
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "个号详情信息")
public class PersonalAccountDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @Schema(description = "购买日期")
    @ExcelProperty(value = "购买日期")
    private LocalDateTime purchaseTime;

    private Long channelId;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    @ExcelProperty(value = "渠道")
    private String channelName;

    /**
     * 账号信息
     */
    @Schema(description = "账号信息")
    @ExcelProperty(value = "账号信息")
    private String content;

    /**
     * 类型
     */
    @ExcelProperty(value = "状态", converter = ExcelBaseEnumConverter.class)
    private PersonalAccoutTypeEnum type;

    /**
     * 接入人
     */
    @Schema(description = "接入人")
    @ExcelProperty(value = "接入操作人")
    private String accessUser;

    /**
     * 是否接入
     */
    @Schema(description = "是否接入")
    @ExcelProperty(value = "是否接入")
    private Boolean isAccess;

    @ExcelProperty(value = "是否改密码")
    private Boolean isChangePwd;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    @ExcelProperty(value = "浏览器编号")
    private String browserNo;

    /**
     * 账号状态
     */
    @Schema(description = "状态")
    @ExcelProperty(value = "状态", converter = ExcelBaseEnumConverter.class)
    private PersonalAccountStatusEnum accountStatus;

    /**
     * 单价
     */
    @Schema(description = "单价")
    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "补号")
    private Boolean isAfterSale;

    @ExcelProperty(value = "售后状态", converter = ExcelBaseEnumConverter.class)
    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    @ExcelProperty(value = "售后原因")
    private String afterSaleReason;

    @ExcelProperty(value = "申诉状态", converter = ExcelBaseEnumConverter.class)
    private PersonalAccoutAppealStatusEnum appealStatus;

    private String proxy;

    private String browserId;

}