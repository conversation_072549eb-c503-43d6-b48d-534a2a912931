package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索跟进记录更新请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "线索跟进记录更新请求")
public class LeadFollowUpdateReq {

    /**
     * 跟进记录ID
     */
    @NotNull(message = "跟进记录ID不能为空")
    @Schema(description = "跟进记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * 跟进内容
     */
    @Schema(description = "跟进内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    /**
     * 附件地址
     */
    @Schema(description = "附件地址")
    private String attachment;

    /**
     * 跟进时间
     */
    @NotNull(message = "跟进时间不能为空")
    @Schema(description = "跟进时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime followTime;
}