package top.continew.admin.biz.model.resp;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ExcelIgnoreUnannotated
public class AdAccountRetentionResp {
    @ExcelProperty("日期")
    private LocalDate date;
    @ExcelProperty("下户成本")
    private BigDecimal cost;
    @ExcelProperty("数量")
    private Integer count;
    @ExcelProperty("次日留存")
    private Integer retention1d;
    @ExcelProperty("7日留存")
    private Integer retention7d;
    @ExcelProperty("15日留存")
    private Integer retention15d;
    @ExcelProperty("30日及以上留存")
    private Integer retention30d;
    private Integer banCount1d;
    private Integer banCount7d;
    private Integer banCount15d;
    private Integer banCount30d;
}