package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data

public class AdAccountOrderAddReq {

    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    private String adAccountName;

    private String customerBmId;

    private String customerEmail;

    @NotBlank(message = "时区设置不能为空")
    private String timezone;

    @NotNull(message = "订单数量不能为空")
    private Integer quantity;

}
