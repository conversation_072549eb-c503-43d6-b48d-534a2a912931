package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.VisitTaskStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 客户回访任务实体
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Data
@TableName("biz_customer_visit_task")
public class CustomerVisitTaskDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 触发策略ID
     */
    private Long strategyId;

    /**
     * 触发策略名称
     */
    private String strategyName;

    /**
     * 任务状态：1-待处理，2-处理中，3-已完成，4-已关闭
     */
    private VisitTaskStatusEnum taskStatus;

    /**
     * 负责人ID
     */
    private Long assigneeId;

    /**
     * 要求完成时间
     */
    private LocalDateTime requiredFinishTime;

    /**
     * 回访提醒时间
     */
    private LocalDateTime reminderTime;

    /**
     * 回访目标
     */
    private String visitGoal;

    /**
     * 回访结果
     */
    private Integer visitResult;

    /**
     * 关闭原因
     */
    private String closeReason;

    /**
     * 触发条件信息，记录客户满足的具体条件
     */
    private String triggerInfo;
}