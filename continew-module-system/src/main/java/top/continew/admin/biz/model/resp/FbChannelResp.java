/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * FB账号渠道信息
 *
 * <AUTHOR>
 * @since 2025/01/06 11:37
 */
@Data
@Schema(description = "FB账号渠道信息")
public class FbChannelResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String name;
}