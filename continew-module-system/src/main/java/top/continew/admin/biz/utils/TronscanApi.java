package top.continew.admin.biz.utils;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import okhttp3.Headers;
import okhttp3.HttpUrl;

public class TronscanApi {

    public static JSONObject getTransferList(String relatedAddress,
                                             Long startTimestamp,
                                             Long endTimestamp,
                                             int start,
                                             int limit) {
        HttpUrl.Builder urlBuilder = HttpUrl.parse("https://apilist.tronscanapi.com/api/filter/trc20/transfers")
            .newBuilder();
        urlBuilder.addQueryParameter("limit", String.valueOf(limit));
        urlBuilder.addQueryParameter("start", String.valueOf(start));
        urlBuilder.addQueryParameter("sort", "-timestamp");
        urlBuilder.addQueryParameter("count", "true");
        urlBuilder.addQueryParameter("filterTokenValue", "0");
        if (startTimestamp != null) {
            urlBuilder.addQueryParameter("start_timestamp", String.valueOf(startTimestamp));
        }
        if (endTimestamp != null) {
            urlBuilder.addQueryParameter("end_timestamp", String.valueOf(endTimestamp));
        }
        urlBuilder.addQueryParameter("relatedAddress", relatedAddress);
        Headers headers = new Headers.Builder().add("accept", "application/json, text/plain, */*")
            .add("accept-language", "zh-CN,zh;q=0.9")
            .add("cache-control", "no-cache")
            .add("origin", "https://tronscan.org")
            .add("referer", "https://tronscan.org/")
            .add("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")
            .build();
        while (true) {
            try {
                String result = HttpRequest.get(urlBuilder.build().toString())
                    .header(headers.toMultimap())
                    .execute()
                    .body();
                return JSONObject.parseObject(result);
            } catch (IORuntimeException ignored) {

            }
        }
    }
}
