/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * fb账号信息
 *
 * <AUTHOR>
 * @since 2025/01/03 16:32
 */
@Data
@Schema(description = "fb账号信息")
public class FbAccountResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联账号ID
     */
    @Schema(description = "关联账号ID")
    private String platformAccountId;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    private String browserSerialNo;

    /**
     * 状态（1=正常，2=封禁）
     */
    @Schema(description = "状态（1=正常，2=封禁）")
    private FbAccountStatusEnum status;

    private Long channelId;

    private String tag;

    private String proxy;

    private String customRemark;

    private BigDecimal price;
}