package top.continew.admin.biz.mapper.crm;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.crm.CustomerVisitTaskDetailDO;
import top.continew.starter.data.mp.base.BaseMapper;

import java.util.List;

/**
 * 客户回访任务明细 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
public interface CustomerVisitTaskDetailMapper extends BaseMapper<CustomerVisitTaskDetailDO> {
    
    /**
     * 根据任务ID查询回访明细列表
     *
     * @param taskId 任务ID
     * @return 回访明细列表
     */
    List<CustomerVisitTaskDetailDO> selectByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 根据任务ID删除回访明细
     *
     * @param taskId 任务ID
     * @return 删除数量
     */
    int deleteByTaskId(@Param("taskId") Long taskId);
}