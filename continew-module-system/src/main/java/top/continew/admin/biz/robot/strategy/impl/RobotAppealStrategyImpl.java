/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.AdAccountAppealStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.enums.AppealOrderStatusEnum;
import top.continew.admin.biz.event.AppealOrderCreateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.AppealOrderDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotAppealReq;
import top.continew.admin.biz.robot.strategy.BaseRobotCommandService;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.AppealOrderService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;

import java.time.LocalDateTime;

@Service
@Slf4j
@RequiredArgsConstructor
public class RobotAppealStrategyImpl extends BaseRobotCommandService implements RobotCommandStrategy {

    private final AppealOrderService appealOrderService;

    private final AdAccountService adAccountService;

    private final AdAccountOrderService adAccountOrderService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.APPEAL;
    }

    @Override
    public String execute(Update update) {
        if (!this.hasPermission(this.getCommand(), update.getMessage().getChatId())) {
            return null;
        }
        RobotAppealReq req = BotUtils.parseBotMsgToObject(update.getMessage()
            .getText(), "\n", ":", RobotAppealReq.class);
        log.info("{}收到一条申诉请求：{}", getCommand().getDescription(), JSONObject.toJSONString(req));
        CustomerDO customer = getCustomer(req.getCustomerName(), update.getMessage().getChatId());
        adAccountOrderService.checkExistOrder(customer.getId(), req.getPlatformAdId());
        AdAccountDO adAccount = adAccountService.getOne(Wrappers.<AdAccountDO>lambdaQuery()
            .eq(AdAccountDO::getPlatformAdId, req.getPlatformAdId()));
        CheckUtils.throwIfNull(adAccount, "未找到相关广告户");
        CheckUtils.throwIfNull(!adAccount.getAppealStatus().equals(AdAccountAppealStatusEnum.WAIT), "请勿重复提交申诉");
        // 一个广告户只允许一条进行中的申诉订单
        AppealOrderDO existOrder = appealOrderService.getOne(Wrappers.<AppealOrderDO>lambdaQuery()
            .eq(AppealOrderDO::getPlatformAdId, req.getPlatformAdId())
            .orderByDesc(AppealOrderDO::getCreateTime)
            .last("limit 1"));
        if (existOrder == null || existOrder.getStatus().equals(AppealOrderStatusEnum.SUCCESS) || existOrder.getStatus()
            .equals(AppealOrderStatusEnum.CANCEL)) {
            AppealOrderDO order = new AppealOrderDO();
            order.setOrderNo(CommonUtils.randomOrderNo("SS"));
            order.setCustomerId(customer.getId());
            order.setPlatformAdId(req.getPlatformAdId());
            order.setStatus(AppealOrderStatusEnum.WAIT);
            order.setRemark(req.getRemark());
            order.setApplyMessageId(update.getMessage().getMessageId());
            appealOrderService.save(order);
            log.info("{}订单入库成功", getCommand().getDescription());
            AppealOrderCreateEvent createEvent = new AppealOrderCreateEvent(order.getId());
            SpringUtil.publishEvent(createEvent);
            adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                .set(AdAccountDO::getAccountStatus, AdAccountStatusEnum.BANNED)
                .set(AdAccountDO::getBanTime, LocalDateTime.now())
                .eq(AdAccountDO::getPlatformAdId, req.getPlatformAdId())
                .ne(AdAccountDO::getAccountStatus, AdAccountStatusEnum.BANNED));
            return "订单提交成功，等待客服人员处理";
        } else {
            if (existOrder.getApplyMessageId() == null) {
                // 代表是系统检测封禁自动提交的申诉订单，需要绑定消息ID
                appealOrderService.update(Wrappers.<AppealOrderDO>lambdaUpdate()
                    .set(AppealOrderDO::getApplyMessageId, update.getMessage().getMessageId())
                    .set(StringUtils.isNotBlank(req.getRemark()), AppealOrderDO::getRemark, req.getRemark())
                    .eq(AppealOrderDO::getId, existOrder.getId()));
                return "订单提交成功，等待客服人员处理";
            } else {
                throw new BusinessException("请勿重复提交申诉");
            }
        }
    }
}
