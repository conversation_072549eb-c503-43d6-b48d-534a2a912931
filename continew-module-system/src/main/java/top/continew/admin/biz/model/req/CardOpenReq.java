package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import top.continew.admin.biz.enums.CardPlatformEnum;

@Data
public class CardOpenReq {

    @NotNull(message = "请选择开卡平台")
    private CardPlatformEnum platform;

    @NotBlank(message = "卡头不能为空")
    private String cardBin;

    @NotBlank(message = "请选择广告户")
    private String platformAdId;

    /**
     * 开卡组织
     */
    private String cardScheme;
}
