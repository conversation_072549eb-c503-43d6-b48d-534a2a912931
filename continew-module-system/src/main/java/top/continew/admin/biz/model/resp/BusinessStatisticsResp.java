package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Data
@Schema(description = "商务统计响应")
public class BusinessStatisticsResp {

    @Schema(description = "商务ID")
    private Long id;

    @Schema(description = "商务名称")
    private String nickname;

    @Schema(description = "累计客户数")
    private Integer totalCustomer;

    @Schema(description = "总打款")
    private BigDecimal totalPaid;

    @Schema(description = "总充值")
    private BigDecimal totalRecharge;

    @Schema(description = "总消耗")
    private BigDecimal totalSpend;

    @Schema(description = "总户数")
    private Integer totalAccount;

    @Schema(description = "新增客户数")
    private Integer totalNewCustomer;

    @Schema(description = "退款客户数")
    private Integer totalRefundCustomer;

    @Schema(description = "死户数量")
    private Integer deadAccount;

    @Schema(description = "占用资金")
    private BigDecimal occupiedAmount;


    @Schema(description = "死户率")
    private BigDecimal deadRate;

    @Schema(description = "平均户消耗")
    private BigDecimal averageSpend;

    @Schema(description = "平均户充值")
    private BigDecimal averageRecharge;
}