package top.continew.admin.biz.model.req;

import cn.hutool.core.date.DatePattern;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PurchaseOrderPayReq {

    private Boolean isFinish;

    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime payTime;

    private List<PurchaseOrderPayItemReq> items;

    @Data
    public static class PurchaseOrderPayItemReq {
        @NotNull(message = "请选择记录")
        private Long id;

        @NotNull(message = "请输入支付金额")
        private BigDecimal payPrice;
    }
}
