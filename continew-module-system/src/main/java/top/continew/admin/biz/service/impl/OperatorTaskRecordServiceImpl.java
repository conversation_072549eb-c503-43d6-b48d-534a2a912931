package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.OperatorTaskRecordMapper;
import top.continew.admin.biz.model.entity.OperatorTaskRecordDO;
import top.continew.admin.biz.model.query.OperatorTaskRecordQuery;
import top.continew.admin.biz.model.req.OperatorTaskRecordReq;
import top.continew.admin.biz.model.resp.OperatorTaskRecordDetailResp;
import top.continew.admin.biz.model.resp.OperatorTaskRecordResp;
import top.continew.admin.biz.service.OperatorTaskRecordService;

/**
 * 运营人员工作记录业务实现
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Service
@RequiredArgsConstructor
public class OperatorTaskRecordServiceImpl extends BaseServiceImpl<OperatorTaskRecordMapper, OperatorTaskRecordDO, OperatorTaskRecordResp, OperatorTaskRecordDetailResp, OperatorTaskRecordQuery, OperatorTaskRecordReq> implements OperatorTaskRecordService {

    private final AdAccountOrderService adAccountOrderService;

    @Override
    protected void beforeAdd(OperatorTaskRecordReq req) {
        if (StringUtils.isNotBlank(req.getPlatformAdId())) {
            AdAccountOrderDO adAccountOrder = adAccountOrderService.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
                .eq(AdAccountOrderDO::getAdAccountId, req.getPlatformAdId())
                .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
            if (adAccountOrder != null) {
                req.setCustomerId(adAccountOrder.getCustomerId());
            }
        }
    }
}