package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.time.*;
import java.util.List;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改客户回访任务参数
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Data
@Schema(description = "创建或修改客户回访任务参数")
public class CustomerVisitTaskReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID列表（批量创建）
     */
    @Schema(description = "客户ID列表")
    @NotEmpty(message = "客户ID列表不能为空")
    private List<Long> customerIds;

    /**
     * 触发策略ID（手动创建时为0）
     */
    @Schema(description = "触发策略ID")
    private Long strategyId = 0L;

    /**
     * 触发策略名称
     */
    @Schema(description = "触发策略名称")
    @Length(max = 100, message = "触发策略名称长度不能超过 {max} 个字符")
    private String strategyName = "手动创建";


    /**
     * 要求完成时间
     */
    @Schema(description = "要求完成时间")
    @NotNull(message = "要求完成时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requiredFinishTime;

    /**
     * 回访目标
     */
    @Schema(description = "回访目标")
    private String visitGoal;

    /**
     * 触发条件信息（手动创建原因）
     */
    @Schema(description = "触发条件信息")
    @NotBlank(message = "触发条件信息不能为空")
    @Length(max = 500, message = "触发条件信息长度不能超过 {max} 个字符")
    private String triggerInfo;
}