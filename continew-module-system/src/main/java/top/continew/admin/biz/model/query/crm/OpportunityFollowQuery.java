package top.continew.admin.biz.model.query.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import top.continew.starter.extension.crud.model.query.PageQuery;

/**
 * 商机跟进记录查询
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "商机跟进记录查询")
public class OpportunityFollowQuery extends PageQuery {

    @Schema(description = "商机ID")
    @NotNull(message = "商机ID不能为空")
    private Long opportunityId;
}