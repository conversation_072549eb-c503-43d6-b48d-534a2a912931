package top.continew.admin.biz.service;

import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.OperatorTaskRecordQuery;
import top.continew.admin.biz.model.req.OperatorTaskRecordReq;
import top.continew.admin.biz.model.resp.OperatorTaskRecordDetailResp;
import top.continew.admin.biz.model.resp.OperatorTaskRecordResp;

/**
 * 运营人员工作记录业务接口
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
public interface OperatorTaskRecordService extends BaseService<OperatorTaskRecordResp, OperatorTaskRecordDetailResp, OperatorTaskRecordQuery, OperatorTaskRecordReq> {}