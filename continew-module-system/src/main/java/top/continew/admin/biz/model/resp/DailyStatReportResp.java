package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;


@Getter
@Setter
@ExcelIgnoreUnannotated
public class DailyStatReportResp {

    /**
     * 统计日期
     */
    @ExcelProperty("统计日期")
    private String statDate;

    /**
     * 打款金额
     */
    @ExcelProperty("打款金额")
    private BigDecimal transferAmount;

    @ExcelProperty("打款金额")
    private BigDecimal refundAmount;

    /**
     * 充值手续费
     */
    @ExcelProperty("服务费")
    private BigDecimal fee;


    @ExcelProperty("广告户开户费")
    private BigDecimal adAccountBuyAmount;

    @ExcelProperty("总充值金额")
    private BigDecimal totalRecharge;


    @ExcelProperty("卡台消耗")
    private BigDecimal cardSpent;
}
