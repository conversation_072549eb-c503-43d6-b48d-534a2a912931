package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AdAccountUpdateBMReq {
    @NotEmpty(message = "请选择记录")
    private List<Long> ids;

    @NotNull(message = "BM不能为空")
    private Long businessManagerId;

    @NotNull(message = "授权时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime authTime;
}
