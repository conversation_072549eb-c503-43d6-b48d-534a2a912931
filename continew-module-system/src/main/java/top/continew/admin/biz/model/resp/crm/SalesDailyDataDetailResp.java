package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 商务每日数据详情信息
 *
 * <AUTHOR>
 * @since 2025/07/11 14:30
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "商务每日数据详情信息")
public class SalesDailyDataDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 线索ID
     */
    @Schema(description = "线索ID")
    @ExcelProperty(value = "线索ID")
    private Long leadId;

    /**
     * 记录添加日期，默认为当前日期
     */
    @Schema(description = "记录添加日期，默认为当前日期")
    @ExcelProperty(value = "添加日期")
    private LocalDate recordDate;

    /**
     * 添加方式，例如：被动，主动
     */
    @Schema(description = "添加方式，例如：被动，主动")
    @ExcelProperty(value = "添加方式")
    private Integer addMethod;

    /**
     * 客户账号类型，例如：微信，tg
     */
    @Schema(description = "客户账号类型，例如：微信，tg")
    @ExcelProperty(value = "账号类型")
    private Integer accountType;

    /**
     * 客户的微信号或tg id
     */
    @Schema(description = "客户的微信号或tg id")
    @ExcelProperty(value = "客户账号")
    private String customerAccount;

    private String customerName;

    private Long socialAccountId;


    /**
     * 客户的大致业务范围或描述
     */
    @Schema(description = "客户的大致业务范围或描述")
    @ExcelProperty(value = "客户业务")
    private String customerBusiness;

    /**
     * 客户所在的城市
     */
    @Schema(description = "客户所在的城市")
    @ExcelProperty(value = "客户城市")
    private String customerCity;

    /**
     * 对客户的初步情况进行描述
     */
    @Schema(description = "对客户的初步情况进行描述")
    @ExcelProperty(value = "客户初步情况")
    private String customerOverview;

    /**
     * 记录与客户首次沟通的内容和结果
     */
    @Schema(description = "记录与客户首次沟通的内容和结果")
    @ExcelProperty(value = "首次沟通内容")
    private String firstContactNotes;
}