/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.ClearOrderStatusEnum;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.model.entity.ClearOrderDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.ClearOrderService;
import top.continew.admin.system.model.entity.UserDO;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
@RequiredArgsConstructor
@Slf4j
public class UseBillingResetSpendMutationStrategyImpl implements ReportOpsStrategy {

    private final ClearOrderService clearOrderService;

    @Override
    public ReportType getReport() {
        return ReportType.USE_BILLING_RESET_SPEND_MUTATION;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        JSONObject data = JSONObject.from(browserReq.getData());
        String platformAdId = data.getJSONObject("body")
            .getJSONObject("input")
            .getString("billable_account_payment_legacy_account_id");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("platformAdId", platformAdId);
        JSONObject beforeAdAccountBillingHubPaymentSetting = data.getJSONObject("beforeAdAccountBillingHubPaymentSetting");
        if (beforeAdAccountBillingHubPaymentSetting == null) {
            return jsonObject.toString();
        }
        JSONObject spendingInfo = beforeAdAccountBillingHubPaymentSetting.getJSONObject("spending_info");
        if (spendingInfo == null) {
            return jsonObject.toString();
        }
        BigDecimal spendLimit = spendingInfo.getJSONObject("spend_limit_currency_amount")
            .getBigDecimal("amount_with_offset");
        BigDecimal amountSpent = spendingInfo.getJSONObject("amount_spent_currency_amount")
            .getBigDecimal("amount_with_offset");
        jsonObject.put("spendLimit", spendLimit);
        jsonObject.put("amountSpent", amountSpent);
        ClearOrderDO order = clearOrderService.getOne(Wrappers.<ClearOrderDO>lambdaQuery()
            .eq(ClearOrderDO::getPlatformAdId, platformAdId)
            .in(ClearOrderDO::getStatus, ClearOrderStatusEnum.HANDLING));
        log.info("【花费重置】{}当前限额：{}，当前花费：{}", platformAdId, spendLimit, amountSpent);
        if (order != null) {
            // 更新清零金额
            BigDecimal clearAmount = spendLimit.subtract(amountSpent);
            clearOrderService.update(Wrappers.<ClearOrderDO>lambdaUpdate()
                .set(ClearOrderDO::getClearAmount, clearAmount.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                .eq(ClearOrderDO::getId, order.getId()));
        }
        return jsonObject.toString();
    }
}
