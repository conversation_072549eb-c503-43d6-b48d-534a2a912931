/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.RechargeOrderCardStatusEnum;
import top.continew.admin.biz.enums.FbLimitCheckStatusEnum;
import top.continew.admin.biz.enums.RechargeOrderStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值订单实体
 *
 * <AUTHOR>
 * @since 2024/12/30 17:09
 */
@Data
@TableName("biz_recharge_order")
public class RechargeOrderDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 卡片充值状态
     */
    private RechargeOrderCardStatusEnum cardStatus;

    /**
     * fb限额检测状态
     */
    private FbLimitCheckStatusEnum fbCheckStatus;

    /**
     * 关联广告户
     */
    private String platformAdId;

    /**
     * 充值金额
     */
    private BigDecimal amount;

    /**
     * 状态
     */
    private RechargeOrderStatusEnum status;

    /**
     * 关联消息ID
     */
    private Integer applyMessageId;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 接收时间
     */
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 凭证
     */
    private String certificate;

    /**
     * 备注
     */
    private String remark;

    /**
     * fb操作ID
     */
    private String fbOpsId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    @TableField(fill = FieldFill.UPDATE)
    private Long updateUser;
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}