package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 运营人员工作记录信息
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Data
@Schema(description = "运营人员工作记录信息")
public class OperatorTaskRecordResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    private String platformAdId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Integer type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer num;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}