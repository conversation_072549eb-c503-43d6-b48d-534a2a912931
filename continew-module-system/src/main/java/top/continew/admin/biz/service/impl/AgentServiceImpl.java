package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.AgentMapper;
import top.continew.admin.biz.model.entity.AgentDO;
import top.continew.admin.biz.model.query.AgentQuery;
import top.continew.admin.biz.model.req.AgentReq;
import top.continew.admin.biz.model.resp.AgentDetailResp;
import top.continew.admin.biz.model.resp.AgentResp;
import top.continew.admin.biz.service.AgentService;

/**
 * 中介业务实现
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Service
@RequiredArgsConstructor
public class AgentServiceImpl extends BaseServiceImpl<AgentMapper, AgentDO, AgentR<PERSON>p, AgentDetailResp, Agent<PERSON>uery, AgentReq> implements AgentService {}