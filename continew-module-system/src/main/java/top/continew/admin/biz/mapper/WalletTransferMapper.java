package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.WalletTransferDO;
import top.continew.admin.biz.model.resp.WalletTransferDetailResp;
import top.continew.admin.biz.model.resp.WalletTransferResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包流水 Mapper
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
public interface WalletTransferMapper extends BaseMapper<WalletTransferDO> {

    IPage<WalletTransferResp> selectCustomPage(IPage<WalletTransferDO> page,
                                               @Param(Constants.WRAPPER) QueryWrapper<WalletTransferDO> queryWrapper);

    List<WalletTransferDetailResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<WalletTransferDO> queryWrapper);

    BigDecimal getCurrentBalance();
}