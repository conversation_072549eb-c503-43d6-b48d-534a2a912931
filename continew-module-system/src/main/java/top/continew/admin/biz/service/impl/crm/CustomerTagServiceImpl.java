package top.continew.admin.biz.service.impl.crm;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import top.continew.admin.biz.mapper.crm.CustomerTagMapper;
import top.continew.admin.biz.model.entity.crm.CustomerTagDO;
import top.continew.admin.biz.model.req.crm.CustomerTagAddReq;
import top.continew.admin.biz.model.resp.crm.CustomerTagResp;
import top.continew.admin.biz.service.crm.CustomerTagService;
import top.continew.admin.system.mapper.DictItemMapper;
import top.continew.admin.system.mapper.DictMapper;
import top.continew.admin.system.model.entity.DictDO;
import top.continew.admin.system.model.entity.DictItemDO;
import top.continew.starter.core.exception.BusinessException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户标签业务实现
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Service
@RequiredArgsConstructor
public class CustomerTagServiceImpl implements CustomerTagService {
    private final CustomerTagMapper customerTagMapper;
    private final DictItemMapper dictItemMapper;
    private final DictMapper dictMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCustomerTag(CustomerTagAddReq req) {
        // 1. 验证标签类型编码是否存在
        DictDO dictDO = dictMapper.selectOne(new LambdaQueryWrapper<DictDO>().eq(DictDO::getCode, req.getCode()));
        if (dictDO == null) {
            throw new BusinessException("标签类型编码不存在");
        }
        
        // 2. 验证标签ID是否存在
        DictItemDO dictItem = dictItemMapper.selectById(req.getTagId());
        
        if (null == dictItem) {
            throw new BusinessException("标签不存在");
        }
        
        // 3. 删除已有的同类型标签
        customerTagMapper.delete(
            new LambdaQueryWrapper<CustomerTagDO>()
                .eq(CustomerTagDO::getCustomerId, req.getCustomerId())
                .eq(CustomerTagDO::getCode, req.getCode())
        );
        
        // 4. 添加新标签
        CustomerTagDO tag = new CustomerTagDO();
        tag.setCustomerId(req.getCustomerId());
        tag.setTagId(req.getTagId());
        tag.setCode(req.getCode());
        customerTagMapper.insert(tag);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCustomerTag(Long customerId, Long tagId) {
        customerTagMapper.delete(
            new LambdaQueryWrapper<CustomerTagDO>()
                .eq(CustomerTagDO::getCustomerId, customerId)
                .eq(CustomerTagDO::getTagId, tagId)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteCustomerTag(Long customerId, List<Long> tagIds) {
        if (CollUtil.isEmpty(tagIds)) {
            return;
        }

        customerTagMapper.delete(
            new LambdaQueryWrapper<CustomerTagDO>()
                .eq(CustomerTagDO::getCustomerId, customerId)
                .in(CustomerTagDO::getTagId, tagIds)
        );
    }

    @Override
    public List<CustomerTagResp> getCustomerTagList(Long customerId) {
        // 1. 查询客户标签
        List<CustomerTagDO> tags = customerTagMapper.selectList(
            new LambdaQueryWrapper<CustomerTagDO>()
                .eq(CustomerTagDO::getCustomerId, customerId)
        );
        
        if (CollUtil.isEmpty(tags)) {
            return new ArrayList<>();
        }

        // 4. 获取所有标签ID
        List<Long> tagIds = tags.stream()
            .map(CustomerTagDO::getTagId)
            .collect(Collectors.toList());
        
        // 5. 查询标签名称
        Map<Long, String> tagNameMap = new HashMap<>();
        List<DictItemDO> dictItems = dictItemMapper.selectList(
            new LambdaQueryWrapper<DictItemDO>()
                .in(DictItemDO::getId, tagIds)
        );
        
        for (DictItemDO dictItem : dictItems) {
            tagNameMap.put(dictItem.getId(), dictItem.getLabel());
        }
        
        // 6. 组装返回结果
        List<CustomerTagResp> result = new ArrayList<>();
        for (CustomerTagDO tag : tags) {
            CustomerTagResp resp = new CustomerTagResp();
            resp.setTagId(tag.getTagId());
            resp.setTagName(tagNameMap.getOrDefault(tag.getTagId(), ""));
            resp.setCode(tag.getCode());
            result.add(resp);
        }
        
        return result;
    }

    @Override
    public Map<Long, List<CustomerTagResp>> batchGetCustomerTagList(List<Long> customerIds) {
        if (CollUtil.isEmpty(customerIds)) {
            return new HashMap<>();
        }
        
        // 1. 查询客户标签
        List<CustomerTagDO> tags = customerTagMapper.selectList(
            new LambdaQueryWrapper<CustomerTagDO>()
                .in(CustomerTagDO::getCustomerId, customerIds)
        );
        
        if (CollUtil.isEmpty(tags)) {
            return customerIds.stream().collect(Collectors.toMap(id -> id, id -> new ArrayList<>()));
        }

        // 4. 获取所有标签ID
        List<Long> tagIds = tags.stream()
            .map(CustomerTagDO::getTagId)
            .collect(Collectors.toList());
        
        // 5. 查询标签名称
        Map<Long, String> tagNameMap = new HashMap<>();
        List<DictItemDO> dictItems = dictItemMapper.selectList(
            new LambdaQueryWrapper<DictItemDO>()
                .in(DictItemDO::getId, tagIds)
        );
        
        for (DictItemDO dictItem : dictItems) {
            tagNameMap.put(dictItem.getId(), dictItem.getLabel());
        }
        
        // 6. 按客户ID分组
        Map<Long, List<CustomerTagDO>> customerTagMap = tags.stream()
            .collect(Collectors.groupingBy(CustomerTagDO::getCustomerId));
        
        // 7. 组装返回结果
        Map<Long, List<CustomerTagResp>> result = new HashMap<>();
        for (Long customerId : customerIds) {
            List<CustomerTagDO> customerTags = customerTagMap.getOrDefault(customerId, new ArrayList<>());
            List<CustomerTagResp> tagRespList = new ArrayList<>();
            
            for (CustomerTagDO tag : customerTags) {
                CustomerTagResp resp = new CustomerTagResp();
                resp.setTagId(tag.getTagId());
                resp.setTagName(tagNameMap.getOrDefault(tag.getTagId(), ""));
                resp.setCode(tag.getCode());
                tagRespList.add(resp);
            }
            
            result.put(customerId, tagRespList);
        }
        
        // 确保所有客户ID都有对应的列表，即使是空列表
        for (Long customerId : customerIds) {
            if (!result.containsKey(customerId)) {
                result.put(customerId, new ArrayList<>());
            }
        }
        
        return result;
    }
}