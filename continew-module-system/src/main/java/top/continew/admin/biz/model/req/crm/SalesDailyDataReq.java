package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改商务每日数据参数
 *
 * <AUTHOR>
 * @since 2025/07/11 14:30
 */
@Data
@Schema(description = "创建或修改商务每日数据参数")
public class SalesDailyDataReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录添加日期，默认为当前日期
     */
    @Schema(description = "记录添加日期，默认为当前日期")
    @NotNull(message = "添加日期不能为空")
    private LocalDate recordDate;

    /**
     * 添加方式，例如：被动，主动
     */
    @Schema(description = "添加方式，例如：被动，主动")
    @NotNull(message = "添加方式不能为空")
    private Integer addMethod;

    /**
     * 客户账号类型，例如：微信，tg
     */
    @Schema(description = "客户账号类型，例如：微信，tg")
    @NotNull(message = "客户账号类型不能为空")
    private Integer accountType;

    /**
     * 客户的微信号或tg id
     */
    @Schema(description = "客户的微信号或tg id")
    @NotBlank(message = "客户账号不能为空")
    @Length(max = 200, message = "客户账号的长度不能超过 {max} 个字符")
    private String customerAccount;

    private String customerName;

    @NotNull(message = "关联账号不能为空")
    private Long socialAccountId;

    /**
     * 客户的大致业务范围或描述
     */
    @Schema(description = "客户业务")
    @Length(max = 255, message = "客户业务长度不能超过 {max} 个字符")
    private String customerBusiness;

    /**
     * 客户所在的城市
     */
    @Schema(description = "客户所在的城市")
    @Length(max = 50, message = "客户城市长度不能超过 {max} 个字符")
    private String customerCity;

    /**
     * 对客户的初步情况进行描述
     */
    @Schema(description = "对客户的初步情况进行描述")
    @Length(max = 5000, message = "客户初步情况长度不能超过 {max} 个字符")
    private String customerOverview;

    /**
     * 记录与客户首次沟通的内容和结果
     */
    @Schema(description = "记录与客户首次沟通的内容和结果")
    @Length(max = 5000, message = "首次沟通内容长度不能超过 {max} 个字符")
    @NotBlank(message = "首次沟通内容不能为空")
    private String firstContactNotes;
}