package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.CustomerWalletDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.CustomerWalletQuery;
import top.continew.admin.biz.model.req.CustomerWalletReq;
import top.continew.admin.biz.model.resp.CustomerWalletDetailResp;
import top.continew.admin.biz.model.resp.CustomerWalletResp;

/**
 * 客户钱包业务接口
 *
 * <AUTHOR>
 * @since 2025/07/22 16:58
 */
public interface CustomerWalletService extends BaseService<CustomerWalletResp, CustomerWalletDetailResp, CustomerWalletQuery, CustomerWalletReq>, IService<CustomerWalletDO> {}