package top.continew.admin.biz.service.crm;

import top.continew.admin.biz.model.req.crm.SocialAccountReq;
import top.continew.admin.biz.model.req.crm.SocialAccountUpdateStatusReq;
import top.continew.admin.biz.model.req.crm.SocialAccountUpdateAssigneeReq;
import top.continew.admin.biz.model.resp.crm.SocialAccountDetailResp;
import top.continew.admin.biz.model.resp.crm.SocialAccountResp;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.crm.SocialAccountQuery;

/**
 * 社交账号业务接口
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
public interface SocialAccountService extends BaseService<SocialAccountResp, SocialAccountDetailResp, SocialAccountQuery, SocialAccountReq> {

    /**
     * 批量修改账号状态
     *
     * @param req 请求参数
     */
    void batchUpdateStatus(SocialAccountUpdateStatusReq req);

    /**
     * 批量修改分配人
     *
     * @param req 请求参数
     */
    void batchUpdateAssignee(SocialAccountUpdateAssigneeReq req);
}