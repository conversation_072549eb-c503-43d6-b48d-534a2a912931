/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户余额变更记录查询条件
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
@Data
@Schema(description = "客户余额变更记录查询条件")
public class CustomerBalanceRecordQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 交易动作
     */
    @Schema(description = "交易动作")
    @Query(type = QueryType.EQ)
    private Integer action;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @Query(type = QueryType.EQ)
    private Integer type;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] transTime;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @Query(type = QueryType.EQ)
    private String platformAdId;
}