/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * fb账号查询条件
 *
 * <AUTHOR>
 * @since 2025/01/03 16:32
 */
@Data
@Schema(description = "fb账号查询条件")
public class FbAccountQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联账号ID
     */
    @Schema(description = "关联账号ID")
    @Query(type = QueryType.EQ)
    private String platformAccountId;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    @Query(type = QueryType.IN)
    private String[] browserSerialNo;

    /**
     * 状态（1=正常，2=封禁）
     */
    @Schema(description = "状态（1=正常，2=封禁）")
    @Query(type = QueryType.EQ)
    private FbAccountStatusEnum status;

    @Schema(description = "渠道")
    @Query(type = QueryType.EQ)
    private Long channelId;

    @Schema(description = "标签")
    @Query(type = QueryType.LIKE)
    private String tag;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;


    @Query(type = QueryType.IN, columns = "id")
    private String[] ids;

    @Query(type = QueryType.LIKE)
    private String customRemark;

    @QueryIgnore
    private Boolean hasPassword;
}