/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改IP库参数
 *
 * <AUTHOR>
 * @since 2025/01/20 13:46
 */
@Data
@Schema(description = "创建或修改IP库参数")
public class IpReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ip地址
     */
    @Schema(description = "ip地址")
    @NotBlank(message = "ip地址不能为空")
    @Length(max = 64, message = "ip地址长度不能超过 {max} 个字符")
    private String host;

    /**
     * 端口
     */
    @Schema(description = "端口")
    @NotBlank(message = "端口不能为空")
    @Length(max = 64, message = "端口长度不能超过 {max} 个字符")
    private String port;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    @Length(max = 64, message = "用户名长度不能超过 {max} 个字符")
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    @Length(max = 64, message = "密码长度不能超过 {max} 个字符")
    private String password;

    @Schema(description = "国家")
    @NotBlank(message = "国家不能为空")
    @Length(max = 64, message = "国家长度不能超过 {max} 个字符")
    private String country;

    private Integer expireDay;

    private LocalDateTime expireTime;
}