package top.continew.admin.biz.model.req.crm;

import java.io.Serial;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 关闭客户回访任务参数
 *
 * <AUTHOR>
 * @since 2025/01/15 10:00
 */
@Data
@Schema(description = "关闭客户回访任务参数")
public class CustomerVisitTaskCloseReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关闭原因
     */
    @Schema(description = "关闭原因")
    @NotBlank(message = "关闭原因不能为空")
    @Length(max = 200, message = "关闭原因长度不能超过 {max} 个字符")
    private String closeReason;
}