/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import top.continew.admin.biz.enums.CardPlatformEnum;

@Data
@Schema(description = "新增广告户银行卡参数")
public class AdAccountAddCardReq {

    @Schema(description = "广告ID")
    @NotBlank(message = "广告ID不能为空")
    private String adAccountId;

    @NotNull(message = "卡台不能为空")
    private CardPlatformEnum platform;

    @Schema(description = "银行卡号")
    @NotNull(message = "银行卡号不能为空")
    private String cardNo;

}
