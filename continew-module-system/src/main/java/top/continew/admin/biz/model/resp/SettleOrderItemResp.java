package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 结算订单详情信息
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@Schema(description = "结算订单详情信息")
public class SettleOrderItemResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    private String platformAdId;

    /**
     * 消耗
     */
    @Schema(description = "消耗")
    private BigDecimal spent;
}