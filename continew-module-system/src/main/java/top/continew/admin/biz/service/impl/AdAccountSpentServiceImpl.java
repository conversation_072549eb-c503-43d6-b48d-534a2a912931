package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.mapper.AdAccountOrderMapper;
import top.continew.admin.biz.mapper.analyze.AdAccountSpentAnalyzeMapper;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.query.AdAccountSpentQuery;
import top.continew.admin.biz.model.query.CustomerAnalyzeQuery;
import top.continew.admin.biz.model.resp.CustomerSpentResp;
import top.continew.admin.biz.model.resp.CustomerSpentStatisticsResp;
import top.continew.admin.biz.service.AdAccountSpentService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdAccountSpentServiceImpl implements AdAccountSpentService {


    private final AdAccountOrderMapper adAccountOrderMapper;

    private final AdAccountSpentAnalyzeMapper adAccountSpentAnalyzeMapper;

    @Override
    public BasePageResp<CustomerSpentResp> pageByCustomer(AdAccountSpentQuery query, PageQuery pageQuery) {

        // 查询出 客户名称	总下户数的分页数据
        IPage<CustomerSpentResp> page = adAccountSpentAnalyzeMapper.selectPageCustomerBase(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query);

        for (CustomerSpentResp record : page.getRecords()) {
            Long customerId = record.getCustomerId();
            record.setAdUsageCount(adAccountSpentAnalyzeMapper.selectAdUsageCount(customerId, null, query.getStartTime(), query.getEndTime()));
            // 查询出 不消耗-低消耗-正常消耗
            CustomerSpentStatisticsResp spentResp = adAccountSpentAnalyzeMapper.selectAllSpentCount(customerId, null, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), query.getStartTime(), query.getEndTime());
            record.setNonSpendingCount(spentResp.getNonSpendingCount());
            record.setLowSpendingCount(spentResp.getLowSpendingCount());
            record.setNormalSpendingCount(spentResp.getNormalSpendingCount());
        }

        return new PageResp<>(page.getRecords(), page.getTotal());
    }


    @Override
    public CustomerSpentStatisticsResp getCustomerDashboardStats(AdAccountSpentQuery query) {
        // 查询汇总数据
        return adAccountSpentAnalyzeMapper.getCustomerDashboardStats(query, query.getLowConsumptionDay(), query.getLowConsumptionAmount());
    }

    @Override
    public List<CustomerSpentResp> analyzeByCustomer(CustomerAnalyzeQuery query) {
        List<CustomerSpentResp> list = new ArrayList<>();
        Long customerId = query.getCustomerId();

        // 卡头
        if (CollUtil.isNotEmpty(query.getCardHeader())) {
            query.getCardHeader().forEach(cardHeader -> {
                CustomerSpentResp resp = new CustomerSpentResp();
                resp.setCustomerName(cardHeader);

                List<String> adAccountIds = adAccountSpentAnalyzeMapper.selectAdIdByCardHeader(customerId,
                        null, cardHeader, query.getStartTime(), query.getEndTime());
                resp.setTotalSubAccounts(adAccountIds.size());
                if (CollUtil.isEmpty(adAccountIds)) {
                    resp.setNonSpendingCount(0);
                    resp.setLowSpendingCount(0);
                    resp.setNormalSpendingCount(0);
                    resp.setAdUsageCount(0);
                    list.add(resp);
                } else {
                    CustomerSpentStatisticsResp analysisStats = adAccountSpentAnalyzeMapper.selectAllSpentCount(customerId, adAccountIds, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), query.getStartTime(), query.getEndTime());
                    resp.setNonSpendingCount(analysisStats.getNonSpendingCount());
                    resp.setLowSpendingCount(analysisStats.getLowSpendingCount());
                    resp.setNormalSpendingCount(analysisStats.getNormalSpendingCount());
                    // 广告使用数
                    Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(customerId, adAccountIds, query.getStartTime(), query.getEndTime());
                    resp.setAdUsageCount(count);
                    list.add(resp);
                }
            });
        }
        // 时区条件
        if (CollUtil.isNotEmpty(query.getTimeZone())) {
            query.getTimeZone().forEach(timeZone -> {
                List<String> adAccountIds = adAccountSpentAnalyzeMapper.selectAdIdByTimeZone(customerId, null, timeZone, query.getStartTime(), query.getEndTime());
                CustomerSpentResp resp = new CustomerSpentResp();
                resp.setCustomerName(timeZone);
                resp.setTotalSubAccounts(adAccountIds.size());
                if (CollUtil.isEmpty(adAccountIds)) {
                    resp.setNonSpendingCount(0);
                    resp.setLowSpendingCount(0);
                    resp.setNormalSpendingCount(0);
                    resp.setAdUsageCount(0);
                    list.add(resp);
                } else {
                    CustomerSpentStatisticsResp analysisStats = adAccountSpentAnalyzeMapper.selectAllSpentCount(customerId, adAccountIds, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), query.getStartTime(), query.getEndTime());
                    resp.setNonSpendingCount(analysisStats.getNonSpendingCount());
                    resp.setLowSpendingCount(analysisStats.getLowSpendingCount());
                    resp.setNormalSpendingCount(analysisStats.getNormalSpendingCount());
                    Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(customerId, adAccountIds, query.getStartTime(), query.getEndTime());
                    resp.setAdUsageCount(count);
                    list.add(resp);
                }
            });
        }
        // 一刀流
        if (query.getOneKnifeFlow()) {
            List<String> adAccountIds = adAccountSpentAnalyzeMapper.selectAdIdByOneDollar(customerId, null, query.getStartTime(), query.getEndTime());
            CustomerSpentResp resp = new CustomerSpentResp();
            resp.setCustomerName("一刀流");
            resp.setTotalSubAccounts(adAccountIds.size());
            if (CollUtil.isEmpty(adAccountIds)) {
                resp.setNonSpendingCount(0);
                resp.setLowSpendingCount(0);
                resp.setNormalSpendingCount(0);
                resp.setAdUsageCount(0);
                list.add(resp);
            } else {
                CustomerSpentStatisticsResp analysisStats = adAccountSpentAnalyzeMapper.selectAllSpentCount(customerId, adAccountIds, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), query.getStartTime(), query.getEndTime());
                resp.setNonSpendingCount(analysisStats.getNonSpendingCount());
                resp.setLowSpendingCount(analysisStats.getLowSpendingCount());
                resp.setNormalSpendingCount(analysisStats.getNormalSpendingCount());
                Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(customerId, adAccountIds, query.getStartTime(), query.getEndTime());
                resp.setAdUsageCount(count);
                list.add(resp);
            }
        }

        // 支付问题
        if (query.getPaymentIssue()) {
            // 该客户下所有的支付问题的广告户ID
            List<String> failAdAccountIds = adAccountSpentAnalyzeMapper.selectAdIdByFailTrans(query.getCustomerId(), null, query.getStartTime(), query.getEndTime());

            CustomerSpentResp resp = new CustomerSpentResp();
            resp.setCustomerName("支付问题");
            resp.setTotalSubAccounts(failAdAccountIds.size());
            if (CollUtil.isEmpty(failAdAccountIds)) {
                resp.setNonSpendingCount(0);
                resp.setNormalSpendingCount(0);
                resp.setAdUsageCount(0);
                resp.setLowSpendingCount(0);
            } else {
                CustomerSpentStatisticsResp analysisStatsByCustomer = adAccountSpentAnalyzeMapper.selectAllSpentCount(customerId, failAdAccountIds,
                        query.getLowConsumptionDay(), query.getLowConsumptionAmount(), query.getStartTime(), query.getEndTime());
                resp.setNonSpendingCount(analysisStatsByCustomer.getNonSpendingCount());
                resp.setLowSpendingCount(analysisStatsByCustomer.getLowSpendingCount());
                resp.setNormalSpendingCount(analysisStatsByCustomer.getNormalSpendingCount());
                Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(customerId, failAdAccountIds, query.getStartTime(), query.getEndTime());
                resp.setAdUsageCount(count);
            }
            list.add(resp);
        }

        return list;
    }

    @Override
    public BasePageResp<CustomerSpentResp> pageByAdAccount(AdAccountSpentQuery query, PageQuery pageQuery) {
        CheckUtils.throwIfBlank(query.getAdAccountIds(), "请输入广告户");
        String[] adAccountIds = query.getAdAccountIdsAsArray();

        CustomerSpentResp customerSpentResp = new CustomerSpentResp();
        List<AdAccountOrderDO> list = adAccountSpentAnalyzeMapper.selectLatestAdAccountOrdersByAdIds(Arrays.asList(adAccountIds));

        if (CollUtil.isEmpty(list)) {
            customerSpentResp.setTotalSubAccounts(0);
            customerSpentResp.setNonSpendingCount(0);
            customerSpentResp.setNormalSpendingCount(0);
            customerSpentResp.setAdUsageCount(0);
            customerSpentResp.setLowSpendingCount(0);
        } else {
            List<String> adAccountList = list.stream().map(AdAccountOrderDO::getAdAccountId).toList();
            customerSpentResp.setTotalSubAccounts(list.size());
            CustomerSpentStatisticsResp analysisStatsByCustomer = adAccountSpentAnalyzeMapper.selectAllSpentCount(null, adAccountList, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), null, null);
            customerSpentResp.setNonSpendingCount(analysisStatsByCustomer.getNonSpendingCount());
            customerSpentResp.setLowSpendingCount(analysisStatsByCustomer.getLowSpendingCount());
            customerSpentResp.setNormalSpendingCount(analysisStatsByCustomer.getNormalSpendingCount());
            Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(null, adAccountList, null, null);
            customerSpentResp.setAdUsageCount(count);
        }

        return new PageResp<>(List.of(customerSpentResp), 1);
    }

    @Override
    public CustomerSpentStatisticsResp getAdAccountDashboardStats(AdAccountSpentQuery query) {
        CheckUtils.throwIfBlank(query.getAdAccountIds(), "请输入广告户");
        String[] adAccountIds = query.getAdAccountIdsAsArray();

        // 查下户订单
        List<AdAccountOrderDO> list = adAccountSpentAnalyzeMapper.selectLatestAdAccountOrdersByAdIds(Arrays.asList(adAccountIds));

        CustomerSpentStatisticsResp resp = new CustomerSpentStatisticsResp();
        if (CollUtil.isEmpty(list)) {
            resp.setTotalSubAccounts(0);
            resp.setNonSpendingCount(0);
            resp.setNormalSpendingCount(0);
            resp.setAdUsageCount(0);
            resp.setLowSpendingCount(0);
        } else {
            List<LocalDateTime> timeList = list.stream().map(AdAccountOrderDO::getFinishTime).toList();
            if (CollUtil.isEmpty(timeList)) {
                resp.setTotalSubAccounts(0);
                resp.setNonSpendingCount(0);
                resp.setNormalSpendingCount(0);
                resp.setAdUsageCount(0);
                resp.setLowSpendingCount(0);
                return resp;
            }

            LocalDateTime[] adjustedTimeRange = CommonUtils.getAdjustedTimeRange(timeList);

            // 找出时间范围内所有的下户订单
            List<AdAccountOrderDO> adAccountOrderList = adAccountOrderMapper.lambdaQuery()
                    .between(AdAccountOrderDO::getFinishTime, adjustedTimeRange[0], adjustedTimeRange[1])
                    .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.RECYCLE)
                    .list();

            resp.setTotalSubAccounts(adAccountOrderList.size());
            List<String> adAccounts = adAccountOrderList.stream().map(AdAccountOrderDO::getAdAccountId).toList();

            CustomerSpentStatisticsResp analysisStatsByCustomer = adAccountSpentAnalyzeMapper.selectAllSpentCount(null, adAccounts, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), adjustedTimeRange[0], adjustedTimeRange[1]);
            resp.setNonSpendingCount(analysisStatsByCustomer.getNonSpendingCount());
            resp.setLowSpendingCount(analysisStatsByCustomer.getLowSpendingCount());
            resp.setNormalSpendingCount(analysisStatsByCustomer.getNormalSpendingCount());

            Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(null, adAccounts, adjustedTimeRange[0], adjustedTimeRange[1]);
            resp.setAdUsageCount(count);
        }
        return resp;
    }


    @Override
    public List<CustomerSpentResp> analyzeByAdAccount(CustomerAnalyzeQuery query) {
        CheckUtils.throwIfBlank(query.getAdAccountIds(), "请输入广告户");
        List<CustomerSpentResp> list = new ArrayList<>();
        String[] adAccountIds = query.getAdAccountIds().split("[\\s,，]+");

        // 卡头
        if (CollUtil.isNotEmpty(query.getCardHeader())) {
            query.getCardHeader().forEach(cardHeader -> {
                CustomerSpentResp resp = new CustomerSpentResp();

                resp.setCustomerName(cardHeader);

                List<String> allAdAccountIds = adAccountSpentAnalyzeMapper.selectAdIdByCardHeader(null, Arrays.asList(adAccountIds), cardHeader, null, null);

                resp.setTotalSubAccounts(allAdAccountIds.size());
                if (CollUtil.isEmpty(allAdAccountIds)) {
                    resp.setNonSpendingCount(0);
                    resp.setLowSpendingCount(0);
                    resp.setNormalSpendingCount(0);
                    resp.setAdUsageCount(0);
                    list.add(resp);
                } else {
                    CustomerSpentStatisticsResp analysisStats = adAccountSpentAnalyzeMapper.selectAllSpentCount(null, allAdAccountIds, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), null, null);
                    resp.setNonSpendingCount(analysisStats.getNonSpendingCount());
                    resp.setLowSpendingCount(analysisStats.getLowSpendingCount());
                    resp.setNormalSpendingCount(analysisStats.getNormalSpendingCount());
                    Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(null, allAdAccountIds, query.getStartTime(), query.getEndTime());
                    resp.setAdUsageCount(count);
                    list.add(resp);
                }

            });
        }
        // 时区条件
        if (CollUtil.isNotEmpty(query.getTimeZone())) {
            query.getTimeZone().forEach(timeZone -> {
                List<String> timeZoneAdAccountIds = adAccountSpentAnalyzeMapper.selectAdIdByTimeZone(null, Arrays.asList(adAccountIds), timeZone, null, null);

                CustomerSpentResp resp = new CustomerSpentResp();
                resp.setCustomerName(timeZone);
                resp.setTotalSubAccounts(timeZoneAdAccountIds.size());
                if (CollUtil.isEmpty(timeZoneAdAccountIds)) {
                    resp.setNonSpendingCount(0);
                    resp.setLowSpendingCount(0);
                    resp.setNormalSpendingCount(0);
                    resp.setAdUsageCount(0);
                    list.add(resp);
                } else {
                    CustomerSpentStatisticsResp analysisStats = adAccountSpentAnalyzeMapper.selectAllSpentCount(null, timeZoneAdAccountIds, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), null, null);
                    resp.setNonSpendingCount(analysisStats.getNonSpendingCount());
                    resp.setLowSpendingCount(analysisStats.getLowSpendingCount());
                    resp.setNormalSpendingCount(analysisStats.getNormalSpendingCount());
                    Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(null, timeZoneAdAccountIds, null, null);
                    resp.setAdUsageCount(count);
                    list.add(resp);
                }
            });
        }
        // 一刀流
        if (query.getOneKnifeFlow()) {
            List<String> oneDollarList = adAccountSpentAnalyzeMapper.selectAdIdByOneDollar(null, Arrays.asList(adAccountIds), null, null);
            CustomerSpentResp resp = new CustomerSpentResp();
            resp.setCustomerName("一刀流");
            resp.setTotalSubAccounts(oneDollarList.size());
            if (CollUtil.isEmpty(oneDollarList)) {
                resp.setNonSpendingCount(0);
                resp.setLowSpendingCount(0);
                resp.setNormalSpendingCount(0);
                resp.setAdUsageCount(0);
                list.add(resp);
            } else {
                CustomerSpentStatisticsResp analysisStats = adAccountSpentAnalyzeMapper.selectAllSpentCount(null, oneDollarList, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), null, null);
                resp.setNonSpendingCount(analysisStats.getNonSpendingCount());
                resp.setLowSpendingCount(analysisStats.getLowSpendingCount());
                resp.setNormalSpendingCount(analysisStats.getNormalSpendingCount());
                Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(null, oneDollarList, null, null);
                resp.setAdUsageCount(count);
                list.add(resp);
            }
        }
        // 支付问题
        if (query.getPaymentIssue()) {
            List<String> failAdAccountIds = adAccountSpentAnalyzeMapper.selectAdIdByFailTrans(null, Arrays.asList(adAccountIds), null, null);
            CustomerSpentResp resp = new CustomerSpentResp();
            resp.setCustomerName("支付问题");
            resp.setTotalSubAccounts(failAdAccountIds.size());
            if (CollUtil.isEmpty(failAdAccountIds)) {
                resp.setNonSpendingCount(0);
                resp.setNormalSpendingCount(0);
                resp.setAdUsageCount(0);
                resp.setLowSpendingCount(0);
            } else {
                CustomerSpentStatisticsResp analysisStatsByCustomer = adAccountSpentAnalyzeMapper.selectAllSpentCount(null, failAdAccountIds, query.getLowConsumptionDay(), query.getLowConsumptionAmount(), null, null);
                resp.setNonSpendingCount(analysisStatsByCustomer.getNonSpendingCount());
                resp.setLowSpendingCount(analysisStatsByCustomer.getLowSpendingCount());
                resp.setNormalSpendingCount(analysisStatsByCustomer.getNormalSpendingCount());
                Integer count = adAccountSpentAnalyzeMapper.selectAdUsageCount(null, failAdAccountIds, null, null);
                resp.setAdUsageCount(count);
            }
            list.add(resp);
        }

        return list;
    }

    @Override
    public LocalDateTime[] timeInterval(String adAccountIds) {
        CheckUtils.throwIfBlank(adAccountIds, "请输入广告户ID");
        List<AdAccountOrderDO> list = adAccountOrderMapper.lambdaQuery()
                .in(AdAccountOrderDO::getAdAccountId, adAccountIds.split("[\\s,，]+"))
                .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.RECYCLE)
                .list();
        Map<String, LocalDateTime> latestCreateTimeByAccountId = list.stream().collect(
                Collectors.toMap(
                        AdAccountOrderDO::getAdAccountId,
                        AdAccountOrderDO::getCreateTime,
                        (existingTime, newItemTime) -> newItemTime.isAfter(existingTime) ? newItemTime : existingTime
                )
        );
        List<LocalDateTime> timeList = latestCreateTimeByAccountId.values().stream().toList();
        if (CollUtil.isEmpty(timeList)) {
            return new LocalDateTime[0];
        }
        return CommonUtils.getAdjustedTimeRange(timeList);
    }


}
