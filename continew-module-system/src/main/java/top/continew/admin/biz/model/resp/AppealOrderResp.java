/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.fastjson2.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 申诉订单信息
 *
 * <AUTHOR>
 * @since 2025/01/16 11:24
 */
@Data
@Schema(description = "申诉订单信息")
public class AppealOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 客户
     */
    @Schema(description = "客户")
    private Long customerId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    private String platformAdId;

    /**
     * 申诉状态
     */
    @Schema(description = "申诉状态")
    private Integer status;

    /**
     * 卡台状态
     */
    @Schema(description = "卡台状态")
    private Integer cardStatus;

    /**
     * 卡台金额
     */
    private BigDecimal cardBalance;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private Long handleUser;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime finishTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    private String handleUserName;

    private String customerName;

    private String browserNo;

    private String bmId;

    private BusinessManagerStatusEnum bmStatus;

    private AdAccountClearStatusEnum clearStatus;

    /**
     * 卡台清零结果
     */
    private String cardClearResult;

    private List<AdAccountCardClearResultResp> cardClearResultList;

    public List<AdAccountCardClearResultResp> getCardClearResultList() {
        if (StringUtils.isBlank(cardClearResult)) {
            return new ArrayList<>();
        }
        return JSONArray.parseArray(cardClearResult, AdAccountCardClearResultResp.class);
    }
}