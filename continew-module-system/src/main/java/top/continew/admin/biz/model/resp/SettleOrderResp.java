package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseResp;

/**
 * 结算订单信息
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@Schema(description = "结算订单信息")
public class SettleOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @AssembleMethod(props = @Mapping(src = "name", ref = "customerName"), targetType = CustomerService.class, method = @ContainerMethod(bindMethod = "get", resultType = CustomerResp.class))
    private Long customerId;

    private String customerName;

    /**
     * 总消耗
     */
    @Schema(description = "总消耗")
    private BigDecimal totalSpent;

    /**
     * 结算消耗
     */
    @Schema(description = "结算消耗")
    private BigDecimal settleSpent;

    /**
     * 结算时间
     */
    @Schema(description = "结算时间")
    private LocalDateTime settleTime;
}