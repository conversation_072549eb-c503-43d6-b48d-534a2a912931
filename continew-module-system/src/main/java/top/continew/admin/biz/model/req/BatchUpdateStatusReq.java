/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量更新状态请求
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Data
@Schema(description = "批量更新状态请求")
public class BatchUpdateStatusReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID列表
     */
    @NotEmpty(message = "ID列表不能为空")
    @Schema(description = "ID列表")
    private List<Long> ids;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 终止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime terminateTime;
}