package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 登记回访结果参数
 *
 * <AUTHOR>
 * @since 2025/01/15 10:00
 */
@Data
@Schema(description = "登记回访结果参数")
public class CustomerVisitTaskUpdateReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 回访方式
     */
    @Schema(description = "回访方式")
    @NotBlank(message = "回访方式不能为空")
    @Length(max = 50, message = "回访方式长度不能超过 {max} 个字符")
    private String visitMethod;

    /**
     * 回访时间
     */
    @Schema(description = "回访时间")
    @NotNull(message = "回访时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime visitTime;

    /**
     * 回访纪要
     */
    @Schema(description = "回访纪要")
    @NotBlank(message = "回访纪要不能为空")
    @Length(max = 1000, message = "回访纪要长度不能超过 {max} 个字符")
    private String visitSummary;

    /**
     * 附件URLs（聊天记录）
     */
    @Schema(description = "附件URLs")
    @Length(max = 2000, message = "附件URLs长度不能超过 {max} 个字符")
    private String attachmentUrls;
}