/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.AdAccountBalanceTypeEnum;
import top.continew.admin.biz.enums.TransactionActionEnum;
import top.continew.admin.common.base.BaseDetailResp;

/**
 * 广告户余额变更记录详情信息
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "广告户余额变更记录详情信息")
public class AdAccountBalanceRecordDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "关联广告户")
    private String platformAdId;

    /**
     * 交易动作
     */
    @Schema(description = "交易动作")
    @ExcelProperty(value = "交易动作")
    private TransactionActionEnum action;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @ExcelProperty(value = "交易类型")
    private AdAccountBalanceTypeEnum type;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    @ExcelProperty(value = "交易金额")
    private BigDecimal amount;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @ExcelProperty(value = "交易时间")
    private LocalDateTime transTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
}