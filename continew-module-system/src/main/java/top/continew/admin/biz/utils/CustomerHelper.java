package top.continew.admin.biz.utils;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.mapper.CustomerMapper;

import java.math.BigDecimal;

@Component
public class CustomerHelper implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        CustomerHelper.applicationContext = context;
    }

    private static CustomerMapper getCustomerMapper() {
        return applicationContext.getBean(CustomerMapper.class);
    }

    public static void addBalance(Long customerId, BigDecimal amount) {
        LambdaUpdateWrapper<CustomerDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerDO::getId, customerId)
                .setSql("balance = balance + " + amount);
        getCustomerMapper().update(null, updateWrapper);
    }

    public static void reduceBalance(Long customerId, BigDecimal amount) {
        LambdaUpdateWrapper<CustomerDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerDO::getId, customerId)
                .setSql("balance = balance - " + amount);
        getCustomerMapper().update(null, updateWrapper);
    }

}