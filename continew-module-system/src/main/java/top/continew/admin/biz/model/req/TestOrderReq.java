package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改测试任务参数
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@Schema(description = "创建或修改测试任务参数")
public class TestOrderReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 测试标题
     */
    @Schema(description = "测试标题")
    @NotBlank(message = "测试标题不能为空")
    @Length(max = 100, message = "测试标题长度不能超过 {max} 个字符")
    private String title;

    /**
     * trello链接
     */
    @Schema(description = "trello链接")
    @NotBlank(message = "trello链接不能为空")
    @Length(max = 100, message = "trello链接长度不能超过 {max} 个字符")
    private String trelloUrl;


    private String adAccountIds;
}