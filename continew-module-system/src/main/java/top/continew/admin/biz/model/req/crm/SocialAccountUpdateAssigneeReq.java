package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量更新分配人请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@Schema(description = "批量更新分配人请求")
public class SocialAccountUpdateAssigneeReq {

    @NotEmpty(message = "ID列表不能为空")
    @Schema(description = "ID列表")
    private List<Long> ids;

    @NotNull(message = "分配人ID不能为空")
    @Schema(description = "分配人ID")
    private Long assigneeId;
}