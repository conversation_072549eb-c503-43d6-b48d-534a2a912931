package top.continew.admin.biz.mapper.crm;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.resp.BusinessPerformanceStatisticsResp;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商务业绩统计 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/16
 */
public interface SalesPerformanceStatMapper extends BaseMapper<Object> {

    /**
     * 统计商务业绩数据
     *
     * @param salesUserIds 商务人员ID列表
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @return 商务业绩统计列表
     */
    List<SalesPerformanceStatResp> selectSalesPerformanceStat(@Param("salesUserIds") List<Long> salesUserIds,
                                                              @Param("startDate") LocalDateTime startDate,
                                                              @Param("endDate") LocalDateTime endDate);

    /**
     * 试用期统计
     *
     * @param userList    用户列表
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @param workingDays 工作日数量
     * @return 商务业绩统计列表
     */
    List<BusinessPerformanceStatisticsResp> selectTrialStat(@Param("userList") List<UserDO> userList, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("workingDays") Integer workingDays);

    /**
     * 试岗期统计
     *
     * @param userList 用户列表
     * @return 商务业绩统计列表
     */
    List<BusinessPerformanceStatisticsResp> selectProbationStat(@Param("userList") List<UserDO> userList);

    /**
     * 正式期统计
     *
     * @param userList    用户列表
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @param workingDays 工作日数量
     * @return 商务业绩统计列表
     */
    List<BusinessPerformanceStatisticsResp> selectFormalStat(@Param("userList") List<UserDO> userList, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("workingDays") int workingDays);
}