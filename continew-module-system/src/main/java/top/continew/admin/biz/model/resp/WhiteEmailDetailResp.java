package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 白名单邮箱详情信息
 *
 * <AUTHOR>
 * @since 2025/07/01 14:36
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "白名单邮箱详情信息")
public class WhiteEmailDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    @ExcelProperty(value = "邮箱")
    private String email;
}