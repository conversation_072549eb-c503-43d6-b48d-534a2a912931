/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.CustomerTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客户周期对比响应
 *
 * <AUTHOR>
 * @since 2024/12/31 10:00
 */
@Data
@Schema(description = "客户周期对比响应")
public class CustomerCycleComparePeroidResp {

    /**
     * 周期标识（如：2024-01）
     */
    @Schema(description = "周期标识")
    private String period;

    /**
     * 下户数
     */
    @Schema(description = "下户数")
    private Integer totalAccounts = 0;

    /**
     * 正常户数
     */
    @Schema(description = "正常户数")
    private Integer normalAccounts = 0;

    /**
     * 死户数
     */
    @Schema(description = "死户数")
    private Integer deadAccounts = 0;

    /**
     * 空置户数
     */
    @Schema(description = "空置户数")
    private Integer idleAccounts = 0;

    /**
     * 回收户数
     */
    @Schema(description = "回收户数")
    private Integer recycledAccounts = 0;

    /**
     * 总消耗
     */
    @Schema(description = "总消耗")
    private BigDecimal totalSpent = BigDecimal.ZERO;

    /**
     * 消耗区间户数分布
     */
    @Schema(description = "消耗区间户数分布")
    private Map<String, Integer> spentRangeDistribution;


}