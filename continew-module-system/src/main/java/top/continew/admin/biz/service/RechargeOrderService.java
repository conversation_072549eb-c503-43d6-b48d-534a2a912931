/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.RechargeOrderDO;
import top.continew.admin.biz.model.query.RechargeOrderQuery;
import top.continew.admin.biz.model.req.RechargeOrderByCustomerReq;
import top.continew.admin.biz.model.req.RechargeOrderFinishReq;
import top.continew.admin.biz.model.req.RechargeOrderRechargeReq;
import top.continew.admin.biz.model.req.RechargeOrderReq;
import top.continew.admin.biz.model.resp.RechargeOrderDetailResp;
import top.continew.admin.biz.model.resp.RechargeOrderResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;

/**
 * 充值订单业务接口
 *
 * <AUTHOR>
 * @since 2024/12/30 17:09
 */
public interface RechargeOrderService extends BaseService<RechargeOrderResp, RechargeOrderDetailResp, RechargeOrderQuery, RechargeOrderReq>, IService<RechargeOrderDO> {

    /**
     * 处理订单
     *
     * @param id
     */
    void handleOrder(Long id);

    /**
     * 完成订单
     *
     * @param req
     */
    void finishOrder(RechargeOrderFinishReq req);

    /**
     * 取消订单
     *
     * @param id
     */
    void cancelOrder(Long id);

    /**
     * 充值订单
     *
     * @param req
     */
    void recharge(RechargeOrderRechargeReq req);

    /**
     * 获取广告户未完成的充值订单
     *
     * @param platformAdId
     */
    RechargeOrderDO getUnFinishOrder(String platformAdId);

    /**
     * 检测fb限额修改是否正确
     *
     * @param platformAdId
     * @param newLimitAmount
     * @param oldLimitAmount
     * @param fbOpsId
     */
    void checkModifyLimit(String platformAdId, BigDecimal newLimitAmount, BigDecimal oldLimitAmount, String fbOpsId);

    /**
     * 生成凭证图片
     *
     * @param id
     */
    void generateCertificate(Long id);

    /**
     * 上传修改限额凭证
     *
     * @param platformAdId
     * @param fbOpsId
     * @param imageBase64
     */
    void uploadModifyLimitCertificate(String platformAdId, String fbOpsId, String imageBase64);

    /**
     * 充值申请-客户端
     *
     * @param req
     */
    void rechargeApply(RechargeOrderByCustomerReq req);
}