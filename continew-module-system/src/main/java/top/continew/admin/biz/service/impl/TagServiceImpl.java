package top.continew.admin.biz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import top.continew.admin.biz.model.entity.TagRelationDO;
import top.continew.admin.biz.model.req.DeleteTagReq;
import top.continew.admin.biz.service.TagRelationService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.TagMapper;
import top.continew.admin.biz.model.entity.TagDO;
import top.continew.admin.biz.model.query.TagQuery;
import top.continew.admin.biz.model.req.TagReq;
import top.continew.admin.biz.model.resp.TagDetailResp;
import top.continew.admin.biz.model.resp.TagResp;
import top.continew.admin.biz.service.TagService;

/**
 * 标签业务实现
 *
 * <AUTHOR>
 * @since 2025/05/07 18:02
 */
@Service
@RequiredArgsConstructor
public class TagServiceImpl extends BaseServiceImpl<TagMapper, TagDO, TagResp, TagDetailResp, TagQuery, TagReq> implements TagService {

    private final TagRelationService tagRelationService;

    @Override
    public Long add(TagReq req) {

        if (StringUtils.isBlank(req.getName())) {
            return null;
        }

        TagDO tagDO = getOne(new LambdaQueryWrapper<TagDO>()
                .eq(TagDO::getName, req.getName()));
        if (tagDO == null) {
            tagDO = new TagDO();
            tagDO.setName(req.getName());
            save(tagDO);
        }

        if (!tagRelationService.exists(new LambdaQueryWrapper<TagRelationDO>()
                .eq(TagRelationDO::getRelationId, req.getId())
                .eq(TagRelationDO::getTagId, tagDO.getId()))) {
            TagRelationDO tagRelationDO = new TagRelationDO();
            tagRelationDO.setTagId(tagDO.getId());
            tagRelationDO.setRelationId(req.getId());
            tagRelationDO.setType(req.getType());
            tagRelationService.save(tagRelationDO);
        }
        return tagDO.getId();
    }

    @Override
    public void deleteTag(DeleteTagReq req) {
        tagRelationService.remove(new LambdaQueryWrapper<TagRelationDO>()
                .eq(TagRelationDO::getRelationId, req.getUserId())
                .eq(TagRelationDO::getTagId, req.getTagId()));
    }
}