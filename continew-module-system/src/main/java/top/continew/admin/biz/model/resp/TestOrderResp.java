package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 测试任务信息
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@Schema(description = "测试任务信息")
public class TestOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 测试标题
     */
    @Schema(description = "测试标题")
    private String title;

    /**
     * trello链接
     */
    @Schema(description = "trello链接")
    private String trelloUrl;

    /**
     * 状态(1=待进行，2=进行中，3=已完成）
     */
    @Schema(description = "状态(1=待进行，2=进行中，3=已完成）")
    private Integer status;
}