package top.continew.admin.biz.model.resp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CustomerStatSummaryResp {

    /**
     * 打款金额
     */
    private BigDecimal transferAmount;

    private BigDecimal refundAmount;

    /**
     * 充值手续费
     */
    private BigDecimal fee;

    private BigDecimal adAccountBuyAmount;

    private BigDecimal totalRecharge;

    /**
     * 取款金额
     */
    private BigDecimal withdrawAmount;

    /**
     * 广告户充值金额
     */
    private BigDecimal adAccountRechargeAmount;

    private BigDecimal cardSpent;

    private BigDecimal totalBalance;
}
