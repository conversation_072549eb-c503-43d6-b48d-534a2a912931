package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
public class AdAccountTestOrderReq {

    @NotNull
    private Integer type;

    @Schema(description = "测试标题")
    private String title;

    /**
     * trello链接
     */
    @Schema(description = "trello链接")
    private String trelloUrl;

    @NotEmpty
    private List<Long> adAccountIds;

    private Long testOrderId;
}
