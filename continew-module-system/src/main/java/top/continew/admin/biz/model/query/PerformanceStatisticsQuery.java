package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.system.enums.JobRankEnum;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class
PerformanceStatisticsQuery {

    @Schema(description = "商务人员ID列表")
    private List<Long> salesIds;

    @Schema(description = "开始时间", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime startDate;

    @Schema(description = "结束时间", example = "2025-01-31 23:59:59")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime endDate;

    @Schema(description = "职级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "职级")
    private JobRankEnum jobRank;

}
