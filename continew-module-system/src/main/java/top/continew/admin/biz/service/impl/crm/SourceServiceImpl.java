package top.continew.admin.biz.service.impl.crm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.crm.SourceMapper;
import top.continew.admin.biz.mapper.crm.SourceGroupMapper;
import top.continew.admin.biz.model.entity.crm.SourceDO;
import top.continew.admin.biz.model.entity.crm.SourceGroupDO;
import top.continew.admin.biz.model.query.crm.SourceQuery;
import top.continew.admin.biz.model.req.crm.SourceGroupReq;
import top.continew.admin.biz.model.req.crm.SourceReq;
import top.continew.admin.biz.model.resp.crm.SourceDetailResp;
import top.continew.admin.biz.model.resp.crm.SourceGroupResp;
import top.continew.admin.biz.model.resp.crm.SourceResp;
import top.continew.admin.biz.service.crm.SourceService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SourceServiceImpl extends BaseServiceImpl<SourceMapper, SourceDO, SourceResp, SourceDetailResp, SourceQuery, SourceReq> implements SourceService {
    
    private final SourceGroupMapper sourceGroupMapper;

    @Override
    public PageResp<SourceResp> page(SourceQuery query, PageQuery pageQuery) {
        PageResp<SourceResp> page = super.page(query, pageQuery);

        // 填充分组名称
        List<SourceResp> records = page.getList();
        if (records != null && !records.isEmpty()) {
            records.forEach(source -> {
                SourceGroupDO group = sourceGroupMapper.selectById(source.getGroupId());
                if (group != null) {
                    source.setGroupName(group.getName());
                }
            });
        }
        return page;
    }

    @Override
    protected void beforeAdd(SourceReq req) {
        // 检查来源名称是否存在
        if (baseMapper.exists(new LambdaQueryWrapper<SourceDO>().eq(SourceDO::getName, req.getName()))) {
            throw new BusinessException("来源名称已存在");
        }

    }



    @Override
    protected void beforeUpdate(SourceReq req, Long id) {
        // 检查来源名称是否存在
        if (baseMapper.exists(new LambdaQueryWrapper<SourceDO>().eq(SourceDO::getName, req.getName()).ne(SourceDO::getId, id))) {
            throw new BusinessException("来源名称已存在");
        }
    }

    @Override
    public SourceDetailResp get(Long id) {
        SourceDetailResp detail = super.get(id);
        if (detail!= null) {
            SourceGroupDO group = sourceGroupMapper.selectById(detail.getGroupId());
            detail.setGroupName(group.getName());
        }
        return detail;
    }

    @Override
    public List<SourceGroupResp> listGroup() {
        return sourceGroupMapper.selectList(null).stream()
            .map(group -> {
                SourceGroupResp resp = new SourceGroupResp();
                resp.setId(group.getId());
                resp.setName(group.getName());
                return resp;
            })
            .collect(Collectors.toList());
    }
    
    @Override
    public void addGroup(SourceGroupReq req) {
        // 检查分组名称是否已存在
        if (sourceGroupMapper.exists(new LambdaQueryWrapper<SourceGroupDO>().eq(SourceGroupDO::getName, req.getName()))) {
            throw new BusinessException("分组名称已存在");
        }

        SourceGroupDO group = new SourceGroupDO();
        group.setName(req.getName());
        sourceGroupMapper.insert(group);
    }
    
    @Override
    public void updateGroup(Long id, SourceGroupReq req) {
        // 检查分组名称是否已存在
        if (sourceGroupMapper.exists(new LambdaQueryWrapper<SourceGroupDO>()
            .eq(SourceGroupDO::getName, req.getName())
            .ne(SourceGroupDO::getId, id))) {
            throw new BusinessException("分组名称已存在");
        }

        SourceGroupDO group = new SourceGroupDO();
        group.setId(id);
        group.setName(req.getName());
        sourceGroupMapper.updateById(group);
    }
    
    @Override
    public void deleteGroup(Long id) {
        // 检查是否有来源关联此分组
        if (baseMapper.exists(new LambdaQueryWrapper<SourceDO>().eq(SourceDO::getGroupId, id))) {
            throw new BusinessException("该分组下存在来源，不能删除");
        }
        sourceGroupMapper.deleteById(id);
    }
}