package top.continew.admin.biz.model.query;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class AdAccountOrderStatisticsQuery {

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * BM类型
     */
    private Integer bmType;

    /**
     * BM渠道ID
     */
    private Long bmChannelId;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 下户时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] payTime;

}