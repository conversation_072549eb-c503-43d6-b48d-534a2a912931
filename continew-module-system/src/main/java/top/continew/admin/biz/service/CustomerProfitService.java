/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.query.CustomerProfitStatQuery;
import top.continew.admin.biz.model.resp.CustomerProfitStatResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.List;

/**
 * 客户业务接口
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
public interface CustomerProfitService {

    /**
     * 查询客户盈利分析
     *
     * @param query
     * @param pageQuery
     * @return
     */
    PageResp<CustomerProfitStatResp> selectCustomerProfitStatPage(CustomerProfitStatQuery query, PageQuery pageQuery);

    /**
     * 查询客户盈利分析
     *
     * @param query
     * @param sortQuery
     * @return
     */
    List<CustomerProfitStatResp> selectCustomerProfitStatList(CustomerProfitStatQuery query, SortQuery sortQuery);

}