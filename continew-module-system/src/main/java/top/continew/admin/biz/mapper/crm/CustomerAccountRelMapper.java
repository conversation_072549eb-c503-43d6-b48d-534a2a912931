package top.continew.admin.biz.mapper.crm;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.crm.CustomerAccountRelDO;
import top.continew.admin.biz.model.resp.crm.CustomerAccountResp;

import java.util.List;

/**
 * 客户账号信息关联 Mapper
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
public interface CustomerAccountRelMapper extends BaseMapper<CustomerAccountRelDO> {

    /**
     * 根据账号ID和实体类型、实体ID查询关联关系
     *
     * @param accountId 账号ID
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 关联关系
     */
    CustomerAccountRelDO selectByAccountIdAndEntityTypeAndEntityId(@Param("accountId") Long accountId, 
                                                                  @Param("entityType") Integer entityType, 
                                                                  @Param("entityId") Long entityId);

    /**
     * 将指定实体的其他关联关系设置为非主要
     *
     * @param accountId 当前账号ID
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 更新数量
     */
    int updateOtherRelNotPrimary(@Param("accountId") Long accountId, 
                                @Param("entityType") Integer entityType, 
                                @Param("entityId") Long entityId);

    /**
     * 根据账号ID统计关联数量
     *
     * @param accountId 账号ID
     * @return 关联数量
     */
    int countByAccountId(@Param("accountId") Long accountId);

    /**
     * 根据实体类型和实体ID查询账号信息
     *
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 账号信息列表
     */
    List<CustomerAccountResp> selectAccountsByEntityTypeAndEntityId(@Param("entityType") Integer entityType,
                                                                    @Param("entityId") Long entityId);
}