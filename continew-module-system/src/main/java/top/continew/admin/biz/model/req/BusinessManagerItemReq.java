package top.continew.admin.biz.model.req;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 创建或修改bm坑位参数
 *
 * <AUTHOR>
 * @since 2025/03/11 11:52
 */
@Data
@Schema(description = "创建或修改bm坑位参数")
public class BusinessManagerItemReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "BM不能为空")
    private Long businessManagerId;

    /**
     * 名称（坑位1）
     */
    @Schema(description = "名称（坑位1）")
    @NotBlank(message = "名称（坑位1）不能为空")
    @Length(max = 64, message = "名称（坑位1）长度不能超过 {max} 个字符")
    private String name;

    /**
     * 单价
     */
    @Schema(description = "单价")
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    /**
     * 状态（1=正常，2=封禁）
     */
    @Schema(description = "状态（1=正常，2=封禁）")
    @NotNull(message = "状态（1=正常，2=封禁）不能为空")
    private BusinessManagerStatusEnum status;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    private String platformAdId;

    /**
     * 是否使用
     */
    @Schema(description = "是否使用")
    @NotNull(message = "是否使用不能为空")
    private Boolean isUse;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime banTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useTime;

    @NotNull(message = "类型不能为空")
    private BusinessManagerTypeEnum type;

    @NotNull(message = "渠道不能为空")
    private Long channelId;

    @NotNull(message = "权限不能为空")
    private Integer ownMethod;

    private String remark;

    @NotNull(message = "是否补号不能为空")
    private Boolean isBu;
}