/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.AdAccountKeepStatusEnum;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改广告账号参数
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
@Data
@Schema(description = "创建或修改广告账号参数")
public class AdAccountReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 时区
     */
    @Schema(description = "时区")
    @Length(max = 64, message = "时区长度不能超过 {max} 个字符")
    private String timezone;
    /**
     * 出售状态
     */
    @Schema(description = "出售状态")
    @NotNull(message = "出售状态不能为空")
    private AdAccountSaleStatusEnum saleStatus;
    /**
     * 清零状态
     */
    @Schema(description = "清零状态")
    @NotNull(message = "清零状态不能为空")
    private AdAccountClearStatusEnum clearStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;

    private Boolean isRemoveAdmin;

    private Boolean isLowLimit;

    private Integer voStatus;

}