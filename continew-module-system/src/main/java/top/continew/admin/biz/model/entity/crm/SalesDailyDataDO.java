package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;
import java.time.*;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 商务每日数据实体
 *
 * <AUTHOR>
 * @since 2025/07/11 14:30
 */
@Data
@TableName("biz_sales_daily_data")
public class SalesDailyDataDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 记录添加日期，默认为当前日期
     */
    private LocalDate recordDate;

    /**
     * 添加方式，例如：被动，主动
     */
    private Integer addMethod;

    /**
     * 客户账号类型，例如：微信，tg
     */
    private Integer accountType;

    /**
     * 客户的微信号或tg id
     */
    private String customerAccount;

    private Long socialAccountId;

    private String customerName;

    /**
     * 客户的大致业务范围或描述
     */
    private String customerBusiness;

    /**
     * 客户所在的城市
     */
    private String customerCity;

    /**
     * 对客户的初步情况进行描述
     */
    private String customerOverview;

    /**
     * 记录与客户首次沟通的内容和结果
     */
    private String firstContactNotes;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT)
    private Long createUser;
}