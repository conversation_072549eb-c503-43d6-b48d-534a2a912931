/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

/**
 * Facebook 广告生效状态枚举
 */
@Getter
@RequiredArgsConstructor
public enum FbAdEffectiveStatusEnum implements BaseEnum<String> {
    /*
    • ACTIVE：投放中。广告正在正常投放。
    • PAUSED：已暂停。广告暂时停止投放，但可以随时恢复。
    • PENDING_REVIEW：待审核。广告已提交，正在等待 Facebook 审核。
    • CREDIT_CARD_NEEDED：需要信用卡。广告账户需要添加或更新信用卡信息才能继续操作。
    • PREAPPROVED：预先批准。广告已获得初步批准，但可能还需进一步审核。
    • DISABLED：已禁用。广告因违反政策或其他原因被禁用，无法投放。
    • PENDING_PROCESS：待处理。广告正在处理相关操作，尚未完成。
    • WITH_ISSUES：存在问题。广告存在某些问题，需要修正后才能继续投放。
     */
    ACTIVE("ACTIVE", "投放中"),
    PAUSED("PAUSED", "已关闭"),
    //有数据
    ADSET_PAUSED("ADSET_PAUSED", "广告组已关闭"),
    //有数据
    IN_PROCESS("IN_PROCESS", "准备中"),
    //有查到数据
    PENDING_REVIEW("PENDING_REVIEW", "审核中"),
    //有数据
    WITH_ISSUES("WITH_ISSUES", "账号已停用"),
    //有数据，原因可能是已被拒、投放错误、无效资产等
    DISAPPROVED("DISAPPROVED", "未投放"),
    //有数据
    CAMPAIGN_PAUSED("CAMPAIGN_PAUSED", "广告系列已关闭"),
    //无数据
    CREDIT_CARD_NEEDED("CREDIT_CARD_NEEDED", "需要信用卡"),
    //无数据
    PREAPPROVED("PREAPPROVED", "预先批准"),
    //无数据
    DISABLED("DISABLED", "已禁用"),
    //无数据
    PENDING_PROCESS("PENDING_PROCESS", "待处理"),
    //无数据
    DELETED("DELETED", "已删除"),
    //无数据
    PENDING_BILLING_INFO("PENDING_BILLING_INFO", "等待账单信息"),
    //无数据
    ARCHIVED("ARCHIVED", "已归档")
  ;

    ;

    private final String value;
    private final String description;
}