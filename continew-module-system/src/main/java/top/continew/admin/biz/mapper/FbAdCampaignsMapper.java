/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.FbAdCampaignsDO;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Facebook 广告系列 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/16 10:00
 */
public interface FbAdCampaignsMapper extends BaseMapper<FbAdCampaignsDO> {

    /**
     * 根据广告户ID查询广告系列列表
     *
     * @param adAccountId 广告户ID
     * @return 广告系列列表
     */
    List<FbAdCampaignsDO> selectByAdAccountId(@Param("adAccountId") String adAccountId);

    /**
     * 根据平台ID查询广告系列
     *
     * @param platformId 平台ID
     * @return 广告系列
     */
    FbAdCampaignsDO selectByPlatformId(@Param("platformId") String platformId);

    /**
     * 根据状态查询广告系列列表
     *
     * @param status 状态
     * @return 广告系列列表
     */
    List<FbAdCampaignsDO> selectByStatus(@Param("status") String status);


    /**
     * 根据投放状态查询广告系列列表
     *
     * @param status 状态
     * @return 广告系列列表
     */
    List<FbAdCampaignsDO> selectByDeliveryStatus(@Param("status") String status);

    /**
     * 批量插入或更新广告系列
     * 如果platform_id已存在则更新，否则插入
     *
     * @param campaigns 广告系列列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("campaigns") List<FbAdCampaignsDO> campaigns);

    /**
     * 批量插入广告系列（忽略重复）
     * 如果platform_id已存在则忽略
     *
     * @param campaigns 广告系列列表
     * @return 影响行数
     */
    int batchInsertIgnore(@Param("campaigns") List<FbAdCampaignsDO> campaigns);
    

    /**
     * 根据广告账户ID和更新时间删除不匹配的广告系列
     *
     * @param adAccountId 广告账户ID
     * @param updateTime  更新时间
     * @return 删除的行数
     */
    int deleteByAdAccountIdAndUpdateTimeNot(@Param("adAccountId") String adAccountId, 
                                           @Param("updateTime") LocalDateTime updateTime);
}