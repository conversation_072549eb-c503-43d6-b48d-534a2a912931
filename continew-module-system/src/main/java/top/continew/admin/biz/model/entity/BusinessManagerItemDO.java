package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * bm坑位实体
 *
 * <AUTHOR>
 * @since 2025/03/11 11:52
 */
@Data
@TableName("biz_business_manager_item")
public class BusinessManagerItemDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * bm 主键ID
     */
    private Long businessManagerId;

    /**
     * 名称（坑位1）
     */
    private String name;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 状态（1=正常，2=封禁）
     */
    private BusinessManagerStatusEnum status;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 是否使用
     */
    private Boolean isUse;

    /**
     * 使用时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime useTime;

    /**
     * 封禁时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime banTime;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private BusinessManagerTypeEnum type;

    private Long channelId;

    private Integer ownMethod;

    private String remark;

    private Boolean isBu;
}