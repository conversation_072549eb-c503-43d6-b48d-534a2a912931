package top.continew.admin.biz.service.impl.crm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/6/15 16:26
 */
@Getter
@AllArgsConstructor
public enum CustomerVisitStrategyConditionType {

    // 基础条件
    CREATE_TIME_RANGE("createTimeRange", "创建时间", "筛选在指定时间范围内创建的客户", true),
    REFUND_CUSTOMER("refundCustomer", "退款客户", "筛选退款的客户", true),
    SALES_TAGS("salesTags", "销售标签", "根据客户的销售标签进行筛选", true),

    // 高级条件
    AD_ACCOUNT_COUNT("adAccountCount", "下户数量", "筛选名下拥有特定数量广告账户的客户", false),
    NORMAL_ACCOUNT_COUNT("normalAccountCount", "正常户数量", "筛选名下拥有特定数量正常广告账户的客户", false),
    EMPTY_ACCOUNT_COUNT("emptyAccountCount", "空置户数量", "筛选名下拥有特定数量空置广告账户的客户", false),
    CONSUMPTION_AMOUNT("consumptionAmount", "总消耗额", "筛选广告消耗总额在指定区间的客户", false),
    CONSUMPTION_TREND("consumptionTrend", "消耗趋势", "筛选消耗呈下降趋势的客户", false),
    SINGLE_ACCOUNT_CONSUMPTION_TREND("singleAccountConsumptionTrend", "单户消耗趋势", "筛选单个广告账户消耗呈下降趋势的客户", false);

    /**
     * 前端使用的条件类型代码（驼峰命名）
     */
    private final String frontendCode;

    /**
     * 条件标题
     */
    private final String title;

    /**
     * 条件描述
     */
    private final String description;

    /**
     * 是否为基础条件
     */
    private final boolean isBasicCondition;

    /**
     * 根据前端代码获取枚举
     */
    public static CustomerVisitStrategyConditionType getByFrontendCode(String frontendCode) {
        for (CustomerVisitStrategyConditionType type : values()) {
            if (type.getFrontendCode().equals(frontendCode)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取所有基础条件类型的前端代码
     */
    public static String[] getBasicConditionFrontendCodes() {
        return java.util.Arrays.stream(values())
                .filter(CustomerVisitStrategyConditionType::isBasicCondition)
                .map(CustomerVisitStrategyConditionType::getFrontendCode)
                .toArray(String[]::new);
    }

    /**
     * 获取所有高级条件类型
     */
    public static CustomerVisitStrategyConditionType[] getAdvancedConditions() {
        return java.util.Arrays.stream(values())
                .filter(type -> !type.isBasicCondition())
                .toArray(CustomerVisitStrategyConditionType[]::new);
    }
}
