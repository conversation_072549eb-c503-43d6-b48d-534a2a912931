package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@Schema(description = "消耗统计请求")
public class SpendStatisticsReq {
    
    @Schema(description = "完成时间-开始")
    @NotNull(message = "完成时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTimeStart;
    
    @Schema(description = "完成时间-结束")
    @NotNull(message = "完成时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTimeEnd;
    
    @Schema(description = "统计时间-开始")
    @NotNull(message = "统计时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statisticsTimeStart;
    
    @Schema(description = "统计时间-结束")
    @NotNull(message = "统计时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statisticsTimeEnd;
}