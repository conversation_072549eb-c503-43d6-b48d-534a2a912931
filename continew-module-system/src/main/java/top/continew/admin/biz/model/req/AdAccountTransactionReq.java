package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改广告户交易记录参数
 *
 * <AUTHOR>
 * @since 2025/03/10 14:15
 */
@Data
@Schema(description = "创建或修改广告户交易记录参数")
public class AdAccountTransactionReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @NotBlank(message = "广告户ID不能为空")
    @Length(max = 64, message = "广告户ID长度不能超过 {max} 个字符")
    private String platformAdId;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @NotNull(message = "交易时间不能为空")
    private LocalDateTime transTime;

    /**
     * 交易ID
     */
    @Schema(description = "交易ID")
    @NotBlank(message = "交易ID不能为空")
    @Length(max = 64, message = "交易ID长度不能超过 {max} 个字符")
    private String transactionId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @NotBlank(message = "交易类型不能为空")
    @Length(max = 64, message = "交易类型长度不能超过 {max} 个字符")
    private String transactionType;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    @NotNull(message = "交易金额不能为空")
    private BigDecimal amount;

    /**
     * 交易状态
     */
    @Schema(description = "交易状态")
    @NotBlank(message = "交易状态不能为空")
    @Length(max = 64, message = "交易状态长度不能超过 {max} 个字符")
    private String status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;
}