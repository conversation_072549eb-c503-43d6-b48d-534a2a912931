package top.continew.admin.biz.model.resp.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * CRM工作台响应参数
 *
 * <AUTHOR>
 * @since 2025/01/XX
 */
@Data
@Schema(description = "CRM工作台响应参数")
public class CrmWorkbenchResp {

    @Schema(description = "线索待跟进数量")
    private Long leadPendingCount;

    @Schema(description = "线索今日需跟进数量")
    private Long leadTodayFollowCount;

    @Schema(description = "线索超时数量")
    private Long leadOverdueCount;

    @Schema(description = "商机待跟进数量")
    private Long opportunityPendingCount;

    @Schema(description = "商机今日需跟进数量")
    private Long opportunityTodayFollowCount;

    @Schema(description = "商机超时数量")
    private Long opportunityOverdueCount;


    @Schema(description = "回访任务今日需跟进数量")
    private Long visitTaskTodayFollowCount;

    @Schema(description = "回访任务超时数量")
    private Long visitTaskOverdueCount;

    @Schema(description = "是否展示线索数量")
    private Boolean showLeadCount = true;

    @Schema(description = "是否展示商机数量")
    private Boolean showOpportunityCount = true;

    @Schema(description = "是否展示回访任务数量")
    private Boolean showVisitTaskCount = true;
}