package top.continew.admin.biz.model.req;

import java.io.Serial;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改白名单邮箱参数
 *
 * <AUTHOR>
 * @since 2025/07/01 14:36
 */
@Data
@Schema(description = "创建或修改白名单邮箱参数")
public class WhiteEmailReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    @Length(max = 64, message = "邮箱长度不能超过 {max} 个字符")
    private String email;
}