package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;
import top.continew.admin.common.base.BaseResp;

/**
 * 中介返点详情信息
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "中介返点详情信息")
public class AgentRebateDetailResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联中介
     */
    @Schema(description = "关联中介")
    private Long agentId;

    @ExcelProperty(value = "关联商务")
    private String businessUserName;

    @ExcelProperty(value = "关联中介")
    private String agentName;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    private Long customerId;

    @ExcelProperty(value = "关联客户")
    private String customerName;

    /**
     * 结算月份
     */
    @Schema(description = "结算月份")
    @ExcelProperty(value = "结算月份")
    private String settleMonth;

    /**
     * 结算金额
     */
    @Schema(description = "结算金额")
    @ExcelProperty(value = "结算金额")
    private BigDecimal settleAmount;

    /**
     * 开户费
     */
    @Schema(description = "开户费")
    @ExcelProperty(value = "开户费")
    private BigDecimal accountOpenAmount;

    /**
     * 返点金额
     */
    @Schema(description = "返点金额")
    @ExcelProperty(value = "预计返点金额")
    private BigDecimal rebateAmount;

    @ExcelProperty(value = "实际返点金额")
    private BigDecimal actualRebateAmount;
    /**
     * 返点日期
     */
    @Schema(description = "返点日期")
    @ExcelProperty(value = "返点日期")
    private LocalDateTime rebateTime;

    /**
     * 返点规则
     */
    @Schema(description = "返点规则")
    @ExcelProperty(value = "返点规则")
    private String rebateRule;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
}