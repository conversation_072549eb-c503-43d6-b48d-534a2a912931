package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "广告户金额核对请求")
public class AdAccountCheckAmountReq {

    @NotNull(message = "广告户ID不能为空")
    @Schema(description = "广告户ID")
    private String platformAdId;

    @NotNull(message = "开始日期不能为空")
    @Schema(description = "开始日期")
    private String startDate;

    @NotNull(message = "结束日期不能为空")
    @Schema(description = "结束日期")
    private String endDate;
}