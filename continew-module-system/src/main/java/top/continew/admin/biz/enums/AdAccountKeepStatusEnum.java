/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum AdAccountKeepStatusEnum implements BaseEnum<Integer> {
    LOGIN_SUCCESS(1, "登号完成"), PROCESS(2, "养号中"), FINISH(3, "养号成功"), SUCCESS(4, "绑定BM5成功"), FAIL(5, "失败"),
    AUTHORIZED(6, "已授权"),
    OBSERVE(9, "待查看"), WAIT_SPENT(10, "待消耗");

    private final Integer value;
    private final String description;
}
